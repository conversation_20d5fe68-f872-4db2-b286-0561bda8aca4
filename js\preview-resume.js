/**
 * Resume Preview JavaScript
 * Handles all functionality for the resume preview page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const previewControls = document.querySelector('.preview-controls');
    const resumePreview = document.querySelector('.resume-preview');
    const zoomInBtn = document.querySelector('.zoom-in');
    const zoomOutBtn = document.querySelector('.zoom-out');
    const zoomLevelEl = document.querySelector('.zoom-level');
    const colorBtns = document.querySelectorAll('.color-btn');
    const fontBtns = document.querySelectorAll('.font-btn');
    const downloadBtn = document.querySelector('.download-btn');
    const printBtn = document.querySelector('.print-btn');
    const editBtn = document.querySelector('.edit-btn');
    const backBtn = document.querySelector('.back-btn');
    
    // Variables
    let zoomLevel = 100;
    const zoomStep = 25;
    const minZoom = 50;
    const maxZoom = 150;
    
    // Initialize
    init();
    
    /**
     * Initialize the preview page
     */
    function init() {
        // Set up event listeners
        setupEventListeners();
        
        // Add scroll effect to preview controls
        handleScroll();
        
        // Apply saved preferences if any
        applySavedPreferences();
    }
    
    /**
     * Set up all event listeners
     */
    function setupEventListeners() {
        // Zoom controls
        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', zoomIn);
        }
        
        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', zoomOut);
        }
        
        // Color scheme buttons
        if (colorBtns) {
            colorBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    changeColorScheme(this.dataset.color);
                });
            });
        }
        
        // Font family buttons
        if (fontBtns) {
            fontBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    changeFontFamily(this.dataset.font);
                });
            });
        }
        
        // Download button
        if (downloadBtn) {
            downloadBtn.addEventListener('click', downloadResume);
        }
        
        // Print button
        if (printBtn) {
            printBtn.addEventListener('click', printResume);
        }
        
        // Window scroll event
        window.addEventListener('scroll', handleScroll);
    }
    
    /**
     * Handle scroll effect for preview controls
     */
    function handleScroll() {
        if (window.scrollY > 10) {
            previewControls.classList.add('scrolled');
        } else {
            previewControls.classList.remove('scrolled');
        }
    }
    
    /**
     * Apply any saved preferences from localStorage
     */
    function applySavedPreferences() {
        // Apply color scheme
        const savedColor = localStorage.getItem('resumeColorScheme');
        if (savedColor) {
            changeColorScheme(savedColor);
            
            // Update active button
            const activeColorBtn = document.querySelector(`.color-btn[data-color="${savedColor}"]`);
            if (activeColorBtn) {
                document.querySelectorAll('.color-btn').forEach(btn => btn.classList.remove('active'));
                activeColorBtn.classList.add('active');
            }
        }
        
        // Apply font family
        const savedFont = localStorage.getItem('resumeFontFamily');
        if (savedFont) {
            changeFontFamily(savedFont);
            
            // Update active button
            const activeFontBtn = document.querySelector(`.font-btn[data-font="${savedFont}"]`);
            if (activeFontBtn) {
                document.querySelectorAll('.font-btn').forEach(btn => btn.classList.remove('active'));
                activeFontBtn.classList.add('active');
            }
        }
        
        // Apply zoom level
        const savedZoom = localStorage.getItem('resumeZoomLevel');
        if (savedZoom) {
            zoomLevel = parseInt(savedZoom);
            updateZoom();
        }
    }
    
    /**
     * Zoom in the resume preview
     */
    function zoomIn() {
        if (zoomLevel < maxZoom) {
            zoomLevel += zoomStep;
            updateZoom();
            saveZoomLevel();
        }
    }
    
    /**
     * Zoom out the resume preview
     */
    function zoomOut() {
        if (zoomLevel > minZoom) {
            zoomLevel -= zoomStep;
            updateZoom();
            saveZoomLevel();
        }
    }
    
    /**
     * Update the zoom level display and apply zoom
     */
    function updateZoom() {
        // Update zoom level display
        if (zoomLevelEl) {
            zoomLevelEl.textContent = `${zoomLevel}%`;
        }
        
        // Apply zoom to resume preview
        if (resumePreview) {
            resumePreview.style.transform = `scale(${zoomLevel / 100})`;
            resumePreview.style.transformOrigin = 'top center';
            
            // Remove all scale classes
            resumePreview.classList.remove('scale-50', 'scale-75', 'scale-100', 'scale-125', 'scale-150');
            
            // Add appropriate scale class
            resumePreview.classList.add(`scale-${zoomLevel}`);
        }
    }
    
    /**
     * Save zoom level to localStorage
     */
    function saveZoomLevel() {
        localStorage.setItem('resumeZoomLevel', zoomLevel.toString());
    }
    
    /**
     * Change the color scheme of the resume
     * @param {string} color - The color scheme to apply
     */
    function changeColorScheme(color) {
        if (resumePreview) {
            // Remove all color classes
            resumePreview.classList.remove('blue', 'green', 'purple', 'red', 'orange', 'teal');
            
            // Add the selected color class
            resumePreview.classList.add(color);
            
            // Save preference
            localStorage.setItem('resumeColorScheme', color);
            
            // Update active button
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.color === color) {
                    btn.classList.add('active');
                }
            });
        }
    }
    
    /**
     * Change the font family of the resume
     * @param {string} font - The font family to apply
     */
    function changeFontFamily(font) {
        if (resumePreview) {
            // Remove all font classes
            resumePreview.classList.remove('serif', 'sans-serif', 'monospace');
            
            // Add the selected font class
            resumePreview.classList.add(font);
            
            // Save preference
            localStorage.setItem('resumeFontFamily', font);
            
            // Update active button
            document.querySelectorAll('.font-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.font === font) {
                    btn.classList.add('active');
                }
            });
        }
    }
    
    /**
     * Download the resume as PDF
     */
    function downloadResume() {
        // Show loading indicator
        showLoadingIndicator();
        
        // Get resume ID from the page
        const resumeId = document.querySelector('[data-resume-id]')?.dataset.resumeId;
        
        if (!resumeId) {
            showError('Resume ID not found');
            return;
        }
        
        // Make AJAX request to generate PDF
        fetch(`download-resume.php?id=${resumeId}&format=pdf`, {
            method: 'GET',
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            // Create a URL for the blob
            const url = window.URL.createObjectURL(blob);
            
            // Create a temporary link and click it to download
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `resume_${resumeId}.pdf`;
            document.body.appendChild(a);
            a.click();
            
            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            // Hide loading indicator
            hideLoadingIndicator();
            
            // Show success message
            showSuccess('Resume downloaded successfully');
        })
        .catch(error => {
            console.error('Error downloading resume:', error);
            hideLoadingIndicator();
            showError('Failed to download resume. Please try again.');
        });
    }
    
    /**
     * Print the resume
     */
    function printResume() {
        window.print();
    }
    
    /**
     * Show loading indicator
     */
    function showLoadingIndicator() {
        // Create loading overlay if it doesn't exist
        let loadingOverlay = document.querySelector('.loading-overlay');
        
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Processing...</span>
                </div>
            `;
            document.body.appendChild(loadingOverlay);
        }
        
        // Show the overlay
        loadingOverlay.style.display = 'flex';
    }
    
    /**
     * Hide loading indicator
     */
    function hideLoadingIndicator() {
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }
    
    /**
     * Show success message
     * @param {string} message - The success message to display
     */
    function showSuccess(message) {
        showNotification(message, 'success');
    }
    
    /**
     * Show error message
     * @param {string} message - The error message to display
     */
    function showError(message) {
        showNotification(message, 'error');
    }
    
    /**
     * Show notification
     * @param {string} message - The message to display
     * @param {string} type - The type of notification (success, error, info, warning)
     */
    function showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let notificationContainer = document.querySelector('.notification-container');
        
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.className = 'notification-container';
            document.body.appendChild(notificationContainer);
        }
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        // Set icon based on type
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        
        // Set content
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="notification-content">
                ${message}
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to container
        notificationContainer.appendChild(notification);
        
        // Add close button event
        notification.querySelector('.notification-close').addEventListener('click', function() {
            notification.classList.add('notification-hiding');
            setTimeout(() => {
                notificationContainer.removeChild(notification);
            }, 300);
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode === notificationContainer) {
                notification.classList.add('notification-hiding');
                setTimeout(() => {
                    if (notification.parentNode === notificationContainer) {
                        notificationContainer.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);
        
        // Animate in
        setTimeout(() => {
            notification.classList.add('notification-show');
        }, 10);
    }
});