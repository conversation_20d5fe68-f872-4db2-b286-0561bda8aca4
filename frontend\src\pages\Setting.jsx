import React, { useState, useEffect } from "react";
import { FiPlus, FiLogIn, FiSettings, FiFile } from "react-icons/fi";
import { FaRegFileAlt, FaRegFileWord } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../store/authStore";
import ProfileCard from "../components/atoms/ProfileCard";

const SIDEBAR_COLLAPSED_WIDTH = 72;
const SIDEBAR_EXPANDED_WIDTH = 256;

const Setting = () => {
  const navigate = useNavigate();
  const { user, isLoading, fetchCurrentUser, logout, updateUser } =
    useAuthStore();

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);
  const [editableUser, setEditableUser] = useState({ name: "", email: "" });
  const [theme, setTheme] = useState(localStorage.getItem("theme") || "light");

  useEffect(() => {
    const loadUser = async () => {
      await fetchCurrentUser();
      setHasCheckedAuth(true);
    };
    loadUser();
  }, []);

  useEffect(() => {
    if (user) {
      setEditableUser({ name: user.name, email: user.email });
    }
  }, [user]);

  useEffect(() => {
    if (hasCheckedAuth && !isLoading && !user) {
      navigate("/login");
    }
  }, [user, isLoading, hasCheckedAuth, navigate]);

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  const handleSave = async () => {
    await updateUser({ name: editableUser.name, email: editableUser.email });
    alert("Profile updated!");
  };

  const handleThemeChange = (newTheme) => {
    setTheme(newTheme);
    localStorage.setItem("theme", newTheme);
    alert(`Theme switched to ${newTheme}! Refresh to apply site-wide styles.`);
  };

  if (isLoading && !hasCheckedAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center text-gray-600">
        Loading...
      </div>
    );
  }

  const sidebarItems = [
    { icon: <FiPlus />, text: "New", action: () => navigate("/resume/new") },
    {
      icon: <FiFile />,
      text: "Dashboard",
      action: () => navigate("/dashboard"),
    },
    {
      icon: <FaRegFileAlt />,
      text: "Resumes",
      action: () => navigate("/resumes"),
    },
    {
      icon: <FaRegFileWord />,
      text: "Cover Letters",
      action: () => navigate("/cover-letters"),
    },
    {
      icon: <FiSettings />,
      text: "Settings",
      action: () => navigate("/dashboard/settings"),
    },
  ];

  return (
    <div className="min-h-screen flex bg-[#29354d] text-gray-800">
      <aside
        className="h-screen flex flex-col justify-between fixed z-20 transition-all duration-300"
        style={{
          width: sidebarOpen ? SIDEBAR_EXPANDED_WIDTH : SIDEBAR_COLLAPSED_WIDTH,
        }}
        onMouseEnter={() => setSidebarOpen(true)}
        onMouseLeave={() => setSidebarOpen(false)}
      >
        <div className="p-4 flex items-center justify-center h-20">
          {sidebarOpen ? (
            <h1 className="text-2xl font-bold whitespace-nowrap text-[#fcc250]">
              ResumeBuilder
            </h1>
          ) : (
            <span
              className="rounded-full w-12 h-12 flex items-center justify-center font-bold text-2xl"
              style={{
                background: "#fcc250",
                color: "#29354d",
              }}
            >
              R
            </span>
          )}
        </div>
        <nav className="flex-1">
          <ul className="space-y-2 mt-6">
            {sidebarItems.map((item, idx) => (
              <li key={idx}>
                <button
                  onClick={item.action}
                  className={`flex items-center w-full px-4 py-3 rounded-lg transition-colors text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d] ${
                    sidebarOpen ? "justify-start space-x-3" : "justify-center"
                  }`}
                >
                  {item.icon}
                  {sidebarOpen && (
                    <span className="whitespace-nowrap">{item.text}</span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t border-[#fcc250] flex justify-center">
          <button
            onClick={handleLogout}
            className={`flex items-center w-full px-4 py-2 rounded-lg transition-colors text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d] ${
              sidebarOpen ? "justify-center space-x-2" : "justify-center"
            }`}
          >
            <FiLogIn />
            {sidebarOpen && <span className="whitespace-nowrap">Log out</span>}
          </button>
        </div>
      </aside>

      <main
        className="flex-1 transition-all duration-300"
        style={{
          marginLeft: sidebarOpen
            ? SIDEBAR_EXPANDED_WIDTH
            : SIDEBAR_COLLAPSED_WIDTH,
        }}
      >
        <div className="p-8 bg-gray-100 min-h-screen space-y-8">
          <h1 className="text-3xl font-bold">Account Settings</h1>

          <div className="bg-white rounded-lg p-6 flex flex-col sm:flex-row items-center space-x-4 space-y-4 sm:space-y-0 shadow">
            <ProfileCard
              name={editableUser.name}
              email={editableUser.email}
              avatarColor="#29354d"
              accentColor="#fcc250"
              size={72}
            />
            <div className="flex flex-col space-y-2 w-full">
              <input
                className="border rounded p-2"
                value={editableUser.name}
                onChange={(e) =>
                  setEditableUser({ ...editableUser, name: e.target.value })
                }
              />
              <input
                className="border rounded p-2"
                value={editableUser.email}
                onChange={(e) =>
                  setEditableUser({ ...editableUser, email: e.target.value })
                }
              />
              <div className="flex space-x-3 mt-2">
                <button
                  onClick={handleSave}
                  className="bg-[#29354d] text-[#fcc250] rounded-lg p-3 hover:bg-[#fcc250] hover:text-[#29354d]"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow space-y-3">
            <h2 className="text-xl font-bold">Theme Settings</h2>
            <div className="flex space-x-3">
              <button
                onClick={() => handleThemeChange("light")}
                className={`rounded-lg p-3 ${
                  theme === "light"
                    ? "bg-[#fcc250] text-[#29354d]"
                    : "bg-gray-100 hover:bg-gray-200"
                }`}
              >
                Light
              </button>
              <button
                onClick={() => handleThemeChange("dark")}
                className={`rounded-lg p-3 ${
                  theme === "dark"
                    ? "bg-[#fcc250] text-[#29354d] "
                    : "bg-gray-100 hover:bg-gray-200"
                }`}
              >
                Dark
              </button>
            </div>
            <p className="text-gray-600 text-sm">
              Theme setting applies site-wide after page refresh.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-bold">Additional Settings</h3>
            <div className="bg-white rounded-lg p-4 hover:shadow flex justify-between items-center">
              <span>Notification Preferences</span>
              <button
                onClick={() => alert("Notification settings coming soon!")}
                className="text-[#29354d] font-medium hover:underline"
              >
                Edit
              </button>
            </div>
            <div className="bg-white rounded-lg p-4 hover:shadow flex justify-between items-center">
              <span>Privacy & Security</span>
              <button
                onClick={() => alert("Privacy settings coming soon!")}
                className="text-[#29354d] font-medium hover:underline"
              >
                Edit
              </button>
            </div>
            <div className="bg-white rounded-lg p-4 hover:shadow flex justify-between items-center">
              <span>Delete Account</span>
              <button
                onClick={() => alert("Delete account coming soon!")}
                className="text-red-600 font-medium hover:underline"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Setting;
