import React from "react";
import {
  FaPalette,
  FaDownload,
  FaFont,
  FaFileAlt,
  FaFileCode,
  FaFileSignature,
  FaThLarge,
} from "react-icons/fa";

const menuItems = [
  { label: "Template", icon: <FaPalette /> },
  // { label: "Layout", icon: <FaThLarge /> },
  // { label: "Margin", icon: <FaFileAlt /> },
  { label: "Theme", icon: <FaFileCode /> },
  { label: "Typography", icon: <FaFont /> },
  { label: "Download", icon: <FaDownload /> },
  // { label: "Custom CSS", icon: <FaFileSignature /> },
];

const Menubar = ({ activeLabel, onSelect, collapsed }) => {
  return (
    <div className="flex flex-col py-4 h-full w-full bg-black text-white">
      {menuItems.map((item) => {
        const isActive = activeLabel === item.label;
        return (
          <button
            key={item.label}
            onClick={() => onSelect(item.label)}
            title={collapsed ? item.label : undefined}
            className={`flex items-center px-4 py-3 w-full text-left transition-all rounded-md
              ${
                isActive
                  ? "bg-gray-200 text-gray-600 font-semibold"
                  : "hover:bg-gray-500"
              }
            `}
          >
            <span className="text-lg">{item.icon}</span>
            {!collapsed && (
              <span className="ml-3 text-sm font-medium">{item.label}</span>
            )}
          </button>
        );
      })}
    </div>
  );
};

export default Menubar;
