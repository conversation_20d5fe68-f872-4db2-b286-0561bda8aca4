import React from "react";

const sampleResume = {
  basics: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    headline: "Software Engineer",
    location: "Bangalore, India",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    url: { href: "https://abhi.dev", label: "Portfolio" },
    customFields: [
      { id: "1", name: "LinkedIn", value: "https://linkedin.com/in/abhikumar" },
      { id: "2", name: "GitHub", value: "https://github.com/abhiCoder" },
    ],
  },
  sections: {
    summary: {
      visible: true,
      name: "Profile",
      content: "<p>Results-driven Software Engineer with 3+ years of experience in full-stack development. Skilled in React, Node.js, and cloud technologies.</p>",
      id: "summary",
    },
    experience: {
      visible: true,
      name: "Work Experience",
      id: "experience",
      items: [
        {
          id: "exp1",
          company: "InnoTech",
          position: "Frontend Developer",
          date: "2019 - 2022",
          location: "Remote",
          summary: '<ul><li>Developed React dashboard, integrated APIs, collaborated with backend team.</li><li>Improved performance and code quality.</li></ul>',
        },
      ],
    },
    education: {
      visible: true,
      name: "Education",
      id: "education",
      items: [
        {
          id: "edu1",
          institution: "NIT Trichy",
          studyType: "B.Tech",
          area: "Computer Science and Engineering",
          date: "2018 - 2022",
        },
        {
          id: "edu2",
          institution: "Some College",
          studyType: "Diploma",
          area: "Information Technology",
          date: "2015 - 2018",
        },
      ],
    },
    skills: {
      visible: true,
      name: "Skills",
      id: "skills",
      items: [
        { id: "sk1", name: "UI/UX" },
        { id: "sk2", name: "Visual Design" },
        { id: "sk3", name: "Wireframes" },
        { id: "sk4", name: "Storyboards" },
        { id: "sk5", name: "User Flows" },
        { id: "sk6", name: "Process Flows" },
      ],
    },
    languages: {
      visible: true,
      name: "Languages",
      id: "languages",
      items: [
        { id: "lang1", name: "English", level: "Fluent" },
        { id: "lang2", name: "Hindi", level: "Native" },
        { id: "lang3", name: "Kannada", level: "Conversational" },
      ],
    },
    volunteering: {
      visible: true,
      name: "Volunteering",
      id: "volunteering",
      items: [
        {
          id: "vol1",
          organization: "Code for Good",
          position: "Volunteer Developer",
          date: "2020 - 2021",
          summary: '<p>Contributed to open-source projects and mentored junior developers.</p>',
        },
      ],
    },
    references: {
      visible: true,
      name: "Reference",
      id: "references",
      items: [
        { id: "ref1", name: "John Doe", detail: "Senior Developer, InnoTech (<EMAIL>)" },
        { id: "ref2", name: "Jane Smith", detail: "Project Manager, WebStart (<EMAIL>)" },
      ],
    },
  },
};

const SectionTitle = ({ number, children }) => (
  <div className="flex items-center gap-3 mb-3 mt-6">
    <span className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-900 text-white font-bold text-sm">{number}</span>
    <span className="uppercase font-bold tracking-widest text-gray-900 text-sm">{children}</span>
  </div>
);

const Sidebar = ({ basics, skills, languages, references }) => (
  <aside className="w-1/3 max-w-xs bg-white p-8 flex flex-col gap-6 border-r border-gray-200">
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 leading-tight mb-1">{basics.name}</h1>
        <h2 className="text-base text-gray-500 font-medium">{basics.headline}</h2>
        <div className="border-b border-gray-300 my-4" />
      </div>
      
      <div className="mb-6">
        <SectionTitle number={1}>Contact</SectionTitle>
        <div className="text-sm text-gray-800 space-y-2">
          <div className="flex items-center gap-2">
            <span className="material-icons text-base">phone</span>
            <span>{basics.phone}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="material-icons text-base">email</span>
            <span>{basics.email}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="material-icons text-base">location_on</span>
            <span>{basics.location}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="material-icons text-base">language</span>
            <a href={basics.url.href} className="underline text-blue-700">{basics.url.label}</a>
          </div>
          {basics.customFields.map(field => (
            <div key={field.id} className="flex items-center gap-2">
              <span className="material-icons text-base">link</span>
              <a href={field.value} className="underline text-blue-700">{field.name}</a>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-6">
        <SectionTitle number={2}>Skills</SectionTitle>
        <ul className="list-disc list-inside text-sm text-gray-800 space-y-1">
          {skills.items.map(skill => (
            <li key={skill.id}>{skill.name}</li>
          ))}
        </ul>
      </div>
      
      <div className="mb-6">
        <SectionTitle number={3}>Languages</SectionTitle>
        <ul className="list-disc list-inside text-sm text-gray-800 space-y-1">
          {languages.items.map(lang => (
            <li key={lang.id}>{lang.name} <span className="text-gray-400">({lang.level})</span></li>
          ))}
        </ul>
      </div>
      
      {references?.items?.length > 0 && (
        <div className="mb-6">
          <SectionTitle number={4}>Reference</SectionTitle>
          <div className="text-sm text-gray-800">
            <div className="font-bold">{references.items[0].name}</div>
            <div>{references.items[0].detail}</div>
          </div>
        </div>
      )}
    </div>
  </aside>
);

const MainContent = ({ summary, experience, education, volunteering }) => (
  <main className="flex-1 bg-white p-10">
    <SectionTitle number={1}>Profile</SectionTitle>
    <div className="text-sm text-gray-700 mb-6 leading-relaxed" dangerouslySetInnerHTML={{ __html: summary.content }} />
    
    <div className="border-b border-gray-200 my-6" />
    
    <SectionTitle number={2}>Work Experience</SectionTitle>
    <div className="space-y-6 mb-6">
      {experience.items.map(exp => (
        <div key={exp.id}>
          <div className="flex justify-between items-center mb-2">
            <div className="font-bold text-gray-900 text-base">{exp.company}</div>
            <div className="text-sm text-gray-500 font-medium">{exp.date}</div>
          </div>
          <div className="font-semibold text-sm text-gray-800 mb-2">{exp.position}</div>
          <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: exp.summary }} />
        </div>
      ))}
    </div>
    
    <div className="border-b border-gray-200 my-6" />
    
    <SectionTitle number={3}>Education</SectionTitle>
    <div className="space-y-4 mb-6">
      {education.items.map(edu => (
        <div key={edu.id}>
          <div className="flex justify-between items-center mb-2">
            <div className="font-bold text-gray-900 text-sm">{edu.studyType}</div>
            <div className="text-sm text-gray-500 font-medium">{edu.date}</div>
          </div>
          <div className="text-sm text-gray-800">{edu.institution}</div>
          <div className="text-sm text-gray-500">{edu.area}</div>
        </div>
      ))}
    </div>
    
    {volunteering?.visible && volunteering.items.length > 0 && (
      <>
        <div className="border-b border-gray-200 my-6" />
        <SectionTitle number={4}>Volunteering</SectionTitle>
        <div className="space-y-6">
          {volunteering.items.map(vol => (
            <div key={vol.id}>
              <div className="flex justify-between items-center mb-2">
                <div className="font-bold text-gray-900 text-base">{vol.organization}</div>
                <div className="text-sm text-gray-500 font-medium">{vol.date}</div>
              </div>
              <div className="font-semibold text-sm text-gray-800 mb-2">{vol.position}</div>
              <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: vol.summary }} />
            </div>
          ))}
        </div>
      </>
    )}
  </main>
);

const Abhi3 = () => {
  const { basics, sections } = sampleResume;
  return (
    <div className="flex min-h-screen bg-gray-100 justify-center items-start py-8">
      <div style={{ width: '210mm', minHeight: '297mm' }} className="flex shadow-lg rounded overflow-hidden border border-gray-300">
        <Sidebar
          basics={basics}
          skills={sections.skills}
          languages={sections.languages}
          references={sections.references}
        />
        <MainContent
          summary={sections.summary}
          experience={sections.experience}
          education={sections.education}
          volunteering={sections.volunteering}
        />
      </div>
    </div>
  );
};

export default Abhi3;
