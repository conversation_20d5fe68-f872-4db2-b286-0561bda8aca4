/* Animation Styles */
@keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }
  
  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.5s ease forwards;
  }
  
  .slide-in-left {
    animation: slideInLeft 0.5s ease forwards;
  }
  
  .slide-in-right {
    animation: slideInRight 0.5s ease forwards;
  }
  
  .scale-in {
    animation: scaleIn 0.5s ease forwards;
  }
  
  .pulse {
    animation: pulse 2s infinite;
  }
  
  /* Apply animations to elements */
  .dashboard-stats .stat-card {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: calc(var(--i, 0) * 0.1s);
    opacity: 0;
  }
  
  .templates-grid .template-card {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: calc(var(--i, 0) * 0.1s);
    opacity: 0;
  }
  
  .sidebar-nav li {
    animation: slideInLeft 0.3s ease forwards;
    animation-delay: calc(var(--i, 0) * 0.05s);
    opacity: 0;
  }
  
  .dashboard-header h1 {
    animation: slideInLeft 0.5s ease forwards;
  }
  
  .dashboard-header .action-buttons {
    animation: slideInRight 0.5s ease forwards;
  }
  
  .section {
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.2s;
    opacity: 0;
  }
  
  .empty-icon {
    animation: pulse 2s infinite;
  }
  
  