<?php
require_once 'config.php';
require_once 'db.php';

session_start();

/**
 * Sanitize user input
 * @param string $data
 * @return string
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Redirect to a specific page
 * @param string $location
 * @return void
 */
function redirect($location) {
    header("Location: $location");
    exit;
}

/**
 * Check if user is logged in
 * @return boolean
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Require user to be logged in
 * @return void
 */
function requireLogin() {
    if (!isLoggedIn()) {
        $_SESSION['message'] = 'You must be logged in to access this page';
        $_SESSION['message_type'] = 'error';
        redirect('login.php');
    }
}

/**
 * Generate a random token
 * @param int $length
 * @return string
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Set flash message
 * @param string $message
 * @param string $type
 * @return void
 */
function setMessage($message, $type = 'success') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * Display flash message
 * @return string
 */
function displayMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'];
        
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
        
        return "<div class='alert alert-$type'>$message</div>";
    }
    return '';
}

/**
 * Upload file
 * @param array $file
 * @param string $directory
 * @return string|boolean
 */
function uploadFile($file, $directory = UPLOAD_DIR) {
    // Check if directory exists, if not create it
    if (!file_exists($directory)) {
        mkdir($directory, 0777, true);
    }
    
    // Check if file was uploaded without errors
    if ($file['error'] == 0) {
        $filename = $file['name'];
        $tmp_name = $file['tmp_name'];
        $size = $file['size'];
        
        // Get file extension
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        // Check file size
        if ($size > MAX_FILE_SIZE) {
            return false;
        }
        
        // Check if extension is allowed
        if (!in_array($ext, ALLOWED_EXTENSIONS)) {
            return false;
        }
        
        // Generate unique filename
        $new_filename = uniqid() . '.' . $ext;
        $destination = $directory . $new_filename;
        
        // Move uploaded file
        if (move_uploaded_file($tmp_name, $destination)) {
            return $new_filename;
        }
    }
    
    return false;
}

/**
 * Generate resume objective based on user data
 * @param array $userData
 * @return string
 */
function generateObjective($profile) {
    // Default objective if profile is incomplete
    $defaultObjective = "Dedicated professional seeking to leverage my skills and experience in a challenging role that allows for professional growth and development.";
    
    // Check if we have enough data to generate a personalized objective
    if (empty($profile['title']) || empty($profile['industry'])) {
        return $defaultObjective;
    }
    
    // Extract relevant profile information
    $title = $profile['title'];
    $industry = $profile['industry'];
    $years_experience = isset($profile['years_experience']) ? (int)$profile['years_experience'] : 0;
    
    // Experience level descriptions
    $experience_level = 'entry-level';
    if ($years_experience > 10) {
        $experience_level = 'seasoned';
    } elseif ($years_experience > 5) {
        $experience_level = 'experienced';
    } elseif ($years_experience > 2) {
        $experience_level = 'proficient';
    }
    
    // Generate objective based on experience level
    if ($years_experience > 5) {
        return "{$experience_level} {$title} with {$years_experience}+ years of experience in the {$industry} industry. Seeking to leverage extensive expertise to drive innovation and excellence while taking on challenging leadership responsibilities.";
    } elseif ($years_experience > 2) {
        return "{$experience_level} {$title} with {$years_experience} years of experience in {$industry}. Looking to apply my skills and knowledge to contribute to company growth while expanding my professional capabilities.";
    } else {
        return "Motivated {$title} professional with " . ($years_experience > 0 ? "{$years_experience} years of" : "a background in") . " {$industry}. Eager to contribute my skills and passion while growing professionally in a collaborative environment.";
    }
}
/**
 * Calculate total years of experience
 * @param int $userId
 * @return int
 */
function calculateExperience($userId) {
    global $db;
    
    $db->query("SELECT start_date, end_date, current_job FROM user_experience WHERE user_id = :user_id");
    $db->bind(':user_id', $userId);
    $experiences = $db->resultSet();
    
    $totalMonths = 0;
    
    foreach ($experiences as $exp) {
        $startDate = new DateTime($exp['start_date']);
        
        if ($exp['current_job']) {
            $endDate = new DateTime(); // Current date
        } else {
            $endDate = new DateTime($exp['end_date']);
        }
        
        $interval = $startDate->diff($endDate);
        $months = ($interval->y * 12) + $interval->m;
        
        $totalMonths += $months;
    }
    
    return round($totalMonths / 12);
}

/**
 * Get top skills by proficiency
 * @param int $userId
 * @param int $limit
 * @return array
 */
function getTopSkills($userId, $limit = 5) {
    global $db;
    
    $db->query("SELECT skill_name FROM user_skills WHERE user_id = :user_id ORDER BY proficiency DESC LIMIT :limit");
    $db->bind(':user_id', $userId);
    $db->bind(':limit', $limit, PDO::PARAM_INT);
    $skills = $db->resultSet();
    
    $skillNames = [];
    foreach ($skills as $skill) {
        $skillNames[] = $skill['skill_name'];
    }
    
    return $skillNames;
}

/**
 * Format date for display
 * @param string $date
 * @param string $format
 * @return string
 */
function formatDate($date, $format = 'M Y') {
    if (empty($date)) return '';
    $dateObj = new DateTime($date);
    return $dateObj->format($format);
}

/**
 * Check if email exists
 * @param string $email
 * @return boolean
 */
function emailExists($email) {
    global $db;
    
    $db->query("SELECT id FROM users WHERE email = :email");
    $db->bind(':email', $email);
    $db->execute();
    
    return $db->rowCount() > 0;
}

/**
 * Check if username exists
 * @param string $username
 * @return boolean
 */
function usernameExists($username) {
    global $db;
    
    $db->query("SELECT id FROM users WHERE name = :username");
    $db->bind(':username', $username);
    $db->execute();
    
    return $db->rowCount() > 0;
}

/**
 * Get user by ID
 * @param int $userId
 * @return array|boolean
 */
function getUserById($userId) {
    global $db;
    
    $db->query("SELECT * FROM users WHERE id = :id");
    $db->bind(':id', $userId);
    
    return $db->single();
}



// Get user downloads count
function getUserDownloadsCount($user_id) {
    global $db;
    
    $db->query("SELECT COUNT(*) as download_count FROM resume_downloads WHERE user_id = :user_id");
    $db->bind(':user_id', $user_id);
    
    $result = $db->single();
    return $result ? $result['download_count'] : 0;
}

/**
 * Get user profile by user ID
 * @param int $userId
 * @return array|boolean
 */
function getUserProfile($userId) {
    global $db;
    
    $db->query("SELECT * FROM user_profiles WHERE user_id = :id");
    $db->bind(':id', $userId);
    
    $profile = $db->single();
    
    if (!$profile) {
        // Create empty profile if it doesn't exist
        $db->query("INSERT INTO users (id) VALUES (:id)");
        $db->bind(':id', $userId);
        $db->execute();
        
        $db->query("SELECT * FROM users WHERE id = :id");
        $db->bind(':id', $userId);
        $profile = $db->single();
    }
    
    return $profile;
}

/**
 * Get user skills
 * @param int $userId
 * @return array
 */
function getUserSkills($userId) {
    global $db;
    
    $db->query("SELECT s.*, c.name AS category_name, c.icon AS category_icon 
                FROM skills s 
                LEFT JOIN skill_categories c ON s.category_id = c.id 
                WHERE s.user_id = :user_id 
                ORDER BY c.name, s.skill_name");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get user work experience
 * @param int $userId
 * @return array
 */
function getUserWorkExperience($userId) {
    global $db;
    
    $db->query("SELECT * FROM work_experience WHERE user_id = :user_id ORDER BY start_date DESC");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get user education
 * @param int $userId
 * @return array
 */
function getUserEducation($userId) {
    global $db;
    
    $db->query("SELECT * FROM education WHERE user_id = :user_id ORDER BY start_date DESC");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get user projects
 * @param int $userId
 * @return array
 */
function getUserProjects($userId) {
    global $db;
    
    $db->query("SELECT * FROM projects WHERE user_id = :user_id ORDER BY start_date DESC");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get user certifications
 * @param int $userId
 * @return array
 */
function getUserCertifications($userId) {
    global $db;
    
    $db->query("SELECT * FROM certifications WHERE user_id = :user_id ORDER BY issue_date DESC");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get all resume templates
 * @return array
 */
function getResumeTemplates() {
    global $db;
    
    $db->query("SELECT * FROM templates ORDER BY name");
    
    return $db->resultSet();
}

/**
 * Get resume template by ID
 * @param int $templateId
 * @return array|boolean
 */
function getResumeTemplate($templateId) {
    global $db;
    
    $db->query("SELECT * FROM templates WHERE id = :id");
    $db->bind(':id', $templateId);
    
    return $db->single();
}

/**
 * Get saved resume by ID
 * @param int $resumeId
 * @return array|boolean
 */
function getSavedResume($resumeId) {
    global $db;
    
    $db->query("SELECT * FROM user_resumes WHERE id = :id");
    $db->bind(':id', $resumeId);
    
    return $db->single();
}

/**
 * Get user saved resumes
 * @param int $userId
 * @return array
 */
function getUserSavedResumes($userId) {
    global $db;
    
    $db->query("SELECT sr.*, rt.name, rt.thumbnail 
                FROM user_resumes sr 
                JOIN templates rt ON sr.template_id = rt.id 
                WHERE sr.user_id = :user_id 
                ORDER BY sr.updated_at DESC");
    $db->bind(':user_id', $userId);
    
    return $db->resultSet();
}

/**
 * Get all skill categories
 * @return array
 */
function getSkillCategories() {
    global $db;
    
    $db->query("SELECT * FROM skill_categories ORDER BY name");
    
    return $db->resultSet();
}

/**
 * Check if profile is complete
 * @param int $userId
 * @return boolean
 */
function isProfileComplete($userId) {
    $profile = getUserProfile($userId);
    $skills = getUserSkills($userId);
    $experience = getUserWorkExperience($userId);
    $education = getUserEducation($userId);
    
    // Check if basic profile info is complete
    if (empty($profile['first_name']) || empty($profile['last_name']) || empty($profile['title'])) {
        return false;
    }
    
    // Check if user has at least one skill
    if (count($skills) == 0) {
        return false;
    }
    
    // Check if user has at least one work experience or education
    if (count($experience) == 0 && count($education) == 0) {
        return false;
    }
    
    return true;
}

function getSimilarTemplates($templateId, $category, $limit = 3) {
    global $db;

    $db->query("SELECT * FROM templates 
                WHERE category = :category AND id != :template_id 
                ORDER BY RAND() 
                LIMIT :limit");

    $db->bind(':category', $category);
    $db->bind(':template_id', $templateId);
    $db->bind(':limit', $limit, PDO::PARAM_INT);

    return $db->resultSet();
}

function getTemplateById($template_id) {
    global $db; // Assuming $db is your Database instance
    
    $db->query("SELECT * FROM templates WHERE id = :id");
    $db->bind(":id", $template_id);
    
    return $db->single(); // Fetch single record
}


/**
 * Get completion percentage of profile
 * @param int $userId
 * @return int
 */
function getProfileCompletionPercentage($userId) {
    $profile = getUserProfile($userId);
    $skills = getUserSkills($userId);
    $experience = getUserWorkExperience($userId);
    $education = getUserEducation($userId);
    $projects = getUserProjects($userId);
    $certifications = getUserCertifications($userId);
    
    $total = 0;
    $completed = 0;
    
    // Basic profile (30%)
    $profileFields = ['first_name', 'last_name', 'phone', 'address', 'city', 'state', 'country', 'title', 'summary', 'linkedin', 'github', 'website', 'profile_image'];
    $total += count($profileFields);
    foreach ($profileFields as $field) {
        if (!empty($profile[$field])) {
            $completed++;
        }
    }
    
    // Skills (20%)
    $total += 5; // Expecting at least 5 skills
    $completed += min(count($skills), 5);
    
    // Work Experience (20%)
    $total += 2; // Expecting at least 2 work experiences
    $completed += min(count($experience), 2);
    
    // Education (15%)
    $total += 1; // Expecting at least 1 education
    $completed += min(count($education), 1);
    
    // Projects (10%)
    $total += 2; // Expecting at least 2 projects
    $completed += min(count($projects), 2);
    
    // Certifications (5%)
    $total += 1; // Expecting at least 1 certification
    $completed += min(count($certifications), 1);
    
    return round(($completed / $total) * 100);
}
?>