import React from "react";

const Projects = ({
  title = "Projects",
  projects = [],
  sectionStyle = "",
  titleStyle = "",
  itemStyle = "",
  nameStyle = "",
  linkStyle = "",
  descriptionStyle = "",
}) => {
  if (!projects || projects.length === 0) return null;

  return (
    <section className={sectionStyle}>
      {title && (
        <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      )}
      <ul className="space-y-4">
        {projects.map((project, idx) => (
          <li key={idx} className={itemStyle}>
            <div className="flex justify-between items-center">
              <h4 className={`text-base font-semibold ${nameStyle}`}>
                {project.name}
              </h4>
              {project.link && (
                <a
                  href={project.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-sm text-blue-600 hover:underline ${linkStyle}`}
                >
                  {project.linkLabel || "View Project"}
                </a>
              )}
            </div>
            {project.description && (
              <p className={`text-sm text-gray-700 ${descriptionStyle}`}>
                {project.description}
              </p>
            )}
          </li>
        ))}
      </ul>
    </section>
  );
};

export default Projects;
