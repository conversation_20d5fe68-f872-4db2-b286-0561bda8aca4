import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";

const ModernTemplate = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user)
    return <p className="text-center mt-20 text-gray-500">Loading...</p>;

  const {
    Title,
    Headline,
    Email,
    Phone,
    Location,
    Website,
    ProfilePic,
    summery,
    Profiles = [],
    Experience = [],
    Projects = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
  } = resume;

  return (
    <div className="min-h-screen bg-[#f9f9f9] py-10 px-6 md:px-16 font-sans">
      {/* Sidebar Layout */}
      <div className="grid md:grid-cols-3 gap-8">
        {/* Sidebar */}
        <aside className="md:col-span-1 bg-white p-6 rounded-lg shadow-lg">
          {ProfilePic && (
            <img
              src={ProfilePic}
              alt="Profile"
              className="w-32 h-32 mx-auto rounded-full object-cover mb-4 border"
            />
          )}
          <h1 className="text-center text-2xl font-bold">{Title}</h1>
          <p className="text-center text-sm text-gray-600">{Headline}</p>
          <div className="mt-4 space-y-1 text-sm">
            <p>📧 {Email}</p>
            <p>📱 {Phone}</p>
            <p>📍 {Location}</p>
            {Website && (
              <a href={Website} className="text-blue-600 underline block">
                🌐 {Website}
              </a>
            )}
          </div>

          {/* Socials */}
          {Profiles.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold border-b mb-2">Socials</h2>
              {Profiles.map((p) => (
                <p key={p._id}>
                  {p.Network}:{" "}
                  <a href={p.ProfileLink} className="text-blue-600 underline">
                    {p.Username}
                  </a>
                </p>
              ))}
            </div>
          )}

          {/* Skills */}
          {Skills.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold border-b mb-2">Skills</h2>
              <ul className="list-disc list-inside">
                {Skills.map((s) => (
                  <li key={s._id}>
                    {s.Skill} ({s.Proficiency})
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Languages */}
          {Languages.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold border-b mb-2">Languages</h2>
              <ul className="list-disc list-inside">
                {Languages.map((l) => (
                  <li key={l._id}>
                    {l.Name} - {l.Proficiency}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </aside>

        {/* Main Content */}
        <main className="md:col-span-2 space-y-8">
          {/* Summary */}
          {summery && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Summary</h2>
              <p>{summery}</p>
            </section>
          )}

          {/* Experience */}
          {Experience.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Experience</h2>
              {Experience.map((e) => (
                <div key={e._id} className="mb-3">
                  <h3 className="font-semibold">
                    {e.Position} @ {e.Company}
                  </h3>
                  <p className="text-sm text-gray-600 italic">{e.Location}</p>
                  <p className="text-sm">
                    {new Date(e.StartDate).toLocaleDateString()} -{" "}
                    {new Date(e.EndDate).toLocaleDateString()}
                  </p>
                  {e.Description && <p>{e.Description}</p>}
                </div>
              ))}
            </section>
          )}

          {/* Projects */}
          {Projects.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Projects</h2>
              {Projects.map((p) => (
                <div key={p._id} className="mb-3">
                  <h3 className="font-semibold">{p.Title}</h3>
                  <p>{p.Description}</p>
                  <p className="text-sm text-gray-600">
                    {p.Technologies?.join(", ")}
                  </p>
                  {p.Link && (
                    <a href={p.Link} className="text-blue-600 underline">
                      {p.Link}
                    </a>
                  )}
                </div>
              ))}
            </section>
          )}

          {/* Education */}
          {Education.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Education</h2>
              {Education.map((edu) => (
                <div key={edu._id}>
                  <p className="font-semibold">
                    {edu.Degree} @ {edu.Institution}
                  </p>
                  <p className="text-sm italic">{edu.Location}</p>
                  <p className="text-sm">
                    {new Date(edu.StartDate).toLocaleDateString()} -{" "}
                    {new Date(edu.EndDate).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </section>
          )}

          {/* Certifications */}
          {Certifications.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">
                Certifications
              </h2>
              {Certifications.map((c) => (
                <div key={c._id}>
                  <p>
                    {c.Title} by {c.Issuer}
                  </p>
                  {c.Website && (
                    <a href={c.Website} className="text-blue-600 underline">
                      {c.Website}
                    </a>
                  )}
                </div>
              ))}
            </section>
          )}

          {/* Awards */}
          {Awards.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Awards</h2>
              {Awards.map((a) => (
                <p key={a._id}>
                  {a.Title} - {a.Issuer}
                </p>
              ))}
            </section>
          )}

          {/* Publications */}
          {Publications.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Publications</h2>
              {Publications.map((pub) => (
                <div key={pub._id}>
                  <p>
                    {pub.Title} - {pub.Publisher}
                  </p>
                  <a href={pub.Website} className="text-blue-600 underline">
                    {pub.Website}
                  </a>
                </div>
              ))}
            </section>
          )}

          {/* Volunteering */}
          {Volunteering.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Volunteering</h2>
              {Volunteering.map((v) => (
                <p key={v._id}>
                  {v.Position} at {v.Organization} ({v.Location})
                </p>
              ))}
            </section>
          )}

          {/* References */}
          {References.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">References</h2>
              {References.map((r) => (
                <p key={r._id}>
                  {r.Name}, {r.Position} at {r.Company}
                </p>
              ))}
            </section>
          )}

          {/* Interests */}
          {Interests.length > 0 && (
            <section>
              <h2 className="text-xl font-bold border-b mb-2">Interests</h2>
              <ul className="list-disc list-inside">
                {Interests.map((i, idx) => (
                  <li key={idx}>{i.Interest || i}</li>
                ))}
              </ul>
            </section>
          )}
        </main>
      </div>
    </div>
  );
};

export default ModernTemplate;
