import React from "react";
import { Navigate } from "react-router-dom";
import { useAuthStore } from "../store/authStore";

const AdminRoute = ({ children }) => {
    const { user, isHydrated } = useAuthStore();

    if (!isHydrated) {
        return <div className="text-center mt-10">Loading...</div>;
    }

    if (!user || user.role !== "admin") {
        return <Navigate to="/login" replace />;
    }

    return children;
};

export default AdminRoute;
