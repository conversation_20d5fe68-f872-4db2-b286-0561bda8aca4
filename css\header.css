:root {
    --primary: rgb(25,65,75);
    --primary-light: rgba(25,65,75,0.1);
    --primary-dark: rgb(15,45,55);
    --secondary: #f8a978;
    --secondary-light: #ffeee2;
    --accent: #4ecdc4;
    --light: #f7f9fc;
    --dark: #2d3748;
    --gray: #718096;
    --success: #48bb78;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --radius: 8px;
    --radius-lg: 12px;
    --transition: all 0.3s ease;
}

header {
    background-color: #F2F2F2;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    text-decoration: none;
}

.logo i {
    font-size: 1.8rem;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav a {
    color: var(--dark);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 0;
    position: relative;
}

nav a:hover {
    color: var(--primary);
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: var(--transition);
}

nav a:hover::after {
    width: 100%;
}

.auth-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    border: none;
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

.btn-light {
    background-color: var(--white);
    color: var(--primary);
}

.btn-light:hover {
    background-color: var(--light);
    transform: translateY(-2px);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}