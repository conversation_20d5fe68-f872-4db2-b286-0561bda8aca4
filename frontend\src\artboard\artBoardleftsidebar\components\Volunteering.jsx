import React, { useState, useEffect } from "react";
import { FaHandsHelping, FaPlus, FaBars, FaTimes } from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import DataCard from "../../Components/DataCard";

const Volunteering = () => {
  const [showModal, setShowModal] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const [formData, setFormData] = useState({
    Organization: "",
    Position: "",
    StartDate: "",
    EndDate: "",
    Location: "",
    Description: "",
  });

  const { resume, fetchResume, isLoading } = useResumeStore();
  const addInfo = useAddInfoStore((state) => state.addInfo);
  const updateInfo = useUpdateInfoStore((state) => state.updateInfo);
  const deleteInfo = useDeleteInfoStore((state) => state.deleteInfo);
  const volunteering = resume?.Volunteering || [];

  useEffect(() => {
    fetchResume();
  }, []);

  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!formData.Organization || !formData.Position || !formData.StartDate) {
      alert(
        "Please fill in required fields: Organization, Position, Start Date."
      );
      return;
    }

    const resumeId = resume?._id;
    if (!resumeId) {
      alert("Resume ID is missing.");
      return;
    }

    try {
      if (isEdit && editingId) {
        await updateInfo({
          resumeId,
          section: "volunteering",
          entryId: editingId,
          updatedData: formData,
        });
      } else {
        await addInfo({
          resumeId,
          section: "volunteering",
          newData: formData,
        });
      }

      await fetchResume();
      setShowModal(false);
      setIsEdit(false);
      setEditingId(null);
      setFormData({
        Organization: "",
        Position: "",
        StartDate: "",
        EndDate: "",
        Location: "",
        Description: "",
      });
    } catch (error) {
      alert("Failed to save volunteering entry.");
    }
  };

  const handleDeleteConfirmed = async () => {
    if (!confirmDelete || !resume?._id) return;
    await deleteInfo("volunteering", confirmDelete._id);
    await fetchResume();
    setConfirmDelete(null);
  };

  const handleEdit = (entry) => {
    setFormData({
      Organization: entry.Organization,
      Position: entry.Position,
      StartDate: entry.StartDate?.split("T")[0],
      EndDate: entry.EndDate?.split("T")[0] || "",
      Location: entry.Location,
      Description: entry.Description,
    });
    setIsEdit(true);
    setEditingId(entry._id);
    setShowModal(true);
  };

  return (
    <div className="bg-white min-h-screen text-black p-6 font-sans ml-5">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-gray-100 p-2 rounded-full">
            <FaHandsHelping size={18} />
          </div>
          <h2 className="text-2xl font-bold">Volunteering</h2>
        </div>
        <FaBars size={20} className="text-gray-400" />
      </div>

      {/* Add Button */}
      <div
        onClick={() => {
          setShowModal(true);
          setIsEdit(false);
          setFormData({
            Organization: "",
            Position: "",
            StartDate: "",
            EndDate: "",
            Location: "",
            Description: "",
          });
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-black text-lg font-medium mb-8 transition"
      >
        <FaPlus className="mr-2" /> Add a new item
      </div>

      {isLoading ? (
        <p className="text-gray-500">Loading...</p>
      ) : volunteering.length === 0 ? (
        <p className="text-gray-500">No volunteering entries added yet.</p>
      ) : (
        <div className="flex flex-wrap gap-4">
          {volunteering.map((entry) => (
            <div key={entry._id} className="w-fit min-w-[250px]">
              <DataCard
                title={
                  <div className="flex justify-between items-center">
                    <span>{entry.Organization}</span>
                    <span
                      className="text-gray-400 cursor-pointer hover:text-black"
                      onClick={() => handleEdit(entry)}
                      title="Edit"
                    >
                      Edit
                    </span>
                  </div>
                }
                subtitle={
                  <>
                    <span className="font-medium">{entry.Position}</span>
                    {entry.StartDate && (
                      <span>
                        {" "}
                        • {new Date(entry.StartDate).toLocaleDateString()}
                        {entry.EndDate
                          ? ` - ${new Date(entry.EndDate).toLocaleDateString()}`
                          : " - Present"}
                      </span>
                    )}
                  </>
                }
                description={
                  <>
                    {entry.Description && (
                      <p className="text-sm text-gray-800 mb-1">
                        {entry.Description}
                      </p>
                    )}
                    {entry.Location && (
                      <p className="text-sm text-gray-600 italic">
                        {entry.Location}
                      </p>
                    )}
                  </>
                }
                data={entry}
                onDelete={() => setConfirmDelete(entry)}
              />
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center">
          <div className="bg-white p-8 rounded-xl w-full max-w-lg shadow-2xl relative text-black">
            <button
              onClick={() => {
                setShowModal(false);
                setIsEdit(false);
                setEditingId(null);
              }}
              className="absolute top-4 right-4 text-2xl text-gray-400 hover:text-black"
            >
              ×
            </button>

            <h3 className="text-xl font-bold mb-6 flex items-center gap-2">
              <FaPlus />{" "}
              {isEdit
                ? "Edit Volunteering Experience"
                : "Add Volunteering Experience"}
            </h3>

            <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
              {[
                { label: "Organization", key: "Organization" },
                { label: "Position", key: "Position" },
                { label: "Start Date", key: "StartDate", type: "date" },
                { label: "End Date", key: "EndDate", type: "date" },
                { label: "Location", key: "Location" },
              ].map(({ label, key, type }) => (
                <div key={key}>
                  <label className="block mb-1 text-sm font-medium">
                    {label}
                  </label>
                  <input
                    type={type || "text"}
                    value={formData[key]}
                    onChange={(e) => handleChange(key, e.target.value)}
                    className="w-full bg-gray-50 border border-gray-400 rounded-lg px-4 py-2 text-black"
                  />
                </div>
              ))}

              <div>
                <label className="block mb-1 text-sm font-medium">
                  Description
                </label>
                <textarea
                  rows={4}
                  value={formData.Description}
                  onChange={(e) => handleChange("Description", e.target.value)}
                  placeholder="Describe your volunteering work..."
                  className="w-full bg-gray-50 border border-gray-400 rounded-lg px-4 py-2 text-black"
                />
              </div>

              <button
                type="button"
                onClick={handleSave}
                className="bg-black text-white px-6 py-2 rounded-lg float-right hover:bg-gray-800 transition"
              >
                {isEdit ? "Update" : "Save"}
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl relative">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2 text-black">
              Delete Volunteering Entry
            </h3>
            <p className="text-sm mb-4 text-gray-700">
              Are you sure you want to delete this volunteering entry?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4 text-[#29354d]">
              <p>
                <strong>Organization:</strong> {confirmDelete.Organization}
              </p>
              <p>
                <strong>Position:</strong> {confirmDelete.Position}
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Volunteering;
