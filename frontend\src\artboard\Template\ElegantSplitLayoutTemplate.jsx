import React, { useEffect } from "react";
import {
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiBriefcase,
  FiBookOpen,
  FiAward,
  FiFileText,
  FiStar,
  FiHeart,
  FiUsers,
  FiCheckCircle,
  FiFolder,
  FiLinkedin,
} from "react-icons/fi";

import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";
import { useThemeStore } from "../../store/themeStore";
import { useTypographyStore } from "../../store/themeTypographyStore";

const iconMap = {
  Summary: <FiFileText />,
  Skills: <FiCheckCircle />,
  Languages: <FiGlobe />,
  Interests: <FiHeart />,
  Experience: <FiBriefcase />,
  Projects: <FiFolder />,
  Education: <FiBookOpen />,
  Certifications: <FiAward />,
  Awards: <FiStar />,
  Publications: <FiFileText />,
  Volunteering: <FiUsers />,
  References: <FiUser />,
  Profiles: <FiLinkedin />,
};

const ElegantSplitLayoutTemplate = ({
  exportMode = false,
  themeOverrides = {},
}) => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  const {
    primaryColor: storePrimary,
    backgroundColor: storeBackground,
    textColor: storeText,
  } = useThemeStore();

  const {
    fontFamily,
    fontVariant,
    fontSize,
    lineHeight,
    hideIcons,
    underlineLinks,
  } = useTypographyStore();

  const {
    primaryColor = storePrimary,
    backgroundColor = exportMode ? "#ffffff" : storeBackground,
    textColor = exportMode ? "#1a1a1a" : storeText,
  } = themeOverrides;

  const variantStyle = {};
  if (fontVariant === "italic") variantStyle.fontStyle = "italic";
  else if (!isNaN(fontVariant)) variantStyle.fontWeight = fontVariant;

  useEffect(() => {
    const linkId = "google-font-link";
    let link = document.getElementById(linkId);
    if (link) link.remove();
    link = document.createElement("link");
    link.id = linkId;
    link.rel = "stylesheet";
    link.href = `https://fonts.googleapis.com/css?family=${fontFamily.replace(
      / /g,
      "+"
    )}:${fontVariant}`;
    document.head.appendChild(link);
  }, [fontFamily, fontVariant]);

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user)
    return exportMode ? null : (
      <p className="text-center mt-20 text-gray-500">Loading resume...</p>
    );

  const {
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;

  const { name } = user;

  return (
    <div
      className="a4-container px-8 py-10"
      style={{
        backgroundColor,
        color: textColor,
        fontFamily,
        fontSize,
        lineHeight,
        ...variantStyle,
      }}
    >
      <header className="text-center border-b pb-4 mb-6">
        <h1 className="text-3xl font-bold" style={{ color: primaryColor }}>
          {!hideIcons && <FiUser className="inline mr-2" />} {name}
        </h1>
        {Headline && <p className="italic text-sm text-gray-600">{Headline}</p>}
        <div className="flex justify-center gap-4 mt-2 text-xs">
          {Email && (
            <span>
              {!hideIcons && <FiMail className="inline mr-1" />} {Email}
            </span>
          )}
          {Phone && (
            <span>
              {!hideIcons && <FiPhone className="inline mr-1" />} {Phone}
            </span>
          )}
          {Location && (
            <span>
              {!hideIcons && <FiMapPin className="inline mr-1" />} {Location}
            </span>
          )}
          {Website && (
            <a
              href={Website}
              style={{
                textDecoration: underlineLinks ? "underline" : "none",
                color: primaryColor,
              }}
            >
              {!hideIcons && <FiGlobe className="inline mr-1" />}
              {Website}
            </a>
          )}
        </div>
      </header>

      {summery && (
        <RenderSection
          title="Summary"
          content={<p>{summery}</p>}
          primaryColor={primaryColor}
          hideIcons={hideIcons}
        />
      )}

      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-4">
          <RenderSection
            title="Skills"
            content={<RenderList items={Skills.map((s) => s.Skill)} />}
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderSection
            title="Languages"
            content={
              <RenderList
                items={Languages.map((l) => `${l.Name} - ${l.Proficiency}`)}
              />
            }
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderSection
            title="Interests"
            content={
              <RenderList items={Interests.map((i) => i.Interest || i)} />
            }
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderSection
            title="Profiles"
            content={
              <RenderList
                items={Profiles.map((p) => `${p.Network}: ${p.Username}`)}
              />
            }
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
        </div>

        <div className="space-y-4">
          <RenderExperience
            title="Experience"
            items={Experience}
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderExperience
            title="Projects"
            items={Projects}
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderExperience
            title="Education"
            items={Education}
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
          <RenderExperience
            title="Certifications"
            items={Certifications}
            primaryColor={primaryColor}
            hideIcons={hideIcons}
          />
        </div>
      </div>
    </div>
  );
};

const RenderSection = ({ title, content, primaryColor, hideIcons }) => (
  <section>
    <h2
      className="font-semibold text-sm mb-1 flex items-center gap-2 uppercase tracking-wide"
      style={{ color: primaryColor }}
    >
      {!hideIcons && iconMap[title]} {title}
    </h2>
    <div className="text-xs">{content}</div>
  </section>
);

const RenderList = ({ items }) => (
  <ul className="list-disc pl-5 space-y-1">
    {items.map((item, idx) => (
      <li key={idx}>{item}</li>
    ))}
  </ul>
);

const RenderExperience = ({ title, items, primaryColor, hideIcons }) =>
  items.length > 0 && (
    <RenderSection
      title={title}
      primaryColor={primaryColor}
      hideIcons={hideIcons}
      content={items.map((item, idx) => (
        <div key={idx} className="mb-2">
          <p className="text-sm font-medium">
            {item.Title || item.Position} @ {item.Company || item.Organization}
          </p>
          <p className="text-xs italic text-gray-500">
            {formatDate(item.StartDate)} - {formatDate(item.EndDate)}
          </p>
          <p className="text-xs mt-1">{item.Description}</p>
        </div>
      ))}
    />
  );

const formatDate = (date) => {
  if (!date) return "Present";
  try {
    return new Date(date).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    });
  } catch {
    return "N/A";
  }
};

export default ElegantSplitLayoutTemplate;
