import React, { useState, useRef } from "react";
import {
  FiDownload,
  <PERSON><PERSON><PERSON>,
  <PERSON>EyeOff,
  FiShare2,
  FiUploadCloud,
  FiMail,
  FiCheckCircle,
  FiExternalLink,
} from "react-icons/fi";
import axios from "axios";
import { useTemplateStore } from "../../../store/templateStore";
import { TEMPLATES } from "../../Template";
import { useThemeStore } from "../../../store/themeStore";

const BACKEND_API_URL = import.meta.env.VITE_API_BASE_URL;

const Export = () => {
  const { selectedTemplateId } = useTemplateStore();
  const [exportTypes, setExportTypes] = useState(["pdf"]);
  const [isExporting, setIsExporting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [publicLink, setPublicLink] = useState(null);
  const templateRef = useRef();

  const templateData = TEMPLATES[selectedTemplateId] || TEMPLATES["ganesh"];
  const TemplateComponent = templateData.component;
  const templateName = templateData.name;

  const user = { name: "Ganesh" };

  const generateFileName = () => {
    const username = user?.name?.replace(/\s+/g, "_").toLowerCase() || "user";
    const template =
      templateName?.replace(/\s+/g, "_").toLowerCase() || "template";
    const date = new Date().toISOString().split("T")[0];
    return `resume_${username}_${template}_${date}`;
  };

  const handleExport = async () => {
    if (!templateRef.current) return;
    setIsExporting(true);
    const fileName = generateFileName();
    const htmlContent = templateRef.current.innerHTML;

    try {
      for (const format of exportTypes) {
        const res = await axios.post(
          `${BACKEND_API_URL}/pdf/generate`,
          { html: htmlContent, format, fileName },
          { responseType: "blob" }
        );

        const blob = new Blob([res.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${fileName}.${format}`;
        link.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (err) {
      console.error("Export failed:", err);
      alert("Something went wrong during export.");
    } finally {
      setIsExporting(false);
    }
  };

  const handleUpload = async () => {
    if (!templateRef.current) return;
    setUploading(true);
    const fileName = generateFileName();
    const htmlContent = templateRef.current.innerHTML;

    try {
      const res = await axios.post(
        `${BACKEND_API_URL}/pdf/generate`,
        { html: htmlContent, format: "png", fileName },
        { responseType: "blob" }
      );

      const file = new File([res.data], `${fileName}.png`, {
        type: "image/png",
      });

      const formData = new FormData();
      formData.append("file", file);
      formData.append("fileName", fileName);

      const uploadRes = await axios.post(`${BACKEND_API_URL}/upload`, formData);
      if (uploadRes.data.url) {
        setPublicLink(uploadRes.data.url);
      } else {
        alert("Upload failed.");
      }
    } catch (err) {
      console.error("Upload error:", err);
      alert("Upload failed.");
    } finally {
      setUploading(false);
    }
  };

  const toggleExportType = (type) => {
    setExportTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch {}
  };

  const theme = useThemeStore();

  return (
    <div className="relative bg-black text-white rounded-xl shadow-md p-4 transition-all duration-200 max-w-lg mx-auto">
      <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
        <FiDownload className="text-[#fcc250]" />
        Export Resume
      </h2>

      <button
        onClick={() => setShowPreview((prev) => !prev)}
        className="text-sm mb-2 flex items-center gap-2 px-3 py-1 rounded-md hover:bg-white/10 transition"
      >
        {showPreview ? <FiEyeOff /> : <FiEye />}
        {showPreview ? "Hide Preview" : "Show Preview"}
      </button>

      {showPreview && (
        <div className="border p-3 mb-4 rounded-md shadow-inner bg-white text-black">
          <TemplateComponent exportMode={false} themeOverrides={theme} />
        </div>
      )}

      <label className="block font-semibold text-sm mb-1">
        Select Formats:
      </label>
      <div className="flex flex-wrap gap-4 mt-2 mb-4">
        {["pdf", "docx", "txt", "png"].map((format) => (
          <label
            key={format}
            className={`flex items-center gap-2 px-2 py-1 rounded-md cursor-pointer ${
              exportTypes.includes(format)
                ? "bg-white text-black"
                : "bg-white/10 hover:bg-white/20"
            } transition`}
          >
            <input
              type="checkbox"
              value={format}
              checked={exportTypes.includes(format)}
              onChange={() => toggleExportType(format)}
              className="accent-[#fcc250]"
            />
            <span className="capitalize">{format}</span>
          </label>
        ))}
      </div>

      <button
        onClick={handleExport}
        disabled={isExporting || exportTypes.length === 0}
        className={`mt-2 w-full flex items-center justify-center gap-2 rounded-lg px-4 py-2 font-semibold shadow ${
          isExporting || exportTypes.length === 0
            ? "bg-white/20 text-white cursor-not-allowed"
            : "bg-white text-black hover:bg-[#fcc250] hover:text-black"
        } transition`}
      >
        {isExporting ? (
          <svg
            className="animate-spin h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 00-8 8h4z"
            />
          </svg>
        ) : (
          <FiDownload />
        )}
        <span>{isExporting ? "Exporting…" : "Export"}</span>
      </button>

      <button
        onClick={handleUpload}
        disabled={uploading}
        className="mt-3 w-full flex items-center justify-center gap-2 rounded-lg px-4 py-2 font-medium bg-white text-black hover:bg-[#fcc250] transition"
      >
        <FiUploadCloud />
        <span>{uploading ? "Uploading…" : "Upload to Cloud"}</span>
      </button>

      {publicLink && (
        <div className="mt-6 p-4 rounded-xl bg-white text-black border border-white/20 shadow-sm flex flex-col gap-2">
          <div className="flex items-center gap-2 text-black font-semibold mb-1">
            <FiCheckCircle className="text-green-500" />
            Uploaded! Public Link:
          </div>
          <div className="flex flex-wrap gap-3 items-center mb-1">
            <a
              href={publicLink}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 px-3 py-1 rounded bg-black text-white hover:bg-white hover:text-black border border-black transition"
            >
              <FiExternalLink />
              View Resume
            </a>
            <button
              onClick={() => copyToClipboard(publicLink)}
              className="flex items-center gap-1 px-3 py-1 rounded bg-black text-white hover:bg-white hover:text-black border border-black transition"
            >
              <FiShare2 />
              Copy Link
            </button>
          </div>
          <div className="flex flex-wrap gap-3">
            <a
              href={`https://wa.me/?text=${encodeURIComponent(publicLink)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1 px-3 py-1 rounded bg-green-600 text-white hover:bg-green-700 transition"
            >
              WhatsApp
            </a>
            <a
              href={`mailto:?subject=My Resume&body=Here is my resume: ${encodeURIComponent(
                publicLink
              )}`}
              className="flex items-center gap-1 px-3 py-1 rounded bg-blue-600 text-white hover:bg-blue-700 transition"
            >
              <FiMail />
              Email
            </a>
          </div>
        </div>
      )}

      <div
        ref={templateRef}
        style={{ position: "absolute", left: "-9999px", top: "-9999px" }}
      >
        <div
          style={{
            fontFamily: "'Inter', sans-serif",
            WebkitPrintColorAdjust: "exact",
            printColorAdjust: "exact",
          }}
        >
          <TemplateComponent exportMode={true} themeOverrides={theme} />
        </div>
      </div>
    </div>
  );
};

export default Export;
