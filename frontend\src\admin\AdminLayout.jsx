import React from "react";
import Sidebar from "../components/admin/Sidebar";
import { Outlet } from "react-router-dom";
import Header from "../components/atoms/Header";

const AdminLayout = () => {
    return (
        <div className="flex bg-gray-100">
            {/* Fixed Sidebar */}
            <div className="fixed top-0 left-0 h-screen w-64 bg-[#29354d] text-white shadow-lg z-40">
                <Sidebar />
            </div>

            {/* Main Content with left margin */}
            <div className="ml-64 flex flex-col w-full">
                {/* Sticky Header */}
                <div className="sticky top-0 z-30 bg-white shadow">
                    <Header />
                </div>

                {/* Scrollable Page Content */}
                <main className="flex-1 h-[calc(100vh-64px)] overflow-y-auto p-4">
                    <Outlet />
                </main>
            </div>
        </div>
    );
};

export default AdminLayout;
