import { create } from "zustand";
import { persist } from "zustand/middleware";
import axios from "axios";
import { GETINFO_ENDPOINT } from "../lib/constants";

export const useUserInfoStore = create(
    persist(
        (set) => ({
            userInfo: null,
            isLoading: false,
            fetchUserInfo: async () => {
                set({ isLoading: true });
                try {
                    const res = await axios.get(GETINFO_ENDPOINT, { withCredentials: true });
                    set({ userInfo: res.data.user, isLoading: false });
                } catch (err) {
                    console.error("Failed to fetch user info", err);
                    set({ isLoading: false });
                }
            },
        }),
        {
            name: "resume-user-info", // localStorage key
        }
    )
);
