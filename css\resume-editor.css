/* Base Styles */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-color: #333;
  --light-text: #666;
  --lighter-text: #999;
  --border-color: #ddd;
  --bg-color: #f9f9f9;
  --white: #fff;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --font-family: 'Roboto', sans-serif;
  --font-size-base: 16px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.btn i {
  margin-right: 0.5rem;
}

.primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.primary:hover {
  background-color: #1a252f;
}

.secondary {
  background-color: var(--white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.secondary:hover {
  background-color: #f0f0f0;
}

.icon-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.icon-btn i {
  margin: 0;
}

.add-item-btn {
  margin-top: 1rem;
  background-color: #f0f0f0;
  color: var(--text-color);
}

.add-item-btn:hover {
  background-color: #e0e0e0;
}

/* Layout */
.editor-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--white);
  box-shadow: var(--shadow);
  z-index: 10;
}

.editor-header .logo h1 {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin: 0;
}

.editor-header .actions {
  display: flex;
  gap: 1rem;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.editor-sidebar {
  width: 300px;
  background-color: var(--white);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 1.5rem;
}

.sidebar-section {
  margin-bottom: 2rem;
}

.sidebar-section h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.section-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: var(--transition);
}

.section-item:hover {
  background-color: #e0e0e0;
}

.section-item i {
  margin-right: 0.75rem;
  color: var(--primary-color);
}

/* Style Options */
.style-options {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.style-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.style-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--light-text);
}

.style-group select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.9rem;
}

.color-options {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.color-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--transition);
}

.color-option.active {
  border-color: var(--primary-color);
}

.color-option.custom {
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, #f00, #ff0, #0f0, #0ff, #00f, #f0f, #f00);
}

.color-option.custom input {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  cursor: pointer;
  opacity: 0;
}

.font-size-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

#current-font-size {
  font-size: 0.9rem;
  min-width: 60px;
  text-align: center;
}

/* Main Editor */
.editor-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--light-text);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.95rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Resume Sections */
.editor-section {
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
  flex: 1;
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.section-header h3 i {
  margin-right: 0.75rem;
  color: var(--primary-color);
}

.drag-handle {
  cursor: grab;
  margin-right: 1rem;
  color: var(--lighter-text);
}

.section-controls {
  display: flex;
  gap: 0.5rem;
}

.section-controls button {
  background: none;
  border: none;
  color: var(--light-text);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.section-controls button:hover {
  background-color: #e0e0e0;
  color: var(--text-color);
}

.section-content {
  padding: 1.5rem;
}

.editor-section.active .section-content {
  display: block;
}

/* Experience and Education Items */
.experience-item,
.education-item {
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f0f0f0;
  border-bottom: 1px solid var(--border-color);
}

.item-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.item-controls {
  display: flex;
  gap: 0.5rem;
}

.item-controls button {
  background: none;
  border: none;
  color: var(--light-text);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.item-controls button:hover {
  background-color: #e0e0e0;
  color: var(--text-color);
}

.item-form {
  padding: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

/* Bullet Points */
.bullet-points-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.bullet-point {
  display: flex;
  gap: 0.5rem;
}

.bullet-point input {
  flex: 1;
}

.bullet-point .remove-bullet {
  background: none;
  border: none;
  color: var(--light-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.bullet-point .remove-bullet:hover {
  color: var(--accent-color);
}

/* Skills */
.add-skill-container {
  display: flex;
  gap: 0.5rem;
}

.add-skill-container input {
  flex: 1;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.skill-tag {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.skill-tag .skill-remove {
  background: none;
  border: none;
  color: var(--light-text);
  cursor: pointer;
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.skill-tag .skill-remove:hover {
  color: var(--accent-color);
}

/* Resume Preview */
.resume-preview {
  width: 0;
  background-color: var(--white);
  border-left: 1px solid var(--border-color);
  transition: width 0.3s ease;
  overflow: hidden;
}

.resume-preview.active {
  width: 50%;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid var(--border-color);
}

.preview-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.preview-content {
  padding: 2rem;
  overflow-y: auto;
  height: calc(100vh - 60px);
}

/* Resume Template Styles */
.modern-resume {
  font-family: var(--font-family);
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--white);
  box-shadow: var(--shadow);
  padding: 2rem;
}

.resume-header {
  margin-bottom: 2rem;
  text-align: center;
}

.resume-header .name {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.resume-header .title {
  font-size: 1.25rem;
  color: var(--light-text);
  margin-bottom: 1rem;
}

.contact-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  font-size: 0.9rem;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
  margin-bottom: 1.25rem;
}

.section-item {
  margin-bottom: 1.5rem;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.date {
  color: var(--light-text);
  font-size: 0.9rem;
}

.item-subtitle {
  font-size: 1rem;
  color: var(--light-text);
  margin-bottom: 0.75rem;
}

.description ul {
  padding-left: 1.5rem;
}

.description ul li {
  margin-bottom: 0.5rem;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-item {
  background-color: #f0f0f0;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 1200px) {
  .editor-sidebar {
      width: 250px;
  }
}

@media (max-width: 992px) {
  .editor-content {
      flex-direction: column;
  }
  
  .editor-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
      padding: 1rem;
  }
  
  .sidebar-section {
      margin-bottom: 1.5rem;
  }
  
  .resume-preview.active {
      width: 100%;
      height: 50vh;
  }
}

@media (max-width: 768px) {
  .editor-header {
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;
  }
  
  .form-row {
      flex-direction: column;
      gap: 1rem;
  }
  
  .editor-main {
      padding: 1rem;
  }
}

/* Generating PDF Styles */
.generating-pdf {
  background-color: white !important;
  box-shadow: none !important;
}

.generating-pdf * {
  color: black !important;
  background-color: white !important;
}
