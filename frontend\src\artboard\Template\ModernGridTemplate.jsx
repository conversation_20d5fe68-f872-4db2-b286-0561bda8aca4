import React, { useEffect } from "react";
import { useSkillsStore } from "../../store/skillsStore";
import { useProfileStore } from "../../store/profileStore";
import { useExperienceStore } from "../../store/experienceStore";
import { useEducationStore } from "../../store/educationStore";
import { useUserInfoStore } from "../../store/userInfoStore";
import { useAuthStore } from "../../store/authStore";
import { useProjectStore } from "../../store/projectStore";
import { useCertificationsStore } from "../../store/certificationsStore";
import { useAwardsStore } from "../../store/awardStore";
import { useLanguageStore } from "../../store/languageStore";

const SectionCard = ({ title, children }) => (
  <div className="bg-gray-50 border-l-4 border-[#fcc250] p-4 rounded shadow-sm">
    <h3 className="text-lg font-bold text-[#29354d] mb-2">{title}</h3>
    <div className="text-sm text-gray-800">{children}</div>
  </div>
);

const ModernGridTemplate = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { userInfo, fetchUserInfo } = useUserInfoStore();
  const { experiences, fetchExperiences } = useExperienceStore();
  const { education, fetchEducation } = useEducationStore();
  const { projects, fetchProjects } = useProjectStore();
  const { skills, fetchSkills } = useSkillsStore();
  const { profiles, fetchProfiles } = useProfileStore();
  const { certifications, fetchCertifications } = useCertificationsStore();
  const { awards, fetchAwards } = useAwardsStore();
  const { languages, fetchLanguages } = useLanguageStore();

  useEffect(() => {
    fetchCurrentUser();
    fetchUserInfo();
    fetchExperiences();
    fetchEducation();
    fetchProjects();
    fetchSkills();
    fetchProfiles();
    fetchCertifications();
    fetchAwards();
    fetchLanguages();
  }, []);

  const basics = {
    name: user?.name || "",
    location: userInfo?.Location || "",
    phone: userInfo?.Phone || "",
    email: userInfo?.email || "",
    website: userInfo?.Website || "",
    summary: user?.summary || "<p>Passionate Full Stack Developer</p>",
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-xl shadow-md border print:p-0 print:shadow-none print:border-none print:max-w-a4">
      {/* Header */}
      <div className="text-center mb-6">
        <h1 className="text-3xl font-extrabold text-[#29354d]">
          {basics.name}
        </h1>
        <div className="text-sm text-gray-700 mt-1">
          📍 {basics.location} | 📞 {basics.phone} | ✉️{" "}
          <a
            href={`mailto:${basics.email}`}
            className="underline text-blue-700"
          >
            {basics.email}
          </a>{" "}
          | 🌐{" "}
          <a href={basics.website} className="underline text-blue-700">
            Portfolio
          </a>
        </div>
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <SectionCard title="Summary">
            <div dangerouslySetInnerHTML={{ __html: basics.summary }} />
          </SectionCard>

          {skills?.length > 0 && (
            <SectionCard title="Skills">
              <ul className="list-disc pl-5">
                {skills.map((skill, i) => (
                  <li key={i}>
                    {skill.Skill}{" "}
                    {skill.proficiency && (
                      <span className="text-xs text-gray-600 ml-1">
                        ({skill.proficiency})
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </SectionCard>
          )}

          {projects?.length > 0 && (
            <SectionCard title="Projects">
              {projects.map((proj, i) => (
                <div key={i} className="mb-3">
                  <p className="font-semibold">{proj.Name}</p>
                  <p className="text-sm text-gray-600">{proj.Description}</p>
                  {proj.Website && (
                    <a
                      href={proj.Website}
                      target="_blank"
                      rel="noreferrer"
                      className="text-xs text-blue-600 underline"
                    >
                      View Project
                    </a>
                  )}
                </div>
              ))}
            </SectionCard>
          )}

          {languages?.length > 0 && (
            <SectionCard title="Languages">
              <ul className="list-disc pl-5">
                {languages.map((lang, idx) => (
                  <li key={idx}>
                    {lang.Name} –{" "}
                    <span className="text-gray-600">{lang.Proficiency}</span>
                  </li>
                ))}
              </ul>
            </SectionCard>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          {experiences?.length > 0 && (
            <SectionCard title="Experience">
              {experiences.map((exp, i) => (
                <div key={i} className="mb-3">
                  <p className="font-semibold">{exp.Position}</p>
                  <p className="text-sm text-gray-600">
                    {exp.Company} —{" "}
                    {exp.StartDate && exp.EndDate && (
                      <>
                        {new Date(exp.StartDate).toLocaleDateString("en-GB", {
                          month: "short",
                          year: "numeric",
                        })}{" "}
                        -{" "}
                        {new Date(exp.EndDate).toLocaleDateString("en-GB", {
                          month: "short",
                          year: "numeric",
                        })}
                      </>
                    )}
                  </p>
                  <p className="text-sm italic text-gray-700">{exp.Location}</p>
                  <div
                    className="text-sm"
                    dangerouslySetInnerHTML={{ __html: exp.Description }}
                  />
                </div>
              ))}
            </SectionCard>
          )}

          {education?.length > 0 && (
            <SectionCard title="Education">
              {education.map((edu, i) => (
                <div key={i} className="mb-3">
                  <p className="font-semibold">{edu.Institution}</p>
                  <p className="text-sm text-gray-600">
                    {edu.Degree} ({edu.Score}) —{" "}
                    {edu.StartDate &&
                      new Date(edu.StartDate).toLocaleDateString("en-GB", {
                        year: "numeric",
                      })}{" "}
                    to{" "}
                    {edu.EndDate &&
                      new Date(edu.EndDate).toLocaleDateString("en-GB", {
                        year: "numeric",
                      })}
                  </p>
                  <p className="text-sm italic text-gray-600">{edu.Location}</p>
                </div>
              ))}
            </SectionCard>
          )}

          {awards?.length > 0 && (
            <SectionCard title="Awards">
              {awards.map((award, i) => (
                <div key={i} className="text-sm text-gray-800">
                  🏆 {award.Name} —{" "}
                  <span className="italic">{award.Awarder}</span>
                </div>
              ))}
            </SectionCard>
          )}

          {certifications?.length > 0 && (
            <SectionCard title="Certifications">
              {certifications.map((cert, i) => (
                <div key={i} className="text-sm text-gray-800">
                  📜 {cert.Name} – {cert.Issuer}
                </div>
              ))}
            </SectionCard>
          )}

          {profiles?.length > 0 && (
            <SectionCard title="Profiles">
              {profiles.map((profile, i) => (
                <div key={i} className="text-sm">
                  {profile.Network}:{" "}
                  <a
                    href={profile.ProfileLink}
                    className="text-blue-600 underline"
                    target="_blank"
                    rel="noreferrer"
                  >
                    {profile.Username}
                  </a>
                </div>
              ))}
            </SectionCard>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernGridTemplate;
