<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

session_start();

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);

// Get user resumes
$db->query("SELECT r.*, t.template_name, t.preview_image 
            FROM user_resumes r 
            JOIN resume_templates t ON r.template_id = t.id 
            WHERE r.user_id = :user_id 
            ORDER BY r.updated_at DESC");
$db->bind(':user_id', $user_id);
$resumes = $db->resultSet();

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $resume_id = (int)$_GET['id'];
    
    // Check if resume belongs to user
    $db->query("SELECT id FROM user_resumes WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $resume_id);
    $db->bind(':user_id', $user_id);
    $resume = $db->single();
    
    if ($resume) {
        // Delete resume
        $db->query("DELETE FROM user_resumes WHERE id = :id");
        $db->bind(':id', $resume_id);
        
        if ($db->execute()) {
            setMessage('Resume deleted successfully.', 'success');
        } else {
            setMessage('Failed to delete resume.', 'error');
        }
    } else {
        setMessage('Resume not found.', 'error');
    }
    
    redirect('my-resumes.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Resumes - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/my-resumes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="dashboard-container">
        <div class="sidebar">
            <div class="user-info">
                <div class="user-avatar">
                    <?php if (!empty($profile['profile_image'])): ?>
                        <img src="<?php echo UPLOAD_DIR . 'profiles/' . $profile['profile_image']; ?>" alt="Profile Image">
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <h3><?php echo !empty($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : $_SESSION['username']; ?></h3>
                <p><?php echo !empty($profile['title']) ? $profile['title'] : 'Complete your profile'; ?></p>
            </div>
            
            <nav class="sidebar-nav">
                <ul style="gap:0">
                    <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                    <li><a href="skills.php"><i class="fas fa-star"></i> Skills</a></li>
                    <li><a href="experience.php"><i class="fas fa-briefcase"></i> Experience</a></li>
                    <li><a href="education.php"><i class="fas fa-graduation-cap"></i> Education</a></li>
                    <li><a href="projects.php"><i class="fas fa-project-diagram"></i> Projects</a></li>
                    <li><a href="certifications.php"><i class="fas fa-certificate"></i> Certifications</a></li>
                    <li><a href="templates.php"><i class="fas fa-file-alt"></i> Resume Templates</a></li>
                    <li class="active"><a href="my-resumes.php"><i class="fas fa-file"></i> My Resumes</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>My Resumes</h1>
                <a href="templates.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Resume
                </a>
            </div>
            
            <?php echo displayMessage(); ?>
            
            <?php if (empty($resumes)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h2>No Resumes Yet</h2>
                    <p>You haven't created any resumes yet. Get started by choosing a template.</p>
                    <a href="templates.php" class="btn btn-primary">Browse Templates</a>
                </div>
            <?php else: ?>
                <div class="resumes-grid">
                    <?php foreach ($resumes as $resume): ?>
                        <div class="resume-card">
                            <div class="resume-preview">
                                <img src="<?php echo $resume['preview_image']; ?>" alt="<?php echo $resume['resume_name']; ?>">
                                <div class="resume-overlay">
                                    <a href="view-resume.php?id=<?php echo $resume['id']; ?>" class="btn btn-sm btn-outline">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="edit-resume.php?resume_id=<?php echo $resume['id']; ?>&template_id=<?php echo $resume['template_id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </div>
                            </div>
                            <div class="resume-info">
                                <h3><?php echo $resume['resume_name']; ?></h3>
                                <p>Template: <?php echo $resume['template_name']; ?></p>
                                <div class="resume-meta">
                                    <span><i class="fas fa-clock"></i> <?php echo formatDate($resume['updated_at']); ?></span>
                                    <div class="resume-actions">
                                        <a href="download-resume.php?id=<?php echo $resume['id']; ?>" class="btn-icon" title="Download PDF">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="my-resumes.php?action=delete&id=<?php echo $resume['id']; ?>" class="btn-icon delete-resume" title="Delete Resume">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Confirm delete
            const deleteButtons = document.querySelectorAll('.delete-resume');
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('Are you sure you want to delete this resume? This action cannot be undone.')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
</body>
</html>

