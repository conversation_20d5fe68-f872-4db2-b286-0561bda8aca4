import React, { useState } from "react";

const marginOptions = [
  { label: "Narrow", value: "narrow", desc: "0.5 inch" },
  { label: "Normal", value: "normal", desc: "1 inch" },
  { label: "Wide", value: "wide", desc: "1.5 inch" },
  { label: "Custom", value: "custom", desc: "" },
];

const Page = () => {
  const [margin, setMargin] = useState("normal");
  const [customMargin, setCustomMargin] = useState(1);
  const [showPageNumbers, setShowPageNumbers] = useState(true);
  const [showPageBreakdown, setShowPageBreakdown] = useState(false);

  return (
    <div className="p-4 bg-white rounded-xl shadow-md max-w-sm space-y-4">
      <h2 className="text-lg font-bold text-[#29354d]">Page Settings</h2>

      <div>
        <label className="block font-semibold text-sm text-gray-700">
          Page Format
        </label>
        <div className="flex gap-2 mt-2">
          {["A4", "Letter", "Legal"].map((format) => (
            <span
              key={format}
              className="px-3 py-1 rounded border font-semibold text-sm bg-gray-100"
            >
              {format}
            </span>
          ))}
        </div>
      </div>

      <div>
        <label className="block font-semibold text-sm text-gray-700">
          Margin Options
        </label>
        <div className="flex flex-wrap gap-2 mt-2">
          {marginOptions.map((opt) => (
            <label
              key={opt.value}
              className={`flex items-center cursor-pointer rounded border px-2 py-1 text-sm 
                ${
                  margin === opt.value
                    ? "bg-[#fcc250] border-[#29354d] text-[#29354d]"
                    : "bg-gray-100 border-gray-300 text-gray-700"
                }`}
            >
              <input
                type="radio"
                name="margin"
                value={opt.value}
                checked={margin === opt.value}
                onChange={() => setMargin(opt.value)}
                className="mr-1"
              />
              {opt.label}
              {opt.desc && (
                <span className="ml-1 text-xs text-gray-500">({opt.desc})</span>
              )}
            </label>
          ))}
        </div>
        {margin === "custom" && (
          <div className="mt-2">
            <label className="block text-sm text-gray-700">
              Custom Margin (inches):
            </label>
            <input
              type="number"
              min={0}
              step={0.1}
              value={customMargin}
              onChange={(e) => setCustomMargin(parseFloat(e.target.value))}
              className="border rounded px-2 py-1 mt-1 w-20"
            />
          </div>
        )}
      </div>

      <div className="flex flex-col space-y-2">
        <label className="flex items-center text-sm cursor-pointer">
          <input
            type="checkbox"
            checked={showPageNumbers}
            onChange={() => setShowPageNumbers((v) => !v)}
            className="mr-2"
          />
          Show Page Numbers
        </label>
        <label className="flex items-center text-sm cursor-pointer">
          <input
            type="checkbox"
            checked={showPageBreakdown}
            onChange={() => setShowPageBreakdown((v) => !v)}
            className="mr-2"
          />
          Show Page Breakdown
        </label>
      </div>

      <div className="mt-4">
        <div className="border rounded-lg bg-gray-50 p-3 relative">
          <div className="text-gray-500 text-xs">
            Format: <b>A4</b> | Margin:{" "}
            <b>
              {margin === "custom"
                ? `${customMargin}"`
                : marginOptions.find((o) => o.value === margin)?.desc}
            </b>
          </div>
          <div className="h-28 bg-white border border-dashed border-gray-300 flex items-center justify-center text-gray-300 text-2xl rounded mt-2">
            Page Preview
          </div>
          {showPageNumbers && (
            <div className="absolute bottom-2 right-3 text-xs text-gray-400">
              1
            </div>
          )}
          {showPageBreakdown && (
            <div className="absolute top-2 right-3 text-xs text-[#29354d] bg-[#fcc250] px-2 py-1 rounded">
              Page Breakdown
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Page;
