import React, { useEffect, useState } from "react";
import { FaPlus, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const EducationModal = ({ initial, onClose, onSubmit }) => {
  const [form, setForm] = useState({
    institution: "",
    degree: "",
    location: "",
    startDate: null,
    endDate: null,
    summary: "",
  });

  useEffect(() => {
    if (initial) {
      setForm({
        institution: initial.Institution || "",
        degree: initial.Degree || "",
        location: initial.Location || "",
        startDate: initial.StartDate ? new Date(initial.StartDate) : null,
        endDate: initial.EndDate ? new Date(initial.EndDate) : null,
        summary: initial.Summary || "",
      });
    }
  }, [initial]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));
  };

  const handleSubmit = () => {
    const { institution, degree, location, startDate } = form;
    if (!institution || !degree || !location || !startDate) {
      alert("Please fill all required fields");
      return;
    }

    const payload = {
      Institution: form.institution,
      Degree: form.degree,
      Location: form.location,
      StartDate: startDate?.toISOString().split("T")[0],
      EndDate: form.endDate ? form.endDate.toISOString().split("T")[0] : "",
      Summary: form.summary,
    };

    onSubmit(payload, initial?._id || null);
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
      <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
        >
          <FaTimes size={20} />
        </button>
        <h2 className="text-xl font-bold mb-6 text-[#29354d] flex items-center gap-2">
          <FaPlus /> {initial ? "Update Education" : "Add New Education"}
        </h2>

        <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
          <input
            name="institution"
            placeholder="Institution"
            value={form.institution}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2"
          />
          <input
            name="degree"
            placeholder="Degree"
            value={form.degree}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2"
          />
          <input
            name="location"
            placeholder="Location"
            value={form.location}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2"
          />

          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm mb-1 block">Start Date</label>
              <DatePicker
                selected={form.startDate}
                onChange={(date) => setForm((f) => ({ ...f, startDate: date }))}
                dateFormat="yyyy-MM-dd"
                className="w-full border border-gray-300 rounded px-3 py-2"
              />
            </div>
            <div className="flex-1">
              <label className="text-sm mb-1 block">End Date</label>
              <DatePicker
                selected={form.endDate}
                onChange={(date) => setForm((f) => ({ ...f, endDate: date }))}
                dateFormat="yyyy-MM-dd"
                isClearable
                className="w-full border border-gray-300 rounded px-3 py-2"
              />
            </div>
          </div>

          <textarea
            name="summary"
            placeholder="Summary"
            value={form.summary}
            onChange={handleChange}
            className="w-full min-h-[80px] border border-gray-300 rounded px-3 py-2"
          />

          <div className="flex justify-end">
            <button
              type="submit"
              onClick={handleSubmit}
              className="bg-[#73716c] hover:bg-[#000000] text-[#f9f9f9]  font-semibold px-6 py-2 rounded-md transition"
            >
              {initial ? "Update" : "Create"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EducationModal;
