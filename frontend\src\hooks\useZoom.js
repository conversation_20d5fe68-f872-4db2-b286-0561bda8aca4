// hooks/useZoom.js
import { useState, useEffect } from "react";

const ZOOM_KEY = "resume_zoom";

export const useZoom = (initial = 1) => {
    const [zoom, setZoom] = useState(() => {
        const saved = localStorage.getItem(ZOOM_KEY);
        return saved ? parseFloat(saved) : initial;
    });

    useEffect(() => {
        localStorage.setItem(ZOOM_KEY, zoom.toString());
    }, [zoom]);

    const increaseZoom = () => setZoom((z) => Math.min(z + 0.1, 2));
    const decreaseZoom = () => setZoom((z) => Math.max(z - 0.1, 0.5));
    const resetZoom = () => setZoom(1);

    return { zoom, increaseZoom, decreaseZoom, resetZoom, setZoom };
};
