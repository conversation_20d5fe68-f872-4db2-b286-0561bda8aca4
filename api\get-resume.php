<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

// Check if resume ID is provided
if (!isset($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Resume ID is required'
    ]);
    exit;
}

$resume_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Get resume from database
$db->query("SELECT r.*, t.name, t.category, t.html_structure, t.css_styles 
            FROM resumes r 
            JOIN templates t ON r.template_id = t.id 
            WHERE r.id = :id AND r.user_id = :user_id");
$db->bind(':id', $resume_id);
$db->bind(':user_id', $user_id);
$resume = $db->single();

if (!$resume) {
    echo json_encode([
        'success' => false,
        'message' => 'Resume not found or access denied'
    ]);
    exit;
}

// Parse content JSON
$resume['content'] = json_decode($resume['content'], true);

// Return resume data
echo json_encode([
    'success' => true,
    'resume' => $resume
]);
?>
