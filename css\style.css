/* Global Styles - Enhanced Version */
:root {
    --primary: rgb(25,65,75);
    --primary-dark: #3a56d4;
    --primary-light: #eef1ff;
    --secondary: #7209b7;
    --secondary-dark: #6008a0;
    --secondary-light: #f3e8ff;
    --dark: #1a1a2e;
    --light: #f8f9fa;
    --gray: #6c757d;
    --gray-light: #e9ecef;
    --gray-dark: #343a40;
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
  
    --blue: #4361ee;
    --purple: #7209b7;
    --pink: #f72585;
    --orange: #ff9e00;
    --green: #38b000;
    --teal: #3a86ff;
    --red: #e5383b;
    --black: #212529;
    --navy: #0a2463;
    --maroon: #7a0000;
    --brown: #6b4226;
  
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 2rem;
    
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.12);
    --shadow-inner: inset 0 2px 5px rgba(0, 0, 0, 0.05);
    
    --transition-fast: all 0.2s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-mono: 'JetBrains Mono', 'SF Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  }
  
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Poppins:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark);
    background-color: #f5f7fa;
    overflow-x: hidden;
  }
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }
  
  .section {
    padding: 6rem 0;
    position: relative;
  }
  
  .section-sm {
    padding: 3rem 0;
  }
  
  .section-lg {
    padding: 8rem 0;
  }
  
  .text-center {
    text-align: center;
  }
  
  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.2;
    font-family: var(--font-secondary);
  }
  
  h1 {
    font-size: 3rem;
    letter-spacing: -0.02em;
  }
  
  h2 {
    font-size: 2.25rem;
    letter-spacing: -0.01em;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  h4 {
    font-size: 1.5rem;
  }
  
  h5 {
    font-size: 1.25rem;
  }
  
  h6 {
    font-size: 1rem;
  }
  
  p {
    margin-bottom: 1rem;
  }
  
  .lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.5;
  }
  
  .text-small {
    font-size: 0.875rem;
  }
  
  .text-xs {
    font-size: 0.75rem;
  }
  
  a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
  }
  
  a:hover {
    color: var(--primary-dark);
  }
  
  .text-primary { color: var(--primary); }
  .text-secondary { color: var(--secondary); }
  .text-dark { color: var(--dark); }
  .text-light { color: var(--light); }
  .text-gray { color: var(--gray); }
  .text-success { color: var(--success); }
  .text-danger { color: var(--danger); }
  .text-warning { color: var(--warning); }
  .text-info { color: var(--info); }
  
  .bg-primary { background-color: var(--primary); }
  .bg-primary-light { background-color: var(--primary-light); }
  .bg-secondary { background-color: var(--secondary); }
  .bg-secondary-light { background-color: var(--secondary-light); }
  .bg-dark { background-color: var(--dark); }
  .bg-light { background-color: var(--light); }
  .bg-gray { background-color: var(--gray); }
  .bg-gray-light { background-color: var(--gray-light); }
  .bg-success { background-color: var(--success); }
  .bg-danger { background-color: var(--danger); }
  .bg-warning { background-color: var(--warning); }
  .bg-info { background-color: var(--info); }
  
  /* Buttons */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
    z-index: 1;
  }
  
  .btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.5s ease;
  }
  
  .btn:hover::after {
    transform: scaleX(1);
    transform-origin: left;
  }
  
  .btn i, .btn svg {
    margin-right: 0.5rem;
  }
  
  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
  }
  
  .btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius-lg);
  }
  
  .btn-block {
    display: flex;
    width: 100%;
  }
  
  .btn-primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 14px rgba(67, 97, 238, 0.3);
  }
  
  .btn-primary:hover {
    background-color: var(--primary-dark);
    color: white;
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
    transform: translateY(-2px);
  }
  
  .btn-secondary {
    background-color: var(--secondary);
    color: white;
    box-shadow: 0 4px 14px rgba(114, 9, 183, 0.3);
  }
  
  .btn-secondary:hover {
    background-color: var(--secondary-dark);
    color: white;
    box-shadow: 0 6px 20px rgba(114, 9, 183, 0.4);
    transform: translateY(-2px);
  }
  
  .btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
  }
  
  .btn-outline:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
  }
  
  .btn-outline-secondary {
    background-color: transparent;
    border: 2px solid var(--secondary);
    color: var(--secondary);
  }
  
  .btn-outline-secondary:hover {
    background-color: var(--secondary);
    color: white;
    transform: translateY(-2px);
  }
  
  .btn-light {
    background-color: white;
    color: var(--dark);
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  }
  
  .btn-light:hover {
    background-color: #f8f9fa;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .btn-dark {
    background-color: var(--dark);
    color: white;
  }
  
  .btn-dark:hover {
    background-color: #000;
    transform: translateY(-2px);
  }
  
  .btn-danger {
    background-color: var(--danger);
    color: white;
  }
  
  .btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-2px);
  }
  
  .btn-success {
    background-color: var(--success);
    color: white;
  }
  
  .btn-success:hover {
    background-color: #218838;
    transform: translateY(-2px);
  }
  
  .btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
  
  .btn-icon.btn-sm {
    width: 32px;
    height: 32px;
  }
  
  .btn-icon.btn-lg {
    width: 48px;
    height: 48px;
  }
  
  .btn-icon i, .btn-icon svg {
    margin-right: 0;
  }
  
  .btn.disabled, .btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  .btn-group {
    display: inline-flex;
    border-radius: var(--border-radius);
    overflow: hidden;
  }
  
  .btn-group .btn {
    border-radius: 0;
    margin: 0;
  }
  
  .btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
  }
  
  .btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  }
  
  /* Forms */
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
  }
  
  .form-row > .form-group {
    flex: 1;
    padding: 0 0.75rem;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
  }
  
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="date"],
  input[type="tel"],
  input[type="url"],
  input[type="number"],
  input[type="search"],
  select,
  textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background-color: white;
    color: var(--dark);
    font-family: var(--font-primary);
  }
  
  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="date"]:focus,
  input[type="tel"]:focus,
  input[type="url"]:focus,
  input[type="number"]:focus,
  input[type="search"]:focus,
  select:focus,
  textarea:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  }
  
  .input-group {
    position: relative;
    display: flex;
    align-items: stretch;
    width: 100%;
  }
  
  .input-group input,
  .input-group select,
  .input-group textarea {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
  }
  
  .input-group-prepend,
  .input-group-append {
    display: flex;
  }
  
  .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--gray);
    text-align: center;
    white-space: nowrap;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
  }
  
  .input-group-prepend .input-group-text {
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
  }
  
  .input-group-append .input-group-text {
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  }
  
  .input-group > input:not(:first-child),
  .input-group > select:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  
  .input-group > input:not(:last-child),
  .input-group > select:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  
  .input-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 1rem;
    color: var(--gray);
    pointer-events: none;
  }
  
  .input-icon ~ input {
    padding-left: 2.5rem;
  }
  
  .toggle-password {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 1rem;
    cursor: pointer;
    color: var(--gray);
    z-index: 10;
  }
  
  .form-check {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .form-check input[type="checkbox"],
  .form-check input[type="radio"] {
    margin-right: 0.5rem;
  }
  
  .custom-checkbox {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    user-select: none;
    display: inline-block;
  }
  
  .custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: var(--transition);
  }
  
  .custom-checkbox:hover input ~ .checkmark {
    border-color: var(--primary);
  }
  
  .custom-checkbox input:checked ~ .checkmark {
    background-color: var(--primary);
    border-color: var(--primary);
  }
  
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  
  .custom-checkbox input:checked ~ .checkmark:after {
    display: block;
  }
  
  .custom-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  
  .form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .form-hint {
    font-size: 0.875rem;
    color: var(--gray);
    margin-top: 0.25rem;
  }
  
  .form-error {
    font-size: 0.875rem;
    color: var(--danger);
    margin-top: 0.25rem;
  }
  
  /* Alerts */
  .alert {
    position: relative;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
  }
  
  .alert-icon {
    margin-right: 1rem;
    font-size: 1.25rem;
  }
  
  .alert-content {
    flex: 1;
  }
  
  .alert-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.5;
    transition: var(--transition);
  }
  
  .alert-close:hover {
    opacity: 1;
  }
  
  .alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
  }
  
  .alert-error, .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border-left: 4px solid var(--danger);
  }
  
  .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
    border-left: 4px solid var(--warning);
  }
  
  .alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
    border-left: 4px solid var(--info);
  }
  
  .alert ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
  }
  
  /* Cards */
  .card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
  }
  
  .card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
  }
  
  .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: rgba(0, 0, 0, 0.02);
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .card-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background-color: rgba(0, 0, 0, 0.02);
  }
  
  .card-img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  .card-img-top {
    width: 100%;
    height: auto;
    display: block;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
  }
  
  .card-img-bottom {
    width: 100%;
    height: auto;
    display: block;
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
  }
  
  .card-title {
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
  }
  
  .card-subtitle {
    margin-top: -0.375rem;
    margin-bottom: 0.75rem;
    color: var(--gray);
  }
  
  .card-text:last-child {
    margin-bottom: 0;
  }
  
  .card-link + .card-link {
    margin-left: 1.25rem;
  }
  
  /* Badges */
  .badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    transition: var(--transition);
  }
  
  .badge-primary {
    background-color: var(--primary);
    color: white;
  }
  
  .badge-secondary {
    background-color: var(--secondary);
    color: white;
  }
  
  .badge-success {
    background-color: var(--success);
    color: white;
  }
  
  .badge-danger {
    background-color: var(--danger);
    color: white;
  }
  
  .badge-warning {
    background-color: var(--warning);
    color: #212529;
  }
  
  .badge-info {
    background-color: var(--info);
    color: white;
  }
  
  .badge-light {
    background-color: var(--light);
    color: #212529;
  }
  
  .badge-dark {
    background-color: var(--dark);
    color: white;
  }
  

  /* Tooltips */
  .tooltip {
    position: relative;
    display: inline-block;
  }
  
  .tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.75rem;
  }
  
  .tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark) transparent transparent transparent;
  }
  
  .tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
  }
  
  /* Utilities */
  .mt-1 { margin-top: 0.25rem; }
  .mt-2 { margin-top: 0.5rem; }
  .mt-3 { margin-top: 1rem; }
  .mt-4 { margin-top: 1.5rem; }
  .mt-5 { margin-top: 3rem; }
  
  .mb-1 { margin-bottom: 0.25rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-3 { margin-bottom: 1rem; }
  .mb-4 { margin-bottom: 1.5rem; }
  .mb-5 { margin-bottom: 3rem; }
  
  .ml-1 { margin-left: 0.25rem; }
  .ml-2 { margin-left: 0.5rem; }
  .ml-3 { margin-left: 1rem; }
  .ml-4 { margin-left: 1.5rem; }
  .ml-5 { margin-left: 3rem; }
  
  .mr-1 { margin-right: 0.25rem; }
  .mr-2 { margin-right: 0.5rem; }
  .mr-3 { margin-right: 1rem; }
  .mr-4 { margin-right: 1.5rem; }
  .mr-5 { margin-right: 3rem; }
  
  .mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
  .mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
  .mx-3 { margin-left: 1rem; margin-right: 1rem; }
  .mx-4 { margin-left: 1.5rem; margin-right: 1.5rem; }
  .mx-5 { margin-left: 3rem; margin-right: 3rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }
  
  .my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
  .my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
  .my-3 { margin-top: 1rem; margin-bottom: 1rem; }
  .my-4 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
  .my-5 { margin-top: 3rem; margin-bottom: 3rem; }
  
  .p-1 { padding: 0.25rem; }
  .p-2 { padding: 0.5rem; }
  .p-3 { padding: 1rem; }
  .p-4 { padding: 1.5rem; }
  .p-5 { padding: 3rem; }
  
  .pt-1 { padding-top: 0.25rem; }
  .pt-2 { padding-top: 0.5rem; }
  .pt-3 { padding-top: 1rem; }
  .pt-4 { padding-top: 1.5rem; }
  .pt-5 { padding-top: 3rem; }
  
  .pb-1 { padding-bottom: 0.25rem; }
  .pb-2 { padding-bottom: 0.5rem; }
  .pb-3 { padding-bottom: 1rem; }
  .pb-4 { padding-bottom: 1.5rem; }
  .pb-5 { padding-bottom: 3rem; }
  
  .pl-1 { padding-left: 0.25rem; }
  .pl-2 { padding-left: 0.5rem; }
  .pl-3 { padding-left: 1rem; }
  .pl-4 { padding-left: 1.5rem; }
  .pl-5 { padding-left: 3rem; }
  
  .pr-1 { padding-right: 0.25rem; }
  .pr-2 { padding-right: 0.5rem; }
  .pr-3 { padding-right: 1rem; }
  .pr-4 { padding-right: 1.5rem; }
  .pr-5 { padding-right: 3rem; }
  
  .px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
  .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
  .px-3 { padding-left: 1rem; padding-right: 1rem; }
  .px-4 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .px-5 { padding-left: 3rem; padding-right: 3rem; }
  
  .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
  .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .py-3 { padding-top: 1rem; padding-bottom: 1rem; }
  .py-4 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
  .py-5 { padding-top: 3rem; padding-bottom: 3rem; }
  
  .text-center { text-align: center; }
  .text-left { text-align: left; }
  .text-right { text-align: right; }
  .text-justify { text-align: justify; }
  
  .d-flex { display: flex; }
  .d-inline-flex { display: inline-flex; }
  .d-block { display: block; }
  .d-inline-block { display: inline-block; }
  .d-inline { display: inline; }
  .d-none { display: none; }
  
  .flex-row { flex-direction: row; }
  .flex-column { flex-direction: column; }
  .flex-row-reverse { flex-direction: row-reverse; }
  .flex-column-reverse { flex-direction: column-reverse; }
  
  .flex-wrap { flex-wrap: wrap; }
  .flex-nowrap { flex-wrap: nowrap; }
  .flex-wrap-reverse { flex-wrap: wrap-reverse; }
  
  .justify-content-start { justify-content: flex-start; }
  .justify-content-end { justify-content: flex-end; }
  .justify-content-center { justify-content: center; }
  .justify-content-between { justify-content: space-between; }
  .justify-content-around { justify-content: space-around; }
  .justify-content-evenly { justify-content: space-evenly; }
  
  .align-items-start { align-items: flex-start; }
  .align-items-end { align-items: flex-end; }
  .align-items-center { align-items: center; }
  .align-items-baseline { align-items: baseline; }
  .align-items-stretch { align-items: stretch; }
  
  .align-self-start { align-self: flex-start; }
  .align-self-end { align-self: flex-end; }
  .align-self-center { align-self: center; }
  .align-self-baseline { align-self: baseline; }
  .align-self-stretch { align-self: stretch; }
  
  .flex-grow-0 { flex-grow: 0; }
  .flex-grow-1 { flex-grow: 1; }
  .flex-shrink-0 { flex-shrink: 0; }
  .flex-shrink-1 { flex-shrink: 1; }
  
  .w-25 { width: 25%; }
  .w-50 { width: 50%; }
  .w-75 { width: 75%; }
  .w-100 { width: 100%; }
  .w-auto { width: auto; }
  
  .h-25 { height: 25%; }
  .h-50 { height: 50%; }
  .h-75 { height: 75%; }
  .h-100 { height: 100%; }
  .h-auto { height: auto; }
  
  .mw-100 { max-width: 100%; }
  .mh-100 { max-height: 100%; }
  
  .min-vw-100 { min-width: 100vw; }
  .min-vh-100 { min-height: 100vh; }
  
  .vw-100 { width: 100vw; }
  .vh-100 { height: 100vh; }
  
  .position-static { position: static; }
  .position-relative { position: relative; }
  .position-absolute { position: absolute; }
  .position-fixed { position: fixed; }
  .position-sticky { position: sticky; }
  
  .top-0 { top: 0; }
  .right-0 { right: 0; }
  .bottom-0 { bottom: 0; }
  .left-0 { left: 0; }
  
  .shadow-none { box-shadow: none !important; }
  .shadow-sm { box-shadow: var(--shadow-sm) !important; }
  .shadow { box-shadow: var(--shadow) !important; }
  .shadow-lg { box-shadow: var(--shadow-lg) !important; }
  
  .rounded { border-radius: var(--border-radius); }
  .rounded-sm { border-radius: var(--border-radius-sm); }
  .rounded-lg { border-radius: var(--border-radius-lg); }
  .rounded-xl { border-radius: var(--border-radius-xl); }
  .rounded-circle { border-radius: 50%; }
  .rounded-pill { border-radius: 50rem; }
  .rounded-0 { border-radius: 0; }
  
  .visible { visibility: visible; }
  .invisible { visibility: hidden; }
  
  .overflow-auto { overflow: auto; }
  .overflow-hidden { overflow: hidden; }
  .overflow-visible { overflow: visible; }
  .overflow-scroll { overflow: scroll; }
  
  .z-0 { z-index: 0; }
  .z-10 { z-index: 10; }
  .z-20 { z-index: 20; }
  .z-30 { z-index: 30; }
  .z-40 { z-index: 40; }
  .z-50 { z-index: 50; }
  .z-auto { z-index: auto; }
  
  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .animate-fadeIn { animation: fadeIn 0.5s ease forwards; }
  .animate-fadeInUp { animation: fadeInUp 0.5s ease forwards; }
  .animate-fadeInDown { animation: fadeInDown 0.5s ease forwards; }
  .animate-fadeInLeft { animation: fadeInLeft 0.5s ease forwards; }
  .animate-fadeInRight { animation: fadeInRight 0.5s ease forwards; }
  .animate-pulse { animation: pulse 2s infinite; }
  .animate-spin { animation: spin 1s linear infinite; }
  
  /* Responsive */
  @media (max-width: 1200px) {
    .container {
      max-width: 960px;
    }
    
    h1 {
      font-size: 2.75rem;
    }
    
    h2 {
      font-size: 2rem;
    }
    
    h3 {
      font-size: 1.5rem;
    }
    
    .section {
      padding: 5rem 0;
    }
  }
  
  @media (max-width: 992px) {
    .container {
      max-width: 720px;
    }
    
    h1 {
      font-size: 2.5rem;
    }
    
    h2 {
      font-size: 1.75rem;
    }
    
    h3 {
      font-size: 1.35rem;
    }
    
    .section {
      padding: 4rem 0;
    }
    
    .section-lg {
      padding: 6rem 0;
    }
  }
  
  @media (max-width: 768px) {
    .container {
      max-width: 540px;
    }
    
    h1 {
      font-size: 2.25rem;
    }
    
    h2 {
      font-size: 1.5rem;
    }
    
    h3 {
      font-size: 1.25rem;
    }
    
    .section {
      padding: 3rem 0;
    }
    
    .section-lg {
      padding: 5rem 0;
    }
    
    .form-row {
      flex-direction: column;
    }
    
    .form-row > .form-group {
      padding: 0;
    }
    
  }
  
  @media (max-width: 576px) {
    .container {
      padding: 0 1rem;
    }
    
    h1 {
      font-size: 2rem;
    }
    
    h2 {
      font-size: 1.35rem;
    }
    
    h3 {
      font-size: 1.15rem;
    }
    
    .btn {
      padding: 0.6rem 1.2rem;
    }
    
    .section {
      padding: 2.5rem 0;
    }
    
    .section-lg {
      padding: 4rem 0;
    }
    
    .form-actions {
      flex-direction: column;
      gap: 1rem;
    }
    
    .form-actions .btn {
      width: 100%;
    }
  }