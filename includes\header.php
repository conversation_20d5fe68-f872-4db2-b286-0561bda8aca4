<?php
    
    $username = isset($_SESSION['username']) ? $_SESSION['username'] : '';
?>

<!DOCTYPE html>
<html>
<head>
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        header {
            background-color: var(--white);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* padding: var(--spacing-sm) 0; */
            height: 70px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            text-decoration: none;
        }
        
        .logo i {
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--spacing-lg);
        }
        
        nav a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: var(--spacing-xs) 0;
            position: relative;
            font-size: 0.95rem;
        }
        
        nav a:hover {
            color: var(--primary);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary);
            transition: var(--transition);
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        .user-dropdown {
            position: relative;
            cursor: pointer;
        }
        
        .user-dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius);
            transition: var(--transition);
        }
        
        .user-dropdown-toggle:hover {
            background-color: var(--light);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-weight: 600;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            width: 200px;
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
            z-index: 100;
        }
        
        .user-dropdown.active .user-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-menu a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--dark);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .user-dropdown-menu a:hover {
            background-color: var(--light);
            color: var(--primary);
        }
        
        .user-dropdown-menu .divider {
            height: 1px;
            background-color: var(--light-gray);
            margin: 0.5rem 0;
        }
        
        .user-dropdown-menu .logout {
            color: var(--danger);
        }
        
    </style>
</head>
<body>
<header>
    <div class="container header-container">
        <a href="index.php" class="logo">
            <i class="fas fa-file-alt"></i>
            <span>Medini</span>
        </a>
        
        <nav>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="templates.php">Templates</a></li>
                <li><a href="about.php">About us</a></li>
                <!-- <li><a href="blog.php">Resources</a></li> -->
            </ul>
        </nav>
        
        <?php if(isset($_SESSION['user_id'])): ?>
            <!-- User is logged in - show profile dropdown -->
            <div class="user-dropdown">
                <div class="user-dropdown-toggle">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span><?php echo $username; ?></span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="user-dropdown-menu">
                    <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    <a href="my-resumes.php"><i class="fas fa-file"></i> My Resumes</a>
                    <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    <div class="divider"></div>
                    <a href="logout.php" class="logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </div>
        <?php else: ?>
            <!-- User is not logged in - show login/signup buttons -->
            <div class="auth-buttons">
                <a href="login.php" class="btn btn-outline">Log In</a>
                <a href="register.php" class="btn btn-primary">Sign Up</a>
            </div>
        <?php endif; ?>
        
    </div>
</header>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update current year
            document.getElementById('current-year').textContent = new Date().getFullYear();
            
            // User dropdown toggle
            const userDropdown = document.querySelector('.user-dropdown');
            const userDropdownToggle = document.querySelector('.user-dropdown-toggle');
            
            if (userDropdownToggle) {
                userDropdownToggle.addEventListener('click', function() {
                    userDropdown.classList.toggle('active');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!userDropdown.contains(event.target)) {
                        userDropdown.classList.remove('active');
                    }
                });
            }
            
            // Sidebar toggle
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    
                    // Update toggle icon
                    const icon = this.querySelector('i');
                    if (sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-left');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-left');
                    }
                });
            }
            
            // Mobile sidebar toggle
            const mobileSidebarToggle = document.querySelector('.mobile-sidebar-toggle');
            const overlay = document.querySelector('.overlay');
            
            if (mobileSidebarToggle && sidebar && overlay) {
                mobileSidebarToggle.addEventListener('click', function() {
                    sidebar.classList.add('active');
                    overlay.classList.add('active');
                });
                
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }
            
            // Animate progress bar
            const progressBar = document.querySelector('.progress');
            if (progressBar) {
                setTimeout(() => {
                    progressBar.style.width = progressBar.parentElement.getAttribute('data-progress') || '75%';
                }, 300);
            }
            
            // Number counter animation
            const counters = document.querySelectorAll('.count-up');
            const speed = 200; // The lower the faster
            
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / speed;
                let count = 0;
                
                const updateCount = () => {
                    if (count < target) {
                        count += increment;
                        if (count > target) count = target;
                        counter.textContent = Math.floor(count);
                        requestAnimationFrame(updateCount);
                    }
                };
                
                updateCount();
            });
        });
    </script>
</body>
</html>