<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);

// Check if template ID is provided
if (!isset($_GET['template_id']) || empty($_GET['template_id'])) {
    $_SESSION['error'] = "No template selected";
    header("Location: templates.php");
    exit();
}

$template_id = $_GET['template_id'];

// Get template details
$db->query("SELECT * FROM resume_templates WHERE id = :id");
$db->bind(':id', $template_id);
$template = $db->single();

if (!$template) {
    $_SESSION['error'] = "Template not found";
    header("Location: templates.php");
    exit();
}

// Check if resume already exists for this template and user
$db->query("SELECT * FROM saved_resumes WHERE user_id = :user_id AND template_id = :template_id");
$db->bind(':user_id', $user_id);
$db->bind(':template_id', $template_id);
$existing_resume = $db->single();

$resume_data = [];
$resume_id = null;

if ($existing_resume) {
    $resume_id = $existing_resume['id'];
    $resume_data = json_decode($existing_resume['resume_data'], true);
} else {
    // Get user data to pre-populate the resume
    $resume_data = [
        'personal' => [
            'name' => $profile['first_name'] . ' ' . $profile['last_name'],
            'email' => $profile['email'],
            'phone' => $profile['phone'] ?? '',
            'address' => $profile['address'] ?? '',
            'title' => $profile['professional_title'] ?? '',
            'summary' => $profile['summary'] ?? '',
            'photo' => $profile['photo'] ?? ''
        ],
        'sections' => [
            'experience' => getUserWorkExperience($user_id),
            'education' => getUserEducation($user_id),
            'skills' => getUserSkills($user_id),
            'languages' => getUserLanguages($user_id) ?? [],
            'certifications' => getUserCertifications($user_id) ?? [],
            'projects' => getUserProjects($user_id) ?? []
        ],
        'settings' => [
            'theme_color' => '#19414B',
            'font_family' => 'Inter',
            'font_size' => 'medium',
            'spacing' => 'normal',
            'layout' => 'standard'
        ]
    ];
}

// Get all available fonts
$fonts = [
    'Inter' => "'Inter', sans-serif",
    'Roboto' => "'Roboto', sans-serif",
    'Open Sans' => "'Open Sans', sans-serif",
    'Montserrat' => "'Montserrat', sans-serif",
    'Lato' => "'Lato', sans-serif",
    'Poppins' => "'Poppins', sans-serif",
    'Georgia' => "Georgia, serif",
    'Playfair Display' => "'Playfair Display', serif",
    'Times New Roman' => "'Times New Roman', Times, serif"
];

// Get all section types
$section_types = [
    'experience' => 'Work Experience',
    'education' => 'Education',
    'skills' => 'Skills',
    'languages' => 'Languages',
    'certifications' => 'Certifications',
    'projects' => 'Projects',
    'interests' => 'Interests',
    'references' => 'References',
    'custom' => 'Custom Section'
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Resume - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css">
    <!-- Add Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&family=Montserrat:wght@300;400;500;700&family=Lato:wght@300;400;700&family=Poppins:wght@300;400;500;700&family=Playfair+Display:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Include color picker -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.css">
    <!-- Include rich text editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        :root {
            --primary: <?php echo $resume_data['settings']['theme_color'] ?? '#19414B'; ?>;
            --text-dark: #1a1a1a;
            --text-light: #666;
            --background: #ffffff;
            --border: #e0e0e0;
            --hover: #f5f5f5;
            --shadow: rgba(0,0,0,0.05);
            --transition: all 0.3s ease;
        }

        body {
            font-family: <?php echo $fonts[$resume_data['settings']['font_family'] ?? 'Inter']; ?>;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            background-color: #f9f9f9;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .resume-editor-container {
            display: flex;
            height: calc(100vh - 60px);
            position: relative;
        }

        /* Sidebar */
        .editor-sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid var(--border);
            height: 100%;
            overflow-y: auto;
            box-shadow: 2px 0 5px var(--shadow);
            z-index: 10;
        }

        .sidebar-section {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .sidebar-section h3 {
            color: var(--primary);
            margin: 0 0 1rem 0;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Main editor area */
        .editor-main {
            flex: 1;
            display: flex;
            background-color: #f0f0f0;
            position: relative;
        }

        .resume-canvas {
            width: 794px; /* A4 width at 96dpi */
            height: 1123px; /* A4 height at 96dpi */
            margin: auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
            transform-origin: top center;
        }

        /* Sections */
        .resume-section {
            border: 1px solid transparent;
            margin: 10px;
            padding: 10px;
            position: relative;
            transition: var(--transition);
            min-height: 50px;
        }

        .resume-section:hover {
            border: 1px dashed var(--primary);
        }

        .resume-section.active {
            border: 1px solid var(--primary);
            background-color: rgba(25,65,75,0.05);
        }

        .section-controls {
            position: absolute;
            top: -15px;
            right: 5px;
            background: var(--primary);
            border-radius: 3px;
            display: none;
            z-index: 5;
        }

        .resume-section:hover .section-controls {
            display: flex;
        }

        .section-control-btn {
            color: white;
            padding: 3px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }

        .section-control-btn:hover {
            background-color: rgba(255,255,255,0.2);
        }

        /* Toolbar */
        .editor-toolbar {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            height: 50px;
            background: white;
            box-shadow: 0 2px 5px var(--shadow);
            display: flex;
            align-items: center;
            padding: 0 1rem;
            z-index: 20;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }

        .toolbar-group span {
            font-size: 0.85rem;
            margin-right: 0.5rem;
            color: var(--text-light);
        }

        .toolbar-btn {
            padding: 5px 10px;
            background: none;
            border: 1px solid var(--border);
            border-radius: 4px;
            margin-right: 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .toolbar-btn:hover {
            background-color: var(--hover);
        }

        .toolbar-btn.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .toolbar-spacer {
            flex-grow: 1;
        }

        /* Form controls */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.35rem;
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(25,65,75,0.1);
        }

        select.form-control {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23666' viewBox='0 0 16 16'%3E%3Cpath d='M8 11.5l-6-6h12z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
            padding-right: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
            gap: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background-color: rgb(20, 55, 65);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-outline:hover {
            background-color: rgba(25,65,75,0.1);
        }

        /* Color picker */
        .color-option {
            height: 22px;
            width: 22px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .color-option.active {
            border-color: var(--text-dark);
        }

        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }

        /* Zoom controls */
        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            padding: 5px;
        }

        .zoom-btn {
            background: none;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            font-size: 16px;
            color: var(--text-light);
            transition: var(--transition);
        }

        .zoom-btn:hover {
            color: var(--primary);
        }

        .zoom-value {
            font-size: 14px;
            margin: 0 5px;
            min-width: 40px;
            text-align: center;
        }

        /* Section tabs */
        .section-tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 1rem;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .section-tab {
            padding: 0.5rem 1rem;
            white-space: nowrap;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: var(--transition);
            font-size: 0.9rem;
        }

        .section-tab:hover {
            color: var(--primary);
        }

        .section-tab.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
            font-weight: 500;
        }

        /* Section content */
        .section-content {
            display: none;
        }

        .section-content.active {
            display: block;
        }

        /* Add section button */
        .add-section-btn {
            width: 100%;
            padding: 0.75rem;
            margin-top: 1rem;
            background-color: transparent;
            border: 1px dashed var(--border);
            border-radius: 4px;
            color: var(--primary);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .add-section-btn:hover {
            background-color: rgba(25,65,75,0.05);
            border-color: var(--primary);
        }

        /* Quill editor overrides */
        .ql-toolbar.ql-snow, .ql-container.ql-snow {
            border-color: var(--border);
        }

        .ql-snow .ql-picker-options {
            max-height: 200px;
            overflow-y: auto;
        }

        .ql-editor {
            min-height: 100px;
        }

        /* Item list */
        .item-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .item {
            border: 1px solid var(--border);
            border-radius: 4px;
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .item-header {
            padding: 0.5rem;
            background-color: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .item-content {
            padding: 0.5rem;
            display: none;
        }

        .item-content.active {
            display: block;
        }

        /* Buttons */
        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-light);
            transition: var(--transition);
            padding: 3px;
        }

        .action-btn:hover {
            color: var(--primary);
        }

        .action-btn.delete:hover {
            color: #e53935;
        }

        /* Modal */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal-backdrop.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background-color: white;
            border-radius: 8px;
            width: 100%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal-backdrop.active .modal {
            transform: translateY(0);
        }

        .modal-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: 1.25rem;
            color: var(--primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-light);
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--primary);
        }

        .modal-body {
            padding: 1rem;
        }

        .modal-footer {
            padding: 1rem;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        /* Loading indicator */
        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .loading-indicator.active {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            border: 4px solid rgba(25,65,75,0.2);
            border-radius: 50%;
            border-top: 4px solid var(--primary);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .resume-editor-container {
                flex-direction: column;
                height: auto;
            }

            .editor-sidebar {
                width: 100%;
                height: auto;
                max-height: 300px;
                border-right: none;
                border-bottom: 1px solid var(--border);
            }

            .resume-canvas {
                transform: scale(0.8);
                margin: 0 auto;
            }

            .editor-toolbar {
                overflow-x: auto;
                padding: 0 0.5rem;
            }

            .toolbar-group {
                margin-right: 1rem;
                white-space: nowrap;
            }
        }

        @media (max-width: 576px) {
            .resume-canvas {
                transform: scale(0.6);
            }
        }

        /* Unsaved changes warning */
        .unsaved-warning {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: var(--primary);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 50;
            transform: translateY(100px);
            transition: transform 0.3s ease;
        }

        .unsaved-warning.active {
            transform: translateY(0);
        }

        .unsaved-warning button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            transition: var(--transition);
        }

        .unsaved-warning button:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="editor-toolbar">
        <div class="toolbar-group">
            <span>Theme:</span>
            <div class="color-palette">
                <div class="color-option active" data-color="#19414B" style="background-color: #19414B;"></div>
                <div class="color-option" data-color="#2C3E50" style="background-color: #2C3E50;"></div>
                <div class="color-option" data-color="#3498DB" style="background-color: #3498DB;"></div>
                <div class="color-option" data-color="#16A085" style="background-color: #16A085;"></div>
                <div class="color-option" data-color="#8E44AD" style="background-color: #8E44AD;"></div>
                <div class="color-option" data-color="#C0392B" style="background-color: #C0392B;"></div>
                <div class="color-option" data-color="#D35400" style="background-color: #D35400;"></div>
                <div class="color-option" data-color="#7F8C8D" style="background-color: #7F8C8D;"></div>
            </div>
        </div>
        
        <div class="toolbar-group">
            <span>Font:</span>
            <select id="font-family" class="toolbar-select">
                <?php foreach ($fonts as $name => $family): ?>
                    <option value="<?php echo $name; ?>" <?php echo ($resume_data['settings']['font_family'] ?? 'Inter') === $name ? 'selected' : ''; ?>><?php echo $name; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="toolbar-group">
            <span>Layout:</span>
            <button class="toolbar-btn active" data-layout="standard">Standard</button>
            <button class="toolbar-btn" data-layout="creative">Creative</button>
            <button class="toolbar-btn" data-layout="modern">Modern</button>
        </div>
        
        <div class="toolbar-spacer"></div>
        
        <div class="toolbar-group">
            <button id="save-resume" class="btn btn-primary">
                <i class="fas fa-save"></i> Save
            </button>
            <button id="download-pdf" class="btn btn-outline">
                <i class="fas fa-file-pdf"></i> Export PDF
            </button>
        </div>
    </div>
    
    <div class="resume-editor-container">
        <div class="editor-sidebar">
            <div class="sidebar-section">
                <h3>Sections</h3>
                <div class="section-tabs">
                    <div class="section-tab active" data-section="personal">Personal</div>
                    <div class="section-tab" data-section="sections">Sections</div>
                    <div class="section-tab" data-section="appearance">Appearance</div>
                </div>
                
                <div class="section-content active" id="personal-content">
                    <div class="form-group">
                        <label for="fullname">Full Name</label>
                        <input type="text" id="fullname" class="form-control" value="<?php echo htmlspecialchars($resume_data['personal']['name']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="job-title">Professional Title</label>
                        <input type="text" id="job-title" class="form-control" value="<?php echo htmlspecialchars($resume_data['personal']['title'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" class="form-control" value="<?php echo htmlspecialchars($resume_data['personal']['email']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="text" id="phone" class="form-control" value="<?php echo htmlspecialchars($resume_data['personal']['phone'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Address</label>
                        <input type="text" id="address" class="form-control" value="<?php echo htmlspecialchars($resume_data['personal']['address'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="summary">Professional Summary</label>
                        <div id="summary-editor"></div>
                    </div>
                    
                    <?php if ($template['has_photo']): ?>
                    <div class="form-group">
                        <label for="photo">Profile Photo</label>
                        <input type="file" id="photo" class="form-control" accept="image/*">
                        <?php if (!empty($resume_data['personal']['photo'])): ?>
                            <div class="mt-2">
                                <img src="<?php echo $resume_data['personal']['photo']; ?>" alt="Profile Photo" style="max-width: 100px; max-height: 100px;">
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="section-content" id="sections-content">
                    <div id="sections-list" class="item-list">
                        <!-- Sections will be generated dynamically -->
                    </div>
                    
                    <button id="add-section-btn" class="add-section-btn">
                        <i class="fas fa-plus"></i> Add Section
                    </button>
                </div>
                
                <div class="section-content" id="appearance-content">
                    <div class="form-group">
                        <label for="custom-color">Custom Color</label>
                        <input type="text" id="custom-color" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="font-size">Font Size</label>
                        <select id="font-size" class="form-control">
                            <option value="small" <?php echo ($resume_data['settings']['font_size'] ?? 'medium') === 'small' ? 'selected' : ''; ?>>Small</option>
                            <option value="medium" <?php echo ($resume_data['settings']['font_size'] ?? 'medium') === 'medium' ? 'selected' : ''; ?>>Medium</option>
                            <option value="large" <?php echo ($resume_data['settings']['font_size'] ?? 'medium') === 'large' ? 'selected' : ''; ?>>Large</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="spacing">Spacing</label>
                        <select id="spacing" class="form-control">
                            <option value="compact" <?php echo ($resume_data['settings']['spacing'] ?? 'normal') === 'compact' ? 'selected' : ''; ?>>Compact</option>
                            <option value="normal" <?php echo ($resume_data['settings']['spacing'] ?? 'normal') === 'normal' ? 'selected' : ''; ?>>Normal</option>
                            <option value="spacious" <?php echo ($resume_data['settings']['spacing'] ?? 'normal') === 'spacious' ? 'selected' : ''; ?>>Spacious</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="editor-main">
            <div class="resume-canvas" id="resume-canvas">
                <!-- Resume content will be rendered here -->
            </div>
        </div>
    </div>
    
    <div class="zoom-controls">
        <button class="zoom-btn" id="zoom-out"><i class="fas fa-search-minus"></i></button>
        <span class="zoom-value" id="zoom-value">100%</span>
        <button class="zoom-btn" id="zoom-in"><i class="fas fa-search-plus"></i></button>
    </div>
    
    <div class="unsaved-warning" id="unsaved-warning">
    <span>You have unsaved changes</span>
    <button id="save-changes-btn">Save Now</button>
</div>

<!-- Add Section Modal -->
<div class="modal-backdrop" id="add-section-modal">
    <div class="modal">
        <div class="modal-header">
            <h3 class="modal-title">Add New Section</h3>
            <button class="modal-close" id="close-section-modal">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="section-type">Section Type</label>
                <select id="section-type" class="form-control">
                    <?php foreach ($section_types as $value => $label): ?>
                        <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group" id="custom-section-name-group" style="display: none;">
                <label for="custom-section-name">Section Name</label>
                <input type="text" id="custom-section-name" class="form-control" placeholder="e.g. Volunteering, Awards">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" id="cancel-add-section">Cancel</button>
            <button class="btn btn-primary" id="confirm-add-section">Add Section</button>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div class="loading-indicator" id="loading-indicator">
    <div class="spinner"></div>
</div>

<!-- Resume Item Templates (hidden) -->
<div id="templates" style="display: none;">
    <!-- Experience Item Template -->
    <div id="experience-item-template" class="item-template">
        <div class="item">
            <div class="item-header">
                <span class="item-title">New Position</span>
                <div>
                    <button class="action-btn move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                    <button class="action-btn move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    <button class="action-btn toggle-item" title="Edit"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" title="Delete"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-content">
                <div class="form-group">
                    <label>Job Title</label>
                    <input type="text" class="form-control job-title" placeholder="e.g. Software Developer">
                </div>
                <div class="form-group">
                    <label>Company</label>
                    <input type="text" class="form-control company" placeholder="Company name">
                </div>
                <div class="form-group">
                    <label>Location</label>
                    <input type="text" class="form-control location" placeholder="City, Country">
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col">
                            <label>Start Date</label>
                            <input type="month" class="form-control start-date">
                        </div>
                        <div class="col">
                            <label>End Date</label>
                            <input type="month" class="form-control end-date">
                            <div class="form-check mt-2">
                                <input type="checkbox" class="form-check-input current-job">
                                <label class="form-check-label">Current job</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <div class="editor-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Education Item Template -->
    <div id="education-item-template" class="item-template">
        <div class="item">
            <div class="item-header">
                <span class="item-title">New Education</span>
                <div>
                    <button class="action-btn move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                    <button class="action-btn move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    <button class="action-btn toggle-item" title="Edit"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" title="Delete"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-content">
                <div class="form-group">
                    <label>Degree</label>
                    <input type="text" class="form-control degree" placeholder="e.g. Bachelor of Science">
                </div>
                <div class="form-group">
                    <label>Field of Study</label>
                    <input type="text" class="form-control field" placeholder="e.g. Computer Science">
                </div>
                <div class="form-group">
                    <label>Institution</label>
                    <input type="text" class="form-control institution" placeholder="University or school name">
                </div>
                <div class="form-group">
                    <label>Location</label>
                    <input type="text" class="form-control location" placeholder="City, Country">
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col">
                            <label>Start Date</label>
                            <input type="month" class="form-control start-date">
                        </div>
                        <div class="col">
                            <label>End Date</label>
                            <input type="month" class="form-control end-date">
                            <div class="form-check mt-2">
                                <input type="checkbox" class="form-check-input current-education">
                                <label class="form-check-label">In progress</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <div class="editor-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Skill Item Template -->
    <div id="skill-item-template" class="item-template">
        <div class="item">
            <div class="item-header">
                <span class="item-title">New Skill</span>
                <div>
                    <button class="action-btn move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                    <button class="action-btn move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    <button class="action-btn toggle-item" title="Edit"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" title="Delete"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-content">
                <div class="form-group">
                    <label>Skill Name</label>
                    <input type="text" class="form-control skill-name" placeholder="e.g. JavaScript">
                </div>
                <div class="form-group">
                    <label>Proficiency Level</label>
                    <select class="form-control skill-level">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                        <option value="expert">Expert</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Language Item Template -->
    <div id="language-item-template" class="item-template">
        <div class="item">
            <div class="item-header">
                <span class="item-title">New Language</span>
                <div>
                    <button class="action-btn move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                    <button class="action-btn move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                    <button class="action-btn toggle-item" title="Edit"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" title="Delete"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-content">
                <div class="form-group">
                    <label>Language</label>
                    <input type="text" class="form-control language-name" placeholder="e.g. English">
                </div>
                <div class="form-group">
                    <label>Proficiency Level</label>
                    <select class="form-control language-level">
                        <option value="native">Native</option>
                        <option value="fluent">Fluent</option>
                        <option value="advanced">Advanced</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="basic">Basic</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.js"></script>
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize variables
    let resumeData = <?php echo json_encode($resume_data); ?>;
    let resumeId = <?php echo $resume_id ? $resume_id : 'null'; ?>;
    let templateId = <?php echo $template_id; ?>;
    let currentZoom = 1;
    let hasUnsavedChanges = false;
    let editors = {};
    
    // Initialize color picker
    $("#custom-color").spectrum({
        color: resumeData.settings.theme_color || "#19414B",
        preferredFormat: "hex",
        showInput: true,
        allowEmpty: false,
        change: function(color) {
            updateThemeColor(color.toHexString());
        }
    });
    
    // Initialize rich text editor for summary
    const summaryEditor = new Quill('#summary-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                ['bold', 'italic', 'underline'],
                ['link'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }]
            ]
        },
        placeholder: 'Write a professional summary...'
    });
    
    // Set initial summary content
    summaryEditor.root.innerHTML = resumeData.personal.summary || '';
    
    // Load resume sections
    loadSections();
    
    // Render the resume
    renderResume();
    
    // Tabs functionality
    $('.section-tab').click(function() {
        const section = $(this).data('section');
        $('.section-tab').removeClass('active');
        $(this).addClass('active');
        $('.section-content').removeClass('active');
        $(`#${section}-content`).addClass('active');
    });
    
    // Color palette selection
    $('.color-option').click(function() {
        $('.color-option').removeClass('active');
        $(this).addClass('active');
        const color = $(this).data('color');
        updateThemeColor(color);
        $("#custom-color").spectrum("set", color);
    });
    
    // Font family change
    $('#font-family').change(function() {
        const fontFamily = $(this).val();
        updateFontFamily(fontFamily);
    });
    
    // Layout change
    $('[data-layout]').click(function() {
        $('[data-layout]').removeClass('active');
        $(this).addClass('active');
        const layout = $(this).data('layout');
        updateLayout(layout);
    });
    
    // Font size change
    $('#font-size').change(function() {
        const fontSize = $(this).val();
        updateFontSize(fontSize);
    });
    
    // Spacing change
    $('#spacing').change(function() {
        const spacing = $(this).val();
        updateSpacing(spacing);
    });
    
    // Zoom functionality
    $('#zoom-in').click(function() {
        if (currentZoom < 1.5) {
            currentZoom += 0.1;
            updateZoom();
        }
    });
    
    $('#zoom-out').click(function() {
        if (currentZoom > 0.5) {
            currentZoom -= 0.1;
            updateZoom();
        }
    });
    
    // Add section button click
    $('#add-section-btn').click(function() {
        $('#add-section-modal').addClass('active');
    });
    
    // Section type change
    $('#section-type').change(function() {
        const type = $(this).val();
        if (type === 'custom') {
            $('#custom-section-name-group').show();
        } else {
            $('#custom-section-name-group').hide();
        }
    });
    
    // Close modal buttons
    $('#close-section-modal, #cancel-add-section').click(function() {
        $('#add-section-modal').removeClass('active');
    });
    
    // Confirm add section
    $('#confirm-add-section').click(function() {
        const sectionType = $('#section-type').val();
        let sectionName = $('#section-types option:selected').text();
        
        if (sectionType === 'custom') {
            sectionName = $('#custom-section-name').val().trim();
            if (!sectionName) {
                alert('Please enter a name for your custom section');
                return;
            }
        }
        
        addSection(sectionType, sectionName);
        $('#add-section-modal').removeClass('active');
        $('#custom-section-name').val('');
    });
    
    // Save button click
    $('#save-resume, #save-changes-btn').click(function() {
        saveResume();
    });
    
    // Download PDF button click
    $('#download-pdf').click(function() {
        exportPDF();
    });
    
    // Personal info changes
    $('#fullname, #job-title, #email, #phone, #address').on('input', function() {
        updatePersonalInfo();
    });
    
    // Photo upload
    $('#photo').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                resumeData.personal.photo = e.target.result;
                renderResume();
                markUnsaved();
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Summary change
    summaryEditor.on('text-change', function() {
        resumeData.personal.summary = summaryEditor.root.innerHTML;
        renderResume();
        markUnsaved();
    });
    
    // Before unload warning
    window.addEventListener('beforeunload', function(e) {
        if (hasUnsavedChanges) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
    
    // Functions
    
    // Load sections from resumeData
    function loadSections() {
        const sectionsList = $('#sections-list');
        sectionsList.empty();
        
        // For each section in resumeData.sections
        Object.entries(resumeData.sections).forEach(([type, items]) => {
            if (items && items.length > 0) {
                const sectionName = getSectionName(type);
                const sectionElement = createSectionElement(type, sectionName, items);
                sectionsList.append(sectionElement);
            }
        });
    }
    
    // Create a section element
    function createSectionElement(type, name, items) {
        const section = $(`
            <div class="item" data-section-type="${type}">
                <div class="item-header">
                    <span class="item-title">${name}</span>
                    <div>
                        <button class="action-btn move-up" title="Move Up"><i class="fas fa-arrow-up"></i></button>
                        <button class="action-btn move-down" title="Move Down"><i class="fas fa-arrow-down"></i></button>
                        <button class="action-btn toggle-item" title="Edit"><i class="fas fa-edit"></i></button>
                    </div>
                </div>
                <div class="item-content">
                    <div class="items-container"></div>
                    <button class="add-item-btn add-section-btn" data-type="${type}">
                        <i class="fas fa-plus"></i> Add ${getSectionItemName(type)}
                    </button>
                </div>
            </div>
        `);
        
        const itemsContainer = section.find('.items-container');
        
        // Add items to the section
        if (items && items.length > 0) {
            items.forEach((item, index) => {
                const itemElement = createItemElement(type, item, index);
                itemsContainer.append(itemElement);
            });
        }
        
        // Set up event listeners for this section
        section.find('.toggle-item').click(function() {
            section.find('.item-content').toggleClass('active');
        });
        
        section.find('.move-up').click(function() {
            moveSectionUp(section);
        });
        
        section.find('.move-down').click(function() {
            moveSectionDown(section);
        });
        
        section.find('.add-item-btn').click(function() {
            addItemToSection(type, itemsContainer);
        });
        
        return section;
    }
    
    // Create an item element based on type
    function createItemElement(type, itemData, index) {
        let template;
        let item;
        
        switch(type) {
            case 'experience':
                template = $('#experience-item-template').html();
                item = $(template);
                item.find('.item-title').text(itemData.title || 'Position');
                item.find('.job-title').val(itemData.title || '');
                item.find('.company').val(itemData.company || '');
                item.find('.location').val(itemData.location || '');
                item.find('.start-date').val(itemData.start_date || '');
                item.find('.end-date').val(itemData.end_date || '');
                if (itemData.current) {
                    item.find('.current-job').prop('checked', true);
                    item.find('.end-date').prop('disabled', true);
                }
                
                // Initialize editor for description
                const expEditor = new Quill(item.find('.editor-container')[0], {
                    theme: 'snow',
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline'],
                            [{ 'list': 'ordered'}, { 'list': 'bullet' }]
                        ]
                    }
                });
                expEditor.root.innerHTML = itemData.description || '';
                editors[`experience_${index}`] = expEditor;
                break;
                
            case 'education':
                template = $('#education-item-template').html();
                item = $(template);
                item.find('.item-title').text(itemData.degree || 'Education');
                item.find('.degree').val(itemData.degree || '');
                item.find('.field').val(itemData.field || '');
                item.find('.institution').val(itemData.institution || '');
                item.find('.location').val(itemData.location || '');
                item.find('.start-date').val(itemData.start_date || '');
                item.find('.end-date').val(itemData.end_date || '');
                if (itemData.current) {
                    item.find('.current-education').prop('checked', true);
                    item.find('.end-date').prop('disabled', true);
                }
                
                // Initialize editor for description
                const eduEditor = new Quill(item.find('.editor-container')[0], {
                    theme: 'snow',
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline'],
                            [{ 'list': 'ordered'}, { 'list': 'bullet' }]
                        ]
                    }
                });
                eduEditor.root.innerHTML = itemData.description || '';
                editors[`education_${index}`] = eduEditor;
                break;
                
            case 'skills':
                template = $('#skill-item-template').html();
                item = $(template);
                item.find('.item-title').text(itemData.name || 'Skill');
                item.find('.skill-name').val(itemData.name || '');
                item.find('.skill-level').val(itemData.level || 'intermediate');
                break;
                
            case 'languages':
                template = $('#language-item-template').html();
                item = $(template);
                item.find('.item-title').text(itemData.name || 'Language');
                item.find('.language-name').val(itemData.name || '');
                item.find('.language-level').val(itemData.level || 'intermediate');
                break;
                
            // Add more cases for other section types
                
            default:
                return $('<div>Unknown item type</div>');
        }
        
        // Set up event listeners for this item
        setupItemEventListeners(item, type, index);
        
        return item;
    }
    
    // Set up event listeners for an item
    function setupItemEventListeners(item, type, index) {
        // Toggle content
        item.find('.toggle-item').click(function() {
            item.find('.item-content').toggleClass('active');
        });
        
        // Delete item
        item.find('.delete').click(function() {
            if (confirm('Are you sure you want to delete this item?')) {
                // Remove the item from resumeData
                resumeData.sections[type].splice(index, 1);
                
                // Remove the item element
                item.remove();
                
                // Update all other items' indices
                updateItemIndices(type);
                
                // Re-render the resume
                renderResume();
                markUnsaved();
            }
        });
        
        // Move up
        item.find('.move-up').click(function() {
            moveItemUp(item, type, index);
        });
        
        // Move down
        item.find('.move-down').click(function() {
            moveItemDown(item, type, index);
        });
        
        // Input change events based on type
        switch(type) {
            case 'experience':
                setupExperienceItemEvents(item, index);
                break;
                
            case 'education':
                setupEducationItemEvents(item, index);
                break;
                
            case 'skills':
                setupSkillItemEvents(item, index);
                break;
                
            case 'languages':
                setupLanguageItemEvents(item, index);
                break;
                
            // Add more cases for other item types
        }
    }
    
    // Setup events for experience items
    function setupExperienceItemEvents(item, index) {
        // Update title when job title changes
        item.find('.job-title').on('input', function() {
            const value = $(this).val();
            item.find('.item-title').text(value || 'Position');
            resumeData.sections.experience[index].title = value;
            renderResume();
            markUnsaved();
        });
        
        // Update company
        item.find('.company').on('input', function() {
            resumeData.sections.experience[index].company = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Update location
        item.find('.location').on('input', function() {
            resumeData.sections.experience[index].location = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Update dates
        item.find('.start-date').on('change', function() {
            resumeData.sections.experience[index].start_date = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        item.find('.end-date').on('change', function() {
            resumeData.sections.experience[index].end_date = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Current job toggle
        item.find('.current-job').on('change', function() {
            const checked = $(this).prop('checked');
            resumeData.sections.experience[index].current = checked;
            if (checked) {
                item.find('.end-date').prop('disabled', true);
                item.find('.end-date').val('');
                resumeData.sections.experience[index].end_date = '';
            } else {
                item.find('.end-date').prop('disabled', false);
            }
            renderResume();
            markUnsaved();
        });
        
        // Description change (handled by Quill editor)
        editors[`experience_${index}`].on('text-change', function() {
            resumeData.sections.experience[index].description = editors[`experience_${index}`].root.innerHTML;
            renderResume();
            markUnsaved();
        });
    }
    
    // Setup events for education items
    function setupEducationItemEvents(item, index) {
        // Update title when degree changes
        item.find('.degree').on('input', function() {
            const value = $(this).val();
            item.find('.item-title').text(value || 'Education');
            resumeData.sections.education[index].degree = value;
            renderResume();
            markUnsaved();
        });
        
        // Update field
        item.find('.field').on('input', function() {
            resumeData.sections.education[index].field = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Update institution
        item.find('.institution').on('input', function() {
            resumeData.sections.education[index].institution = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Update location
        item.find('.location').on('input', function() {
            resumeData.sections.education[index].location = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Update dates
        item.find('.start-date').on('change', function() {
            resumeData.sections.education[index].start_date = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        item.find('.end-date').on('change', function() {
            resumeData.sections.education[index].end_date = $(this).val();
            renderResume();
            markUnsaved();
        });
        
        // Current education toggle
        item.find('.current-education').on('change', function() {
            const checked = $(this).prop('checked');
            resumeData.sections.education[index].current = checked;
            if (checked) {
                item.find('.end-date').prop('disabled', true);
                item.find('.end-date').val('');
                resumeData.sections.education[index].end_date = '';
            } else {
                item.find('.end-date').prop('disabled', false);
            }
            renderResume();
            markUnsaved();
        });
        
        // Description change (handled by Quill editor)
        editors[`education_${index}`].on('text-change', function() {
            resumeData.sections.education[index].description = editors[`education_${index}`].root.innerHTML;
            renderResume();
            markUnsaved();
        });
    }
    
    // Setup events for skill items
    function setupSkillItemEvents(item, index) {
        // Update title when skill name changes
        item.find('.skill-name').on('input', function() {
            const value = $(this).val();
            item.find('.item-title').text(value || 'Skill');
            resumeData.sections.skills[index].name = value;
            renderResume();
            markUnsaved();
        });
        
        // Update level
        item.find('.skill-level').on('change', function() {
            resumeData.sections.skills[index].level = $(this).val();
            renderResume();
            markUnsaved();
        });
    }
    
    // Setup events for language items
    function setupLanguageItemEvents(item, index) {
        // Update title when language name changes
        item.find('.language-name').on('input', function() {
            const value = $(this).val();
            item.find('.item-title').text(value || 'Language');
            resumeData.sections.languages[index].name = value;
            renderResume();
            markUnsaved();
        });
        
        // Update level
        item.find('.language-level').on('change', function() {
            resumeData.sections.languages[index].level = $(this).val();
            renderResume();
            markUnsaved();
        });
    }
    
    // Add a new section
    function addSection(type, name) {
        // If the section doesn't exist in resumeData, create it
        if (!resumeData.sections[type]) {
            resumeData.sections[type] = [];
        }
        
        // Create the section element
        const section = createSectionElement(type, name, resumeData.sections[type]);
        $('#sections-list').append(section);
        
        // Open the section
        section.find('.item-content').addClass('active');
        
        // Mark changes as unsaved
        markUnsaved();
        
        // Re-render the resume
        renderResume();
    }
    
    // Add a new item to a section
    function addItemToSection(type, container) {
        // Create a new empty item data object
        let itemData = {};
        
        switch(type) {
            case 'experience':
                itemData = {
                    title: '',
                    company: '',
                    location: '',
                    start_date: '',
                    end_date: '',
                    current: false,
                    description: ''
                };
                break;
                
            case 'education':
                itemData = {
                    degree: '',
                    field: '',
                    institution: '',
                    location: '',
                    start_date: '',
                    end_date: '',
                    current: false,
                    description: ''
                };
                break;
                
            case 'skills':
                itemData = {
                    name: '',
                    level: 'intermediate'
                };
                break;
                
            case 'languages':
                itemData = {
                    name: '',
                    level: 'intermediate'
                };
                break;
                
            // Add more cases as needed for other section types
            default:
                itemData = {};
        }
        
        // Add the item to resumeData
        const index = resumeData.sections[type].length;
        resumeData.sections[type].push(itemData);
        
        // Create the item element
        const itemElement = createItemElement(type, itemData, index);
        container.append(itemElement);
        
        // Open the item editor
        itemElement.find('.item-content').addClass('active');
        
        // Mark changes as unsaved
        markUnsaved();
        
        // Re-render the resume
        renderResume();
    }
    
    // Move a section up
    function moveSectionUp(section) {
        const prev = section.prev('.item');
        if (prev.length) {
            section.insertBefore(prev);
            updateSectionOrder();
            markUnsaved();
        }
    }
    
    // Move a section down
    function moveSectionDown(section) {
        const next = section.next('.item');
        if (next.length) {
            section.insertAfter(next);
            updateSectionOrder();
            markUnsaved();
        }
    }
    
    // Update section order in resumeData
    function updateSectionOrder() {
        const newSections = {};
        $('#sections-list .item').each(function() {
            const type = $(this).data('section-type');
            newSections[type] = resumeData.sections[type];
        });
        
        resumeData.sections = newSections;
        renderResume();
    }
    
    // Move an item up
    function moveItemUp(item, type, index) {
        if (index > 0) {
            // Swap in DOM
            const prev = item.prev('.item');
            if (prev.length) {
                item.insertBefore(prev);
            }
            
            // Swap in resumeData
            const temp = resumeData.sections[type][index];
            resumeData.sections[type][index] = resumeData.sections[type][index - 1];
            resumeData.sections[type][index - 1] = temp;
            
            // Update indices
            updateItemIndices(type);
            
            // Re-render
            renderResume();
            markUnsaved();
        }
    }
    
    // Move an item down
    function moveItemDown(item, type, index) {
        if (index < resumeData.sections[type].length - 1) {
            // Swap in DOM
            const next = item.next('.item');
            if (next.length) {
                item.insertAfter(next);
            }
            
            // Swap in resumeData
            const temp = resumeData.sections[type][index];
            resumeData.sections[type][index] = resumeData.sections[type][index + 1];
            resumeData.sections[type][index + 1] = temp;
            
            // Update indices
            updateItemIndices(type);
            
            // Re-render
            renderResume();
            markUnsaved();
        }
    }
    
    // Update item indices after reordering or deletion
    function updateItemIndices(type) {
        // Update editor keys
        let newEditors = {};
        $(`[data-section-type="${type}"] .items-container .item`).each(function(idx) {
            if (type === 'experience' || type === 'education') {
                const oldIndex = $(this).find('.editor-container').closest('.item-content').closest('.item').index();
                if (editors[`${type}_${oldIndex}`]) {
                    newEditors[`${type}_${idx}`] = editors[`${type}_${oldIndex}`];
                }
            }
            
            // Update event listeners for the item
            setupItemEventListeners($(this), type, idx);
        });
        
        // Replace editors object with updated indices
        if (type === 'experience' || type === 'education') {
            editors = {...editors, ...newEditors};
        }
    }
    
    // Update personal information in resumeData
    function updatePersonalInfo() {
        resumeData.personal.name = $('#fullname').val();
        resumeData.personal.title = $('#job-title').val();
        resumeData.personal.email = $('#email').val();
        resumeData.personal.phone = $('#phone').val();
        resumeData.personal.address = $('#address').val();
        
        renderResume();
        markUnsaved();
    }
    
    // Update theme color
    function updateThemeColor(color) {
        resumeData.settings.theme_color = color;
        document.documentElement.style.setProperty('--primary', color);
        renderResume();
        markUnsaved();
    }
    
    // Update font family
    function updateFontFamily(fontFamily) {
        resumeData.settings.font_family = fontFamily;
        document.body.style.fontFamily = $('#font-family option:selected').css('font-family');
        renderResume();
        markUnsaved();
    }
    
    // Update layout
    function updateLayout(layout) {
        resumeData.settings.layout = layout;
        renderResume();
        markUnsaved();
    }
    
    // Update font size
    function updateFontSize(size) {
        resumeData.settings.font_size = size;
        renderResume();
        markUnsaved();
    }
    
    // Update spacing
    function updateSpacing(spacing) {
        resumeData.settings.spacing = spacing;
        renderResume();
        markUnsaved();
    }
    
    // Update zoom level
    function updateZoom() {
        currentZoom = Math.round(currentZoom * 10) / 10;
        $('#zoom-value').text(`${Math.round(currentZoom * 100)}%`);
        $('#resume-canvas').css('transform', `scale(${currentZoom})`);
    }
    
    // Mark changes as unsaved
    function markUnsaved() {
        hasUnsavedChanges = true;
        $('#unsaved-warning').addClass('active');
    }
    
    // Save the resume
    function saveResume() {
        // Show loading indicator
        $('#loading-indicator').addClass('active');
        
        // Prepare data for saving
        const data = {
            template_id: templateId,
            resume_data: JSON.stringify(resumeData)
        };
        
        if (resumeId) {
            data.resume_id = resumeId;
        }
        
        // Send AJAX request
        $.ajax({
            url: 'save_resume.php',
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update resume ID if it was newly created
                    if (response.resume_id) {
                        resumeId = response.resume_id;
                    }
                    
                    // Reset unsaved changes flag
                    hasUnsavedChanges = false;
                    $('#unsaved-warning').removeClass('active');
                    
                    // Show success message
                    alert('Resume saved successfully!');
                } else {
                    // Show error message
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('An error occurred while saving your resume. Please try again.');
            },
            complete: function() {
                // Hide loading indicator
                $('#loading-indicator').removeClass('active');
            }
        });
    }
    
    // Export to PDF
    function exportPDF() {
        // Show loading indicator
        $('#loading-indicator').addClass('active');
        
        // Clone the resume canvas for PDF export
        const element = $('#resume-canvas').clone();
        
        // Remove control buttons and edit styling
        element.find('.section-controls').remove();
        element.find('.resume-section').css('border', 'none');
        
        const options = {
            margin: 10,
            filename: 'resume.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };
        
        // Generate PDF
        html2pdf().from(element[0]).set(options).save().then(function() {
            // Hide loading indicator
            $('#loading-indicator').removeClass('active');
        });
    }
    
    // Render the resume based on template and data
    function renderResume() {
        const canvas = $('#resume-canvas');
        canvas.empty();
        
        // Apply settings
        canvas.css({
            'font-family': $('#font-family option:selected').css('font-family'),
            '--theme-color': resumeData.settings.theme_color
        });
        
        // Add CSS classes based on settings
        canvas.removeClass('font-size-small font-size-medium font-size-large');
        canvas.addClass(`font-size-${resumeData.settings.font_size}`);
        
        canvas.removeClass('spacing-compact spacing-normal spacing-spacious');
        canvas.addClass(`spacing-${resumeData.settings.spacing}`);
        
        canvas.removeClass('layout-standard layout-creative layout-modern');
        canvas.addClass(`layout-${resumeData.settings.layout}`);
        
        // Render the template
        switch(resumeData.settings.layout) {
            case 'creative':
                renderCreativeLayout(canvas);
                break;
            case 'modern':
                renderModernLayout(canvas);
                break;
            default:
                renderStandardLayout(canvas);
        }
    }
    
    // Render standard layout
    function renderStandardLayout(canvas) {
        // Header section
        const header = $('<div class="resume-section" id="header"></div>');
        header.append(`
            <h1>${resumeData.personal.name}</h1>
            ${resumeData.personal.title ? `<h2>${resumeData.personal.title}</h2>` : ''}
            <div class="contact-info">
                ${resumeData.personal.email ? `<div><i class="fas fa-envelope"></i> ${resumeData.personal.email}</div>` : ''}
                ${resumeData.personal.phone ? `<div><i class="fas fa-phone"></i> ${resumeData.personal.phone}</div>` : ''}
                ${resumeData.personal.address ? `<div><i class="fas fa-map-marker-alt"></i> ${resumeData.personal.address}</div>` : ''}
            </div>
        `);
        canvas.append(header);
        
        // Summary section if it exists
        if (resumeData.personal.summary && resumeData.personal.summary.trim() !== '') {
            const summary = $('<div class="resume-section" id="summary"></div>');
            summary.append(`
                <h3>Professional Summary</h3>
                <div>${resumeData.personal.summary}</div>
            `);
            canvas.append(summary);
        }
        
        // Render each section
        Object.entries(resumeData.sections).forEach(([type, items]) => {
            if (items && items.length > 0) {
                const sectionElement = $(`<div class="resume-section" id="${type}"></div>`);
                const sectionName = getSectionName(type);
                
                sectionElement.append(`<h3>${sectionName}</h3>`);
                
                const sectionContent = renderSectionContent(type, items);
                sectionElement.append(sectionContent);
                
                canvas.append(sectionElement);
            }
        });
    }
    
    // Render creative layout
    function renderCreativeLayout(canvas) {
        // Create two-column layout
        const leftColumn = $('<div class="resume-column left-column"></div>');
        const rightColumn = $('<div class="resume-column right-column"></div>');
        
        // Add photo if template has photo and photo exists
        if (resumeData.personal.photo) {
            const photo = $('<div class="resume-section" id="photo"></div>');
            photo.append(`<img src="${resumeData.personal.photo}" alt="Profile Photo">`);
            leftColumn.append(photo);
        }
        
        // Add contact info to left column
        const contact = $('<div class="resume-section" id="contact"></div>');
        contact.append(`
            <h3>Contact</h3>
            <div class="contact-list">
                ${resumeData.personal.email ? `<div><i class="fas fa-envelope"></i> ${resumeData.personal.email}</div>` : ''}
                ${resumeData.personal.phone ? `<div><i class="fas fa-phone"></i> ${resumeData.personal.phone}</div>` : ''}
                ${resumeData.personal.address ? `<div><i class="fas fa-map-marker-alt"></i> ${resumeData.personal.address}</div>` : ''}
            </div>
        `);
        leftColumn.append(contact);
        
        // Add skills and languages to left column if they exist
        ['skills', 'languages'].forEach(type => {
            if (resumeData.sections[type] && resumeData.sections[type].length > 0) {
                const sectionElement = $(`<div class="resume-section" id="${type}"></div>`);
                const sectionName = getSectionName(type);
                
                sectionElement.append(`<h3>${sectionName}</h3>`);
                
                const sectionContent = renderSectionContent(type, resumeData.sections[type]);
                sectionElement.append(sectionContent);
                
                leftColumn.append(sectionElement);
            }
        });
        
        // Add header to right column
        const header = $('<div class="resume-section" id="header"></div>');
        header.append(`
            <h1>${resumeData.personal.name}</h1>
            ${resumeData.personal.title ? `<h2>${resumeData.personal.title}</h2>` : ''}
        `);
        rightColumn.append(header);
        
        // Add summary if it exists
        if (resumeData.personal.summary && resumeData.personal.summary.trim() !== '') {
            const summary = $('<div class="resume-section" id="summary"></div>');
            summary.append(`
                <h3>Professional Summary</h3>
                <div>${resumeData.personal.summary}</div>
            `);
            rightColumn.append(summary);
        }
        
        // Add experience, education and other sections to right column
        ['experience', 'education', 'certifications', 'projects'].forEach(type => {
            if (resumeData.sections[type] && resumeData.sections[type].length > 0) {
                const sectionElement = $(`<div class="resume-section" id="${type}"></div>`);
                const sectionName = getSectionName(type);
                
                sectionElement.append(`<h3>${sectionName}</h3>`);
                
                const sectionContent = renderSectionContent(type, resumeData.sections[type]);
                sectionElement.append(sectionContent);
                
                rightColumn.append(sectionElement);
            }
        });
        
        // Add columns to canvas
        canvas.append(leftColumn);
        canvas.append(rightColumn);
        
        // Add creative layout styles
        canvas.css({
            'display': 'grid',
            'grid-template-columns': '30% 70%',
            'gap': '20px'
        });
        
        leftColumn.css({
            'background-color': `${resumeData.settings.theme_color}15`, // Light version of theme color
            'padding': '20px'
        });
        
        rightColumn.css({
            'padding': '20px'
        });
    }
    
    // Render modern layout
    function renderModernLayout(canvas) {
        // Header with name and title in a colored bar
        const header = $('<div class="resume-section" id="header"></div>');
        header.css({
            'background-color': resumeData.settings.theme_color,
            'color': 'white',
            'padding': '30px 40px',
            'margin': '0'
        });
        
        header.append(`
            <h1>${resumeData.personal.name}</h1>
            ${resumeData.personal.title ? `<h2>${resumeData.personal.title}</h2>` : ''}
        `);
        canvas.append(header);
        
        // Contact row
        const contactRow = $('<div class="resume-section" id="contact-row"></div>');
        contactRow.css({
            'display': 'flex',
            'justify-content': 'space-between',
            'padding': '15px 40px',
            'background-color': '#f5f5f5',
            'margin': '0'
        });
        
        contactRow.append(`
            ${resumeData.personal.email ? `<div><i class="fas fa-envelope"></i> ${resumeData.personal.email}</div>` : ''}
            ${resumeData.personal.phone ? `<div><i class="fas fa-phone"></i> ${resumeData.personal.phone}</div>` : ''}
            ${resumeData.personal.address ? `<div><i class="fas fa-map-marker-alt"></i> ${resumeData.personal.address}</div>` : ''}
        `);
        canvas.append(contactRow);
        
        const contentContainer = $('<div class="content-container"></div>');
        contentContainer.css({
            'padding': '20px 40px'
        });
        
        // Summary section if it exists
        if (resumeData.personal.summary && resumeData.personal.summary.trim() !== '') {
            const summary = $('<div class="resume-section" id="summary"></div>');
            summary.append(`
                <h3>Professional Summary</h3>
                <div>${resumeData.personal.summary}</div>
            `);
            contentContainer.append(summary);
        }
        
        // Create two-column layout for the main content
        const mainContent = $('<div class="main-content"></div>');
        mainContent.css({
            'display': 'grid',
            'grid-template-columns': '60% 40%',
            'gap': '20px'
        });
        
        const leftContent = $('<div class="left-content"></div>');
        const rightContent = $('<div class="right-content"></div>');
        
        // Add experience and education to left column
        ['experience', 'projects'].forEach(type => {
            if (resumeData.sections[type] && resumeData.sections[type].length > 0) {
                const sectionElement = $(`<div class="resume-section" id="${type}"></div>`);
                const sectionName = getSectionName(type);
                
                sectionElement.append(`<h3>${sectionName}</h3>`);
                
                const sectionContent = renderSectionContent(type, resumeData.sections[type]);
                sectionElement.append(sectionContent);
                
                leftContent.append(sectionElement);
            }
        });
        
        // Add skills, languages, education, and certifications to right column
        ['education', 'skills', 'languages', 'certifications'].forEach(type => {
            if (resumeData.sections[type] && resumeData.sections[type].length > 0) {
                const sectionElement = $(`<div class="resume-section" id="${type}"></div>`);
                const sectionName = getSectionName(type);
                
                sectionElement.append(`<h3>${sectionName}</h3>`);
                
                const sectionContent = renderSectionContent(type, resumeData.sections[type]);
                sectionElement.append(sectionContent);
                
                rightContent.append(sectionElement);
            }
        });
        
        mainContent.append(leftContent);
        mainContent.append(rightContent);
        contentContainer.append(mainContent);
        canvas.append(contentContainer);
    }
    
    // Render section content based on type
    function renderSectionContent(type, items) {
        let content;
        
        switch(type) {
            case 'experience':
                content = $('<div class="experience-items"></div>');
                items.forEach(item => {
                    const dateRange = formatDateRange(item.start_date, item.end_date, item.current);
                    content.append(`
                        <div class="experience-item">
                            <div class="item-header">
                                <h4>${item.title || ''}</h4>
                                ${dateRange ? `<span class="date-range">${dateRange}</span>` : ''}
                            </div>
                            <div class="item-subheader">
                                ${item.company ? `<span class="company">${item.company}</span>` : ''}
                                ${item.location ? `<span class="location">${item.location}</span>` : ''}
                            </div>
                            ${item.description ? `<div class="description">${item.description}</div>` : ''}
                        </div>
                    `);
                });
                break;
                
            case 'education':
                content = $('<div class="education-items"></div>');
                items.forEach(item => {
                    const dateRange = formatDateRange(item.start_date, item.end_date, item.current);
                    content.append(`
                        <div class="education-item">
                            <div class="item-header">
                                ${item.degree ? `<h4>${item.degree}${item.field ? ` in ${item.field}` : ''}</h4>` : ''}
                                ${dateRange ? `<span class="date-range">${dateRange}</span>` : ''}
                            </div>
                            <div class="item-subheader">
                                ${item.institution ? `<span class="institution">${item.institution}</span>` : ''}
                                ${item.location ? `<span class="location">${item.location}</span>` : ''}
                            </div>
                            ${item.description ? `<div class="description">${item.description}</div>` : ''}
                        </div>
                    `);
                });
                break;
                
            case 'skills':
                content = $('<div class="skills-items"></div>');
                content.append('<ul class="skills-list">');
                items.forEach(item => {
                    if (item.name) {
                        content.find('.skills-list').append(`
                            <li>
                                <span class="skill-name">${item.name}</span>
                                <span class="skill-level">
                                    <div class="progress-bar">
                                        <div class="progress" style="width: ${getSkillLevelPercentage(item.level)}"></div>
                                    </div>
                                </span>
                            </li>
                        `);
                    }
                });
                break;
                
            case 'languages':
                content = $('<div class="languages-items"></div>');
                content.append('<ul class="languages-list">');
                items.forEach(item => {
                    if (item.name) {
                        content.find('.languages-list').append(`
                            <li>
                                <span class="language-name">${item.name}</span>
                                <span class="language-level">${formatLanguageLevel(item.level)}</span>
                            </li>
                        `);
                    }
                });
                break;
                
            case 'certifications':
                content = $('<div class="certifications-items"></div>');
                content.append('<ul class="certifications-list">');
                items.forEach(item => {
                    if (item.name) {
                        content.find('.certifications-list').append(`
                            <li>
                                <span class="cert-name">${item.name}</span>
                                ${item.issuer ? `<span class="cert-issuer">${item.issuer}</span>` : ''}
                                ${item.date ? `<span class="cert-date">${formatDate(item.date)}</span>` : ''}
                            </li>
                        `);
                    }
                });
                break;
                
            case 'projects':
                content = $('<div class="projects-items"></div>');
                items.forEach(item => {
                    content.append(`
                        <div class="project-item">
                            <div class="item-header">
                                <h4>${item.name || ''}</h4>
                                ${item.date ? `<span class="date">${formatDate(item.date)}</span>` : ''}
                            </div>
                            <div class="item-subheader">
                                ${item.role ? `<span class="role">${item.role}</span>` : ''}
                            </div>
                            ${item.description ? `<div class="description">${item.description}</div>` : ''}
                        </div>
                    `);
                });
                break;
                
            // Add more cases for other section types
                
            default:
                content = $('<div>No content available</div>');
        }
        
        return content;
    }
    
    // Helper functions
    
    // Get section name from type
    function getSectionName(type) {
        const sectionNames = {
            'experience': 'Work Experience',
            'education': 'Education',
            'skills': 'Skills',
            'languages': 'Languages',
            'certifications': 'Certifications',
            'projects': 'Projects',
            'interests': 'Interests',
            'references': 'References'
        };
        
        return sectionNames[type] || 'Section';
    }
    
    // Get section item name for "Add X" button
    function getSectionItemName(type) {
        const itemNames = {
            'experience': 'Position',
            'education': 'Education',
            'skills': 'Skill',
            'languages': 'Language',
            'certifications': 'Certification',
            'projects': 'Project',
            'interests': 'Interest',
            'references': 'Reference'
        };
        
        return itemNames[type] || 'Item';
    }
    
    // Format date range
    function formatDateRange(startDate, endDate, isCurrent) {
        let result = '';
        
        if (startDate) {
            result += formatDate(startDate);
        }
        
        result += ' - ';
        
        if (isCurrent) {
            result += 'Present';
        } else if (endDate) {
            result += formatDate(endDate);
        }
        
        return result;
    }
    
    // Format date (YYYY-MM to Mon YYYY)
    function formatDate(dateStr) {
        if (!dateStr) return '';
        
        const parts = dateStr.split('-');
        if (parts.length !== 2) return dateStr;
        
        const year = parts[0];
        const month = new Date(dateStr + '-01').toLocaleString('default', { month: 'short' });
        
        return `${month} ${year}`;
    }
    
    // Get skill level percentage for progress bar
    function getSkillLevelPercentage(level) {
        const percentages = {
            'beginner': '25%',
            'intermediate': '50%',
            'advanced': '75%',
            'expert': '100%'
        };
        
        return percentages[level] || '50%';
    }
    
    // Format language level
    function formatLanguageLevel(level) {
        const levels = {
            'native': 'Native',
            'fluent': 'Fluent',
            'advanced': 'Advanced',
            'intermediate': 'Intermediate',
            'basic': 'Basic'
        };
        
        return levels[level] || level;
    }
    
    // Make sections sortable
    $('#sections-list').sortable({
        handle: '.item-header',
        update: function() {
            updateSectionOrder();
        }
    });
});
</script>
</body>
</html>