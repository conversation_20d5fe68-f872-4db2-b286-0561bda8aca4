import React from "react";

const References = ({
  references = [],
  containerStyle = "",
  itemStyle = "",
  nameStyle = "",
  detailStyle = "",
}) => {
  if (!references || references.length === 0) return null;

  return (
    <div className={`space-y-4 ${containerStyle}`}>
      {references.map((ref, idx) => (
        <div key={idx} className={`text-sm ${itemStyle}`}>
          <p className={`font-semibold ${nameStyle}`}>{ref.name}</p>
          {ref.position && <p className={detailStyle}>{ref.position}</p>}
          {ref.company && <p className={detailStyle}>{ref.company}</p>}
          {ref.email && (
            <p className={detailStyle}>
              Email:{" "}
              <a href={`mailto:${ref.email}`} className="underline">
                {ref.email}
              </a>
            </p>
          )}
          {ref.phone && <p className={detailStyle}>Phone: {ref.phone}</p>}
        </div>
      ))}
    </div>
  );
};

export default References;
