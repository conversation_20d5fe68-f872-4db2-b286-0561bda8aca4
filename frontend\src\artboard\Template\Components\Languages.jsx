import React, { useEffect } from "react";
import { useLanguageStore } from "../../../store/languageStore";

const Languages = () => {
  const { languages, fetchLanguages } = useLanguageStore();

  useEffect(() => {
    fetchLanguages();
  }, [fetchLanguages]);

  if (!languages || languages.length === 0) return null;

  return (
    <section id="languages" className="mb-6">
      <h4 className="text-xl font-semibold border-b pb-1 border-black mb-2">
        Languages
      </h4>
      <div className="space-y-4">
        {languages.map((lang) => (
          <div key={lang._id}>
            <div className="flex justify-between">
              <h5 className="text-base font-semibold">{lang.Name}</h5>
              <span className="text-sm text-gray-600 italic">
                {lang.Proficiency}
              </span>
            </div>
            {lang.Description && (
              <div
                className="text-sm mt-1 text-gray-700"
                dangerouslySetInnerHTML={{ __html: lang.Description }}
              />
            )}
          </div>
        ))}
      </div>
    </section>
  );
};

export default Languages;
