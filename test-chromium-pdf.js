const axios = require('axios');

async function testProductionPDFGeneration() {
    const testHTML = `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h1 style="color: #333;">Test Resume - Chromium Fix</h1>
            <h2><PERSON></h2>
            <p>Software Developer</p>
            <hr>
            <h3>Experience</h3>
            <p><strong>Senior Developer</strong> - Tech Company (2020-2023)</p>
            <ul>
                <li>Developed web applications using React and Node.js</li>
                <li>Led a team of 5 developers</li>
                <li>Improved application performance by 40%</li>
            </ul>
            <h3>Education</h3>
            <p><strong>Bachelor of Computer Science</strong> - University (2016-2020)</p>
            <h3>Skills</h3>
            <ul>
                <li>JavaScript, TypeScript</li>
                <li>React, Node.js, Express</li>
                <li>MongoDB, PostgreSQL</li>
                <li><PERSON><PERSON>, Docker</li>
            </ul>
        </div>
    `;

    const testData = {
        html: testHTML,
        fileName: "chromium-test-resume",
        format: "pdf"
    };

    try {
        console.log('🧪 Testing production PDF generation with Chromium fix...');
        
        const prodURL = 'https://resumebuilder-m27v.onrender.com/api/pdf/generate';
        console.log(`Testing: ${prodURL}`);
        
        const response = await axios.post(prodURL, testData, {
            responseType: 'blob',
            timeout: 120000 // 2 minutes timeout for production
        });

        if (response.status === 200) {
            console.log('✅ PDF generation successful!');
            console.log('Response headers:', response.headers);
            console.log('Response size:', response.data.size || response.data.length);
            console.log('Content-Type:', response.headers['content-type']);
        } else {
            console.log('❌ PDF generation failed with status:', response.status);
        }

    } catch (error) {
        console.log('❌ PDF generation failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Headers:', error.response.headers);
            
            // Try to parse error response
            if (error.response.data instanceof Buffer) {
                try {
                    const errorText = error.response.data.toString();
                    const errorJson = JSON.parse(errorText);
                    console.log('Error details:', errorJson);
                } catch (parseError) {
                    console.log('Raw error data:', error.response.data.toString());
                }
            } else {
                console.log('Error data:', error.response.data);
            }
        } else if (error.request) {
            console.log('No response received:', error.message);
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Run test
testProductionPDFGeneration();
