import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, GETINFO_ENDPOINT } from "../lib/constants";
const API_BASE_URL = "http://localhost:5000/api"
export const useAwardsStore = create((set) => ({
    awards: [],
    isLoading: false,
    error: null,

    // Fetch all awards
    fetchAwards: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, { withCredentials: true });
            const user = res.data.user;
            set({ awards: user?.Awards || [], isLoading: false });
        } catch (err) {
            set({
                error: err.response?.data?.error || "Failed to load awards",
                isLoading: false,
            });
        }
    },

    // Add new award
    addAward: async (data) => {
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.AWARDS, data, {
                withCredentials: true,
            });
            set((state) => ({
                awards: [...state.awards, res.data],
            }));
        } catch (err) {
            console.error("Error adding award:", err.response?.data || err.message);
        }
    },

    // Delete award
    deleteAward: async (award) => {
        try {
            await axios.delete(`${API_BASE_URL}/deleteinfo/awards`, {
                data: award,
                withCredentials: true,
            });

            set((state) => ({
                awards: state.awards.filter((a) => a._id !== award._id),
            }));
        } catch (error) {
            console.error("Failed to delete award:", error);
        }
    },      

    // Update award
    updateAward: async (id, data) => {
        try {
            const res = await axios.put(`${ADDINFO_ENDPOINTS.AWARDS}/${id}`, data, {
                withCredentials: true,
            });
            set((state) => ({
                awards: state.awards.map((award) =>
                    award._id === id ? res.data : award
                ),
            }));
        } catch (err) {
            console.error("Error updating award:", err.response?.data || err.message);
        }
    },
}));

