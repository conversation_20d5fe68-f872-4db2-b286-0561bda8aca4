/* Base Styles for All Resume Templates */
.resume-template {
    font-family: 'Roboto', sans-serif;
    color: #333;
    line-height: 1.6;
    max-width: 8.5in;
    margin: 0 auto;
    padding: 0.5in;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modern Template */
.resume-template.modern {
    display: grid;
    grid-template-columns: 7fr 3fr;
    grid-gap: 2rem;
}

.modern .resume-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.modern .header-main {
    flex: 1;
}

.modern .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #2c3e50;
}

.modern .resume-title {
    font-size: 1.2rem;
    color: #3498db;
    margin: 0.5rem 0 0;
}

.modern .header-sidebar {
    width: 30%;
}

.modern .contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.modern .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.modern .contact-item i {
    color: #3498db;
    width: 16px;
}

.modern .resume-main {
    padding-right: 2rem;
    border-right: 1px solid #eee;
}

.modern .resume-sidebar {
    padding-left: 0;
}

.modern .section-title {
    color: #3498db;
    font-size: 1.4rem;
    margin: 1.5rem 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.modern .content-item {
    margin-bottom: 1.5rem;
}

.modern .item-header {
    margin-bottom: 0.5rem;
}

.modern .item-title {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.modern .item-subtitle {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.modern .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.modern .skill-item {
    background-color: #f0f8ff;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

/* Minimal Template */
.resume-template.minimal {
    font-family: 'Open Sans', sans-serif;
    padding: 1in;
    color: #333;
}

.minimal .resume-header {
    text-align: center;
    margin-bottom: 2rem;
}

.minimal .resume-name {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
    letter-spacing: 1px;
}

.minimal .resume-title {
    font-size: 1.2rem;
    color: #888;
    margin: 0.5rem 0 1.5rem;
    font-weight: 400;
}

.minimal .resume-contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.minimal .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.minimal .contact-item i {
    color: #555;
}

.minimal .section-title {
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 2rem 0 1rem;
    color: #333;
    font-weight: 600;
}

.minimal .skills-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.minimal .skill-tag {
    background-color: #f5f5f5;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.minimal .timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.minimal .timeline-marker {
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ddd;
}

.minimal .timeline-item::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 0.7rem;
    bottom: -2rem;
    width: 2px;
    background-color: #eee;
}

.minimal .timeline-item:last-child::before {
    display: none;
}

.minimal .timeline-content h3 {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #333;
}

.minimal .timeline-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #888;
    margin-bottom: 0.5rem;
}

/* Executive Template */
.resume-template.executive {
    font-family: 'Merriweather', serif;
    padding: 0.75in;
    color: #333;
}

.executive .resume-header {
    margin-bottom: 2rem;
}

.executive .name-title-section {
    margin-bottom: 1.5rem;
}

.executive .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #2c3e50;
}

.executive .title-bar {
    width: 100px;
    height: 4px;
    background-color: #2c3e50;
    margin: 0.5rem 0;
}

.executive .resume-title {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin: 0.5rem 0 0;
    font-style: italic;
}

.executive .contact-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.executive .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.executive .contact-item i {
    color: #2c3e50;
}

.executive .resume-summary {
    margin: 2rem 0;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-left: 4px solid #2c3e50;
    font-style: italic;
}

.executive .resume-body {
    display: grid;
    grid-template-columns: 7fr 3fr;
    gap: 2rem;
}

.executive .section-header {
    margin-bottom: 1.5rem;
}

.executive .section-title {
    font-size: 1.4rem;
    color: #2c3e50;
    margin: 0 0 0.5rem;
}

.executive .section-line {
    width: 100%;
    height: 2px;
    background-color: #eee;
}

.executive .experience-item, .executive .education-item {
    margin-bottom: 2rem;
}

.executive .position, .executive .degree {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.executive .company-date, .executive .institution-date {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.75rem;
}

.executive .skills-list, .executive .certifications-list, .executive .projects-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.executive .skill-item {
    background-color: #f8f9fa;
    padding: 0.5rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

/* Creative Template */
.resume-template.creative {
    font-family: 'Poppins', sans-serif;
    display: grid;
    grid-template-columns: 3fr 7fr;
    gap: 0;
    padding: 0;
}

.creative .resume-sidebar {
    background-color: #2c3e50;
    color: white;
    padding: 2rem;
}

.creative .profile-section {
    text-align: center;
    margin-bottom: 2rem;
}

.creative .profile-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #3498db;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem;
}

.creative .profile-initial {
    font-size: 3rem;
    font-weight: bold;
    color: white;
}

.creative .profile-name {
    font-size: 1.8rem;
    margin: 0 0 0.25rem;
}

.creative .profile-title {
    font-size: 1rem;
    color: #bdc3c7;
    margin: 0;
}

.creative .sidebar-heading {
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 2rem 0 1rem;
    color: #3498db;
}

.creative .contact-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.creative .contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.creative .contact-icon {
    width: 30px;
    height: 30px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.creative .contact-text {
    font-size: 0.9rem;
}

.creative .skill-item {
    margin-bottom: 1rem;
}

.creative .skill-name {
    margin-bottom: 0.5rem;
    display: block;
}

.creative .skill-bar {
    height: 6px;
    background-color: #34495e;
    border-radius: 3px;
    overflow: hidden;
}

.creative .skill-level {
    height: 100%;
    background-color: #3498db;
    border-radius: 3px;
}

.creative .resume-main {
    padding: 2rem;
}

.creative .main-heading {
    font-size: 1.5rem;
    color: #2c3e50;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.creative .experience-item, .creative .education-item {
    margin-bottom: 2rem;
}

.creative .experience-header, .creative .education-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.creative .experience-title, .creative .education-degree {
    font-size: 1.2rem;
    margin: 0;
    color: #2c3e50;
}

.creative .experience-company, .creative .education-institution {
    font-size: 1rem;
    color: #7f8c8d;
    margin: 0.25rem 0 0;
}

.creative .experience-date, .creative .education-date {
    font-size: 0.9rem;
    color: #bdc3c7;
}

.creative .projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.creative .project-card {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.creative .project-title {
    font-size: 1.1rem;
    margin: 0 0 0.75rem;
    color: #2c3e50;
}

.creative .project-link {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 0.75rem;
}

.creative .certifications-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.creative .certification-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.creative .certification-icon {
    width: 30px;
    height: 30px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.creative .certification-name {
    font-size: 1rem;
    margin: 0 0 0.25rem;
}

.creative .certification-meta {
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Professional Template */
.resume-template.professional {
    font-family: 'Source Sans Pro', sans-serif;
    color: #333;
}

.professional .resume-header {
    background-color: #2c3e50;
    color: white;
    padding: 2rem;
    margin: -0.5in -0.5in 2rem;
}

.professional .resume-name {
    font-size: 2.5rem;
    margin: 0;
}

.professional .resume-title {
    font-size: 1.2rem;
    color: #bdc3c7;
    margin: 0.5rem 0 1.5rem;
}

.professional .contact-bar {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.professional .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.professional .resume-body {
    display: grid;
    grid-template-columns: 7fr 3fr;
    gap: 2rem;
}

.professional .section-title {
    font-size: 1.4rem;
    color: #2c3e50;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #2c3e50;
}

.professional .experience-item, .professional .education-item, .professional .project-item {
    margin-bottom: 2rem;
}

.professional .experience-header, .professional .education-header, .professional .project-header {
    margin-bottom: 0.75rem;
}

.professional .experience-title, .professional .education-degree, .professional .project-title {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.professional .experience-company, .professional .education-institution {
    font-size: 1rem;
    color: #7f8c8d;
    margin: 0 0 0.25rem;
}

.professional .experience-period, .professional .education-period {
    font-size: 0.9rem;
    color: #95a5a6;
}

.professional .skills-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.professional .skill-item {
    background-color: #f8f9fa;
    padding: 0.5rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.professional .certification-item {
    margin-bottom: 1.5rem;
}

.professional .certification-name {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.professional .certification-issuer, .professional .certification-date {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin: 0;
}

/* Elegant Template */
.resume-template.elegant {
    font-family: 'Playfair Display', serif;
    color: #333;
    padding: 1in;
}

.elegant .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.elegant .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #2c3e50;
}

.elegant .resume-title {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin: 0.5rem 0 0;
    font-style: italic;
}

.elegant .contact-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    font-size: 0.9rem;
}

.elegant .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.elegant .contact-item i {
    color: #2c3e50;
}

.elegant .resume-divider {
    width: 100%;
    height: 1px;
    background-color: #eee;
    margin: 1.5rem 0;
}

.elegant .resume-summary {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    font-style: italic;
}

.elegant .resume-body {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.elegant .section-title {
    font-size: 1.4rem;
    color: #2c3e50;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.elegant .item-header {
    margin-bottom: 0.75rem;
}

.elegant .item-title {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.elegant .item-subtitle {
    font-size: 1rem;
    color: #7f8c8d;
    margin: 0 0 0.25rem;
}

.elegant .item-period {
    font-size: 0.9rem;
    color: #95a5a6;
}

.elegant .skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.elegant .skill-item {
    background-color: #f8f9fa;
    padding: 0.5rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
    text-align: center;
}

/* Tech Template */
.resume-template.tech {
    font-family: 'Roboto Mono', monospace;
    color: #333;
}

.tech .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
}

.tech .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #2c3e50;
}

.tech .resume-title {
    font-size: 1.2rem;
    color: #3498db;
    margin: 0.5rem 0 0;
}

.tech .contact-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    font-size: 0.9rem;
}

.tech .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tech .contact-item i {
    color: #3498db;
    width: 16px;
}

.tech .resume-body {
    display: grid;
    grid-template-columns: 3fr 7fr;
    gap: 2rem;
}

.tech .section-title {
    font-size: 1.4rem;
    color: #2c3e50;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.tech .skill-item {
    margin-bottom: 1rem;
}

.tech .skill-name {
    margin-bottom: 0.5rem;
    display: block;
}

.tech .skill-bar {
    height: 6px;
    background-color: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.tech .skill-progress {
    height: 100%;
    background-color: #3498db;
    border-radius: 3px;
}

.tech .certification-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.tech .certification-icon {
    width: 30px;
    height: 30px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.tech .certification-name {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.tech .certification-meta {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.tech .timeline {
    position: relative;
}

.tech .timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #ecf0f1;
}

.tech .timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.tech .timeline-marker {
    position: absolute;
    left: -6px;
    top: 0.5rem;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #3498db;
}

.tech .timeline-header {
    margin-bottom: 0.75rem;
}

.tech .timeline-title {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.tech .timeline-subtitle {
    font-size: 1rem;
    color: #7f8c8d;
    margin: 0 0 0.25rem;
}

.tech .timeline-period {
    font-size: 0.9rem;
    color: #95a5a6;
}

.tech .projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.tech .project-card {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tech .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.tech .project-title {
    font-size: 1.1rem;
    margin: 0;
    color: #2c3e50;
}

.tech .project-link {
    color: #3498db;
    font-size: 1rem;
}

/* Classic Template */
.resume-template.classic {
    font-family: 'Times New Roman', serif;
    color: #333;
    padding: 1in;
}

.classic .resume-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #ddd;
}

.classic .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #333;
}

.classic .resume-title {
    font-size: 1.2rem;
    color: #666;
    margin: 0.5rem 0 1.5rem;
}

.classic .contact-info {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.classic .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.classic .contact-item i {
    color: #666;
}

.classic .section-title {
    font-size: 1.4rem;
    color: #333;
    margin: 2rem 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.classic .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.classic .skill-tag {
    background-color: #f5f5f5;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.classic .content-item {
    margin-bottom: 1.5rem;
}

.classic .item-header {
    margin-bottom: 0.75rem;
}

.classic .item-title {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #333;
}

.classic .item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.classic .item-company, .classic .item-institution, .classic .item-issuer {
    font-weight: 500;
}

/* Minimalist Template */
.resume-template.minimalist {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    color: #333;
    padding: 1in;
    line-height: 1.5;
}

.minimalist .resume-header {
    margin-bottom: 2rem;
}

.minimalist .resume-name {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
    color: #333;
}

.minimalist .resume-title {
    font-size: 1.2rem;
    color: #666;
    margin: 0.5rem 0 0;
    font-weight: 300;
}

.minimalist .resume-contact {
    margin: 1.5rem 0;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.minimalist .contact-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.minimalist .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.minimalist .contact-item i {
    color: #666;
    width: 16px;
}

.minimalist .section-title {
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 2rem 0 1rem;
    color: #333;
    font-weight: 500;
}

.minimalist .summary-content {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
}

.minimalist .section-item {
    margin-bottom: 1.5rem;
}

.minimalist .item-header {
    margin-bottom: 0.75rem;
}

.minimalist .item-header h3 {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #333;
    font-weight: 500;
}

.minimalist .item-date {
    font-size: 0.9rem;
    color: #666;
}

.minimalist .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.minimalist .skill-item {
    background-color: #f5f5f5;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.minimalist .project-link {
    color: #0066cc;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.minimalist .cert-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.minimalist .cert-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
}

.minimalist .cert-name {
    font-weight: 500;
}

.minimalist .cert-meta {
    color: #666;
}

/* Corporate Template */
.resume-template.corporate {
    font-family: 'Arial', sans-serif;
    color: #333;
}

.corporate .resume-header {
    background-color: #1e3a8a;
    color: white;
    padding: 2rem;
    margin: -0.5in -0.5in 2rem;
}

.corporate .resume-name {
    font-size: 2.5rem;
    margin: 0;
}

.corporate .resume-title {
    font-size: 1.2rem;
    color: #d1d5db;
    margin: 0.5rem 0 1.5rem;
}

.corporate .contact-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.corporate .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.corporate .resume-body {
    display: grid;
    grid-template-columns: 7fr 3fr;
    gap: 2rem;
}

.corporate .section-title {
    font-size: 1.4rem;
    color: #1e3a8a;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #1e3a8a;
}

.corporate .experience-item, .corporate .education-item {
    margin-bottom: 2rem;
}

.corporate .experience-header, .corporate .education-header {
    margin-bottom: 0.75rem;
}

.corporate .company-name, .corporate .institution-name {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #1e3a8a;
}

.corporate .position-title, .corporate .degree-title {
    font-size: 1.1rem;
    color: #4b5563;
    margin: 0 0 0.25rem;
}

.corporate .date-range {
    font-size: 0.9rem;
    color: #6b7280;
}

.corporate .skills-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.corporate .skill-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.corporate .skill-bullet {
    color: #1e3a8a;
    font-size: 1.2rem;
}

.corporate .certification-item, .corporate .project-item {
    margin-bottom: 1.5rem;
}

.corporate .certification-name, .corporate .project-name {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: #1e3a8a;
}

.corporate .certification-details {
    font-size: 0.9rem;
    color: #6b7280;
}

.corporate .project-link {
    color: #1e3a8a;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

/* Academic Template */
.resume-template.academic {
    font-family: 'Garamond', serif;
    color: #333;
    padding: 1in;
}

.academic .resume-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #ddd;
}

.academic .resume-name {
    font-size: 2.5rem;
    margin: 0;
    color: #333;
}

.academic .resume-title {
    font-size: 1.2rem;
    color: #666;
    margin: 0.5rem 0 1.5rem;
}

.academic .contact-row {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.academic .contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.academic .contact-item i {
    color: #666;
}

.academic .section-title {
    font-size: 1.4rem;
    color: #333;
    margin: 2rem 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.academic .education-item, .academic .experience-item, .academic .project-item, .academic .certification-item {
    margin-bottom: 1.5rem;
}

.academic .item-header {
    margin-bottom: 0.75rem;
}

.academic .degree, .academic .position, .academic .project-title, .academic .certification-name {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #333;
}

.academic .institution, .academic .date-range {
    font-size: 1rem;
    color: #666;
    margin: 0 0 0.25rem;
}

.academic .skills-categories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.academic .category-title {
    font-size: 1.1rem;
    margin: 0 0 1rem;
    color: #333;
}

.academic .skills-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.academic .skill-item {
    font-size: 0.9rem;
    padding: 0.25rem 0;
    border-bottom: 1px dotted #ddd;
}

.academic .project-link {
    color: #0066cc;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

/* Creative Modern Template */
.resume-template.creative-modern {
    font-family: 'Montserrat', sans-serif;
    display: grid;
    grid-template-columns: 3fr 7fr;
    gap: 0;
    padding: 0;
}

.creative-modern .resume-sidebar {
    background-color: #2c3e50;
    color: white;
    padding: 2rem;
}

.creative-modern .sidebar-header {
    text-align: center;
    margin-bottom: 2rem;
}

.creative-modern .profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #3498db;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1rem;
}

.creative-modern .profile-initial {
    font-size: 3rem;
    font-weight: bold;
    color: white;
}

.creative-modern .profile-name {
    font-size: 1.8rem;
    margin: 0 0 0.25rem;
    line-height: 1.2;
}

.creative-modern .profile-title {
    font-size: 1rem;
    color: #bdc3c7;
    margin: 0;
}

.creative-modern .section-title {
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 2rem 0 1rem;
    color: #3498db;
}

.creative-modern .contact-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.creative-modern .contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.creative-modern .contact-icon {
    width: 30px;
    height: 30px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.creative-modern .contact-info {
    font-size: 0.9rem;
}

.creative-modern .skill-item {
    margin-bottom: 1rem;
}

.creative-modern .skill-name {
    margin-bottom: 0.5rem;
    display: block;
}

.creative-modern .skill-bar {
    height: 6px;
    background-color: #34495e;
    border-radius: 3px;
    overflow: hidden;
}

.creative-modern .skill-progress {
    height: 100%;
    background-color: #3498db;
    border-radius: 3px;
}

.creative-modern .certification-item {
    margin-bottom: 1.5rem;
}

.creative-modern .certification-name {
    font-size: 1.1rem;
    margin: 0 0 0.25rem;
    color: white;
}

.creative-modern .certification-meta {
    font-size: 0.8rem;
    color: #bdc3c7;
}

.creative-modern .resume-main {
    padding: 2rem;
}

.creative-modern .main-section {
    margin-bottom: 2.5rem;
}

.creative-modern .section-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.creative-modern .timeline {
    position: relative;
}

.creative-modern .timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.creative-modern .timeline-dot {
    position: absolute;
    left: -6px;
    top: 0.5rem;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #3498db;
}

.creative-modern .timeline-item::before {
    content: '';
    position: absolute;
    left: 1px;
    top: 0.5rem;
    bottom: -2rem;
    width: 2px;
    background-color: #ecf0f1;
}

.creative-modern .timeline-item:last-child::before {
    display: none;
}

.creative-modern .timeline-date {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.creative-modern .timeline-title {
    font-size: 1.2rem;
    margin: 0 0 0.25rem;
    color: #2c3e50;
}

.creative-modern .timeline-subtitle {
    font-size: 1rem;
    color: #7f8c8d;
    margin: 0 0 0.75rem;
}

.creative-modern .projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.creative-modern .project-card {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.creative-modern .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.creative-modern .project-title {
    font-size: 1.1rem;
    margin: 0;
    color: #2c3e50;
}

.creative-modern .project-link {
    color: #3498db;
    font-size: 1rem;
}
