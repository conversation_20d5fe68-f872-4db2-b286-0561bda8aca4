document.addEventListener('DOMContentLoaded', function() {
    // Color scheme selection
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            colorOptions.forEach(opt => opt.classList.remove('active'));
            // Add active class to selected option
            this.classList.add('active');
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
        });
    });

    // Font family selection
    const fontOptions = document.querySelectorAll('.font-option');
    fontOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            fontOptions.forEach(opt => opt.classList.remove('active'));
            // Add active class to selected option
            this.classList.add('active');
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
        });
    });

    // Form validation
    const form = document.querySelector('.resume-options-form');
    form.addEventListener('submit', function(event) {
        // Get the resume name
        const resumeName = document.getElementById('resume_name').value.trim();
        
        // Validate resume name
        if (!resumeName) {
            event.preventDefault();
            alert('Please enter a resume name');
            return;
        }
        
        // Check if at least one section is selected
        const sections = document.querySelectorAll('input[name="sections[]"]:checked');
        if (sections.length === 0) {
            event.preventDefault();
            alert('Please select at least one section to include in your resume');
            return;
        }

        // Add loading state
        const submitButton = form.querySelector('button[type="submit"]');
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
        submitButton.disabled = true;
        
        // Continue with form submission
    });
});