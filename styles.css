/* Base Styles */
:root {
    --primary-color: #4f46e5;
    --primary-dark: #4338ca;
    --primary-light: #818cf8;
    --secondary-color: #10b981;
    --text-color: #1f2937;
    --text-light: #6b7280;
    --background: #ffffff;
    --background-alt: #f9fafb;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
      "Helvetica Neue", sans-serif;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: var(--font-family);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
  }
  
  a:hover {
    color: var(--primary-dark);
  }
  
  ul {
    list-style: none;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .hidden {
    display: none !important;
  }
  
  /* Button Styles */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    border: none;
    outline: none;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
  }
  
  .btn-primary:hover {
    background-color: var(--primary-dark);
    color: white;
  }
  
  .btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
  }
  
  .btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
  }
  
  .btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }
  
  .btn i {
    margin-right: 0.5rem;
  }
  
  /* Header & Navigation */
  header {
    background-color: var(--background);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
  }
  
  .navbar {
    padding: 1rem 0;
  }
  
  .navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .logo a {
    display: flex;
    align-items: center;
    color: var(--text-color);
  }
  
  .logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
  }
  
  .logo span {
    color: var(--primary-color);
  }
  
  .nav-links ul {
    display: flex;
    gap: 2rem;
  }
  
  .nav-links a {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
  }
  
  .nav-links a:hover,
  .nav-links a.active {
    color: var(--primary-color);
  }
  
  .nav-links a.active::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
  }
  
  .auth-buttons {
    display: flex;
    gap: 1rem;
  }
  
  .user-menu {
    position: relative;
    cursor: pointer;
  }
  
  .user-avatar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .user-avatar img {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 200px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
    z-index: 10;
  }
  
  .user-menu:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  
  .dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    transition: var(--transition);
  }
  
  .dropdown-menu a:hover {
    background-color: var(--background-alt);
    color: var(--primary-color);
  }
  
  .dropdown-menu i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 1.25rem;
  }
  
  .menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
  }
  
  /* Hero Section */
  .hero {
    padding: 5rem 0;
    background-color: var(--background-alt);
    position: relative;
    overflow: hidden;
  }
  
  .hero .container {
    display: flex;
    align-items: center;
    gap: 2rem;
  }
  
  .hero-content {
    flex: 1;
  }
  
  .hero-content h1 {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-color);
  }
  
  .hero-content p {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    max-width: 600px;
  }
  
  .hero-buttons {
    display: flex;
    gap: 1rem;
  }
  
  .hero-image {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
  
  .hero-image img {
    max-width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  /* Section Styles */
  section {
    padding: 5rem 0;
  }
  
  section:nth-child(even) {
    background-color: var(--background-alt);
  }
  
  .section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
  }
  
  .section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
  }
  
  .section-header p {
    font-size: 1.125rem;
    color: var(--text-light);
  }
  
  /* Features Section */
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .feature-card {
    background-color: var(--background);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-align: center;
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 50%;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
  
  .feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-color);
  }
  
  .feature-card p {
    color: var(--text-light);
  }
  
  /* How It Works Section */
  .steps {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    margin-top: 3rem;
  }
  
  .step {
    flex: 1;
    text-align: center;
    position: relative;
  }
  
  .step:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 3rem;
    right: -1rem;
    width: 2rem;
    height: 2px;
    background-color: var(--border-color);
  }
  
  .step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  
  .step-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    background-color: var(--background);
    color: var(--primary-color);
    border-radius: 50%;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    box-shadow: var(--box-shadow);
  }
  
  .step h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }
  
  .step p {
    color: var(--text-light);
  }
  
  /* Templates Preview Section */
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  .template-card {
    background-color: var(--background);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
  }
  
  .template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .template-image {
    position: relative;
    overflow: hidden;
    height: 350px;
  }
  
  .template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
  }
  
  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition);
  }
  
  .template-card:hover .template-overlay {
    opacity: 1;
  }
  
  .template-card h3 {
    padding: 1rem;
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }
  
  .template-card p {
    padding: 0 1rem 1rem;
    color: var(--text-light);
  }
  
  .templates-cta {
    text-align: center;
  }
  
  /* Testimonials Section */
  .testimonials-slider {
    display: flex;
    overflow-x: hidden;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    margin-bottom: 2rem;
  }
  
  .testimonial {
    flex: 0 0 100%;
    scroll-snap-align: start;
    padding: 2rem;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }
  
  .testimonial-content {
    margin-bottom: 1.5rem;
  }
  
  .testimonial-content p {
    font-size: 1.125rem;
    font-style: italic;
    color: var(--text-color);
    line-height: 1.7;
  }
  
  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .testimonial-author img {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .author-info h4 {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
  }
  
  .author-info p {
    color: var(--text-light);
  }
  
  .testimonial-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: var(--border-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .indicator.active {
    background-color: var(--primary-color);
  }
  
  /* CTA Section */
  .cta {
    background-color: var(--primary-color);
    color: white;
    padding: 5rem 0;
  }
  
  .cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .cta h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  
  .cta p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  
  .cta .btn-primary {
    background-color: white;
    color: var(--primary-color);
  }
  
  .cta .btn-primary:hover {
    background-color: var(--background-alt);
  }
  
  /* Footer */
  footer {
    background-color: var(--text-color);
    color: white;
    padding: 4rem 0 1rem;
  }
  
  .footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    margin-bottom: 3rem;
  }
  
  .footer-logo {
    flex: 1;
    min-width: 250px;
  }
  
  .footer-logo h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .footer-logo span {
    color: var(--primary-light);
  }
  
  .footer-logo p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
  }
  
  .social-links {
    display: flex;
    gap: 1rem;
  }
  
  .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    transition: var(--transition);
  }
  
  .social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
  }
  
  .footer-links {
    flex: 2;
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
  }
  
  .footer-column {
    flex: 1;
    min-width: 150px;
  }
  
  .footer-column h3 {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    color: white;
  }
  
  .footer-column ul li {
    margin-bottom: 0.75rem;
  }
  
  .footer-column a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition);
  }
  
  .footer-column a:hover {
    color: white;
  }
  
  .footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.875rem;
    opacity: 0.7;
  }
  
  /* Responsive Styles */
  @media (max-width: 992px) {
    .hero-content h1 {
      font-size: 2.5rem;
    }
  
    .steps {
      flex-direction: column;
      max-width: 500px;
      margin: 0 auto;
    }
  
    .step:not(:last-child)::after {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .hero .container {
      flex-direction: column;
    }
  
    .hero-image {
      order: -1;
      margin-bottom: 2rem;
    }
  
    .hero-content {
      text-align: center;
    }
  
    .hero-buttons {
      justify-content: center;
    }
  
    .nav-links {
      display: none;
    }
  
    .menu-toggle {
      display: block;
    }
  
    .auth-buttons {
      display: none;
    }
  
    .footer-content {
      flex-direction: column;
      gap: 2rem;
    }
  }
  
  @media (max-width: 576px) {
    .hero-content h1 {
      font-size: 2rem;
    }
  
    .section-header h2 {
      font-size: 2rem;
    }
  
    .btn-lg {
      padding: 0.5rem 1.5rem;
    }
  
    .features-grid {
      grid-template-columns: 1fr;
    }
  
    .templates-grid {
      grid-template-columns: 1fr;
    }
  }
  