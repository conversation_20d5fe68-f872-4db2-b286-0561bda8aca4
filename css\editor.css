/* Resume Editor Styles */
:root {
    --sidebar-width: 280px;
    --properties-width: 300px;
    --header-height: 64px;
    --toolbar-height: 50px;
    --resume-width: 8.5in;
    --resume-height: 11in;
    --resume-scale: 0.7;
  }
  
  body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100vh;
    background-color: var(--background-alt);
  }
  
  .editor-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }
  
  /* Header Styles */
  .editor-header {
    height: var(--header-height);
    background-color: var(--background);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    z-index: 100;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    color: var(--text-color);
    transition: var(--transition);
  }
  
  .back-btn:hover {
    background-color: var(--background-alt);
  }
  
  .document-info {
    display: flex;
    flex-direction: column;
  }
  
  .document-name {
    font-weight: 600;
    font-size: 1.125rem;
    outline: none;
    border: 1px solid transparent;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
  }
  
  .document-name:focus {
    border-color: var(--primary-color);
    background-color: var(--background-alt);
  }
  
  .document-status {
    font-size: 0.75rem;
    color: var(--text-light);
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .status-indicator.saved {
    color: var(--success-color);
  }
  
  .status-indicator.unsaved {
    color: var(--warning-color);
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .template-selector {
    margin-right: 0.5rem;
  }
  
  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }
  
  .dropdown {
    position: relative;
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 180px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
    z-index: 10;
  }
  
  .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  
  .dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    transition: var(--transition);
  }
  
  .dropdown-menu a:hover {
    background-color: var(--background-alt);
    color: var(--primary-color);
  }
  
  .dropdown-menu i {
    margin-right: 0.75rem;
    font-size: 1rem;
    width: 1.25rem;
  }
  
  .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .btn-sm i {
    margin-right: 0.25rem;
  }
  
  /* Editor Content */
  .editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  
  /* Sidebar Styles */
  .editor-sidebar {
    width: var(--sidebar-width);
    background-color: var(--background);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    transition: var(--transition);
  }
  
  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .sidebar-header h3 {
    font-size: 1rem;
    margin: 0;
  }
  
  .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-icon:hover {
    background-color: var(--background-alt);
    color: var(--primary-color);
  }
  
  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
  }
  
  .section-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .section-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .section-item:hover {
    background-color: var(--background-alt);
  }
  
  .section-item.active {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
  }
  
  .section-drag-handle {
    margin-right: 0.75rem;
    color: var(--text-light);
    cursor: grab;
  }
  
  .section-info {
    flex: 1;
    font-size: 0.875rem;
  }
  
  .section-actions {
    opacity: 0;
    transition: var(--transition);
  }
  
  .section-item:hover .section-actions {
    opacity: 1;
  }
  
  .sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
  }
  
  .btn-block {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  /* Main Editor Area */
  .editor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    transition: var(--transition);
  }
  
  .editor-toolbar {
    height: var(--toolbar-height);
    background-color: var(--background);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1rem;
    gap: 1rem;
    overflow-x: auto;
  }
  
  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0 0.5rem;
    border-right: 1px solid var(--border-color);
  }
  
  .toolbar-group:last-child {
    border-right: none;
  }
  
  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: none;
    border: none;
    border-radius: 0.25rem;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .toolbar-btn:hover {
    background-color: var(--background-alt);
  }
  
  .toolbar-btn.active {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
  }
  
  .font-selector,
  .font-size-selector {
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    outline: none;
    cursor: pointer;
  }
  
  .editor-workspace {
    flex: 1;
    overflow: auto;
    background-color: var(--background-alt);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 2rem;
  }
  
  .resume-preview {
    background-color: var(--background-alt);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transform: scale(var(--resume-scale));
    transform-origin: top center;
    transition: transform 0.3s ease;
  }
  
  .resume-page {
    width: var(--resume-width);
    min-height: var(--resume-height);
    background-color: white;
    padding: 1in;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .resume-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .section-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.25rem;
    outline: none;
  }
  
  .section-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  /* Personal Section */
  .personal-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .personal-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }
  
  .personal-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
  }
  
  .avatar-placeholder {
    width: 100%;
    height: 100%;
    background-color: var(--background-alt);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 2.5rem;
    position: relative;
  }
  
  .upload-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.875rem;
  }
  
  .personal-name-title {
    flex: 1;
  }
  
  .resume-name {
    margin: 0 0 0.5rem;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    outline: none;
  }
  
  .resume-title {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-light);
    font-weight: 500;
    outline: none;
  }
  
  .personal-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem 2rem;
  }
  
  .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }
  
  .detail-item i {
    color: var(--primary-color);
    width: 1rem;
    text-align: center;
  }
  
  .detail-item span {
    outline: none;
  }
  
  /* Summary Section */
  .summary-text {
    margin: 0;
    line-height: 1.6;
    outline: none;
  }
  
  /* Experience Section */
  .experience-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .experience-item:last-child {
    border-bottom: none;
  }
  
  .experience-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .experience-header h3 {
    margin: 0 0 0.25rem;
    font-size: 1.125rem;
    font-weight: 600;
    outline: none;
  }
  
  .experience-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-light);
    outline: none;
  }
  
  .experience-date {
    font-size: 0.875rem;
    color: var(--text-light);
    white-space: nowrap;
  }
  
  .experience-date span {
    outline: none;
  }
  
  .experience-description ul {
    margin: 0;
    padding-left: 1.5rem;
  }
  
  .experience-description li {
    margin-bottom: 0.25rem;
    line-height: 1.5;
    outline: none;
  }
  
  .item-actions {
    position: absolute;
    top: 0;
    right: -3rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
  }
  
  .experience-item:hover .item-actions,
  .education-item:hover .item-actions,
  .certification-item:hover .item-actions {
    opacity: 1;
  }
  
  .add-item-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: var(--background-alt);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .add-item-btn:hover {
    background-color: rgba(79, 70, 229, 0.1);
  }
  
  /* Education Section */
  .education-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .education-item:last-child {
    border-bottom: none;
  }
  
  .education-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .education-header h3 {
    margin: 0 0 0.25rem;
    font-size: 1.125rem;
    font-weight: 600;
    outline: none;
  }
  
  .education-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-light);
    outline: none;
  }
  
  .education-date {
    font-size: 0.875rem;
    color: var(--text-light);
    white-space: nowrap;
  }
  
  .education-date span {
    outline: none;
  }
  
  .education-description p {
    margin: 0 0 0.25rem;
    line-height: 1.5;
    outline: none;
  }
  
  /* Skills Section */
  .skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }
  
  .skill-item {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    outline: none;
  }
  
  .add-skill-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--background-alt);
    border: 1px dashed var(--border-color);
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .add-skill-btn:hover {
    background-color: rgba(79, 70, 229, 0.1);
  }
  
  /* Certifications Section */
  .certification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .certification-item:last-child {
    border-bottom: none;
  }
  
  .certification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .certification-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    outline: none;
  }
  
  .certification-header span {
    font-size: 0.875rem;
    color: var(--text-light);
    outline: none;
  }
  
  /* Properties Panel */
  .editor-properties {
    width: var(--properties-width);
    background-color: var(--background);
    border-left: 1px solid var(--border-color);
    height: calc(100vh - var(--header-height));
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    transform: translateX(100%);
  }
  
  .editor-properties.active {
    transform: translateX(0);
  }
  
  .properties-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .properties-header h3 {
    font-size: 1rem;
    margin: 0;
  }
  
  .properties-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .property-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .property-group h4 {
    font-size: 0.875rem;
    margin: 0 0 0.5rem;
    color: var(--text-light);
    font-weight: 600;
  }
  
  .property-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .property-item label {
    font-size: 0.875rem;
    color: var(--text-color);
  }
  
  .property-item input[type="text"],
  .property-item select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    outline: none;
    transition: var(--transition);
  }
  
  .property-item input[type="text"]:focus,
  .property-item select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: var(--transition);
    border-radius: 20px;
  }
  
  .toggle-switch label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
  }
  
  .toggle-switch input:checked + label {
    background-color: var(--primary-color);
  }
  
  .toggle-switch input:checked + label:before {
    transform: translateX(20px);
  }
  
  .range-slider {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .range-slider input {
    flex: 1;
  }
  
  .range-value {
    font-size: 0.875rem;
    min-width: 1.5rem;
    text-align: center;
  }
  
  .number-input {
    display: flex;
    align-items: center;
  }
  
  .number-input input {
    width: 3rem;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 0;
    padding: 0.5rem;
    font-size: 0.875rem;
    outline: none;
  }
  
  .number-down,
  .number-up {
    background-color: var(--background-alt);
    border: 1px solid var(--border-color);
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .number-down {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
  }
  
  .number-up {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
  }
  
  .property-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: auto;
    padding-top: 1.5rem;
  }
  
  .property-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  /* Modal Styles */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .modal.active {
    opacity: 1;
    visibility: visible;
  }
  
  .modal-content {
    background-color: var(--background);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: var(--box-shadow);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
  }
  
  .modal.active .modal-content {
    transform: translateY(0);
  }
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
  }
  
  .close-modal {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .close-modal:hover {
    color: var(--primary-color);
  }
  
  .modal-body {
    flex: 1;
    overflow: auto;
    padding: 1.5rem;
  }
  
  .preview-container {
    width: 100%;
    height: 60vh;
    overflow: auto;
    background-color: var(--background-alt);
    border-radius: var(--border-radius);
  }
  
  .preview-container iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  
  .modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
  }
  
  /* Responsive Styles */
  @media (max-width: 1200px) {
    :root {
      --resume-scale: 0.6;
    }
  }
  
  @media (max-width: 992px) {
    :root {
      --sidebar-width: 240px;
      --resume-scale: 0.5;
    }
  
    .editor-properties {
      position: fixed;
      right: 0;
      z-index: 100;
      box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    }
  
    .header-actions {
      gap: 0.5rem;
    }
  
    .btn-sm span {
      display: none;
    }
  
    .btn-sm i {
      margin-right: 0;
    }
  }
  
  @media (max-width: 768px) {
    :root {
      --resume-scale: 0.4;
    }
  
    .editor-sidebar {
      position: fixed;
      left: 0;
      z-index: 100;
      transform: translateX(-100%);
      box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
    }
  
    .editor-sidebar.active {
      transform: translateX(0);
    }
  
    .editor-main {
      margin-left: 0;
    }
  
    .toolbar-group:not(:first-child):not(:last-child) {
      display: none;
    }
  }
  
  @media (max-width: 576px) {
    :root {
      --resume-scale: 0.3;
    }
  
    .editor-header {
      padding: 0 1rem;
    }
  
    .document-name {
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  
    .template-selector {
      display: none;
    }
  }
  