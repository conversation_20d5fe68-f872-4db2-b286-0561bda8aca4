import React from "react";

const sampleResume = {
  basics: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    headline: "Software Engineer",
    location: "Bangalore, India",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    url: { href: "https://abhi.dev", label: "Portfolio" },
    customFields: [
      { id: "1", name: "LinkedIn", value: "https://linkedin.com/in/abhikumar" },
      { id: "2", name: "GitHub", value: "https://github.com/abhiCoder" },
    ],
    image: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  sections: {
    summary: {
      visible: true,
      name: "Profile Summary",
      content: "<p>Results-driven Software Engineer with 3+ years of experience in full-stack development. Skilled in React, Node.js, and cloud technologies.</p>",
      id: "summary",
    },
    experience: {
      visible: true,
      name: "Work Experience",
      id: "experience",
      items: [
        {
          id: "exp1",
          company: "InnoTech",
          position: "Frontend Developer",
          date: "2019 - 2022",
          location: "Remote",
          summary: '<p>Developed React dashboard, integrated APIs, collaborated with backend team.</p>',
        },
      ],
    },
    education: {
      visible: true,
      name: "Education",
      id: "education",
      items: [
        {
          id: "edu1",
          institution: "NIT Trichy",
          studyType: "B.Tech",
          area: "Computer Science and Engineering",
          date: "2018 - 2022",
        },
        {
          id: "edu2",
          institution: "Some College",
          studyType: "Diploma",
          area: "Information Technology",
          date: "2015 - 2018",
        },
      ],
    },
    skills: {
      visible: true,
      name: "Skills",
      id: "skills",
      items: [
        { id: "sk1", name: "UI/UX" },
        { id: "sk2", name: "Visual Design" },
        { id: "sk3", name: "Wireframes" },
        { id: "sk4", name: "Storyboards" },
        { id: "sk5", name: "User Flows" },
        { id: "sk6", name: "Process Flows" },
      ],
    },
    languages: {
      visible: true,
      name: "Languages",
      id: "languages",
      items: [
        { id: "lang1", name: "English", level: "Fluent" },
        { id: "lang2", name: "Hindi", level: "Native" },
        { id: "lang3", name: "Kannada", level: "Conversational" },
      ],
    },
    awards: {
      visible: true,
      name: "Awards",
      id: "awards",
      items: [
        { id: "award1", title: "Best Developer Award", issuer: "InnoTech", year: "2021" },
        { id: "award2", title: "Hackathon Winner", issuer: "NIT Trichy", year: "2020" },
      ],
    },
    volunteering: {
      visible: true,
      name: "Volunteering",
      id: "volunteering",
      items: [
        {
          id: "vol1",
          organization: "Code for Good",
          position: "Volunteer Developer",
          date: "2020 - 2021",
          summary: '<p>Contributed to open-source projects and mentored junior developers.</p>',
        },
      ],
    },
    references: {
      visible: true,
      name: "References",
      id: "references",
      items: [
        { id: "ref1", name: "John Doe", detail: "Senior Developer, InnoTech (<EMAIL>)" },
        { id: "ref2", name: "Jane Smith", detail: "Project Manager, WebStart (<EMAIL>)" },
      ],
    },
  },
};

const Sidebar = ({ basics, skills, languages }) => (
  <aside className="bg-gray-900 text-white w-56 min-h-full flex flex-col items-center py-2 px-2">
    <img
      src={basics.image}
      alt="Profile"
      className="w-28 h-28 rounded-full object-cover mb-3"
    />
    <div className="w-full mb-4">
      <h3 className="text-lg font-bold tracking-widest mb-4 text-gray-400">CONTACT</h3>
      <div className="text-xs space-y-1">
        <div className="flex items-center gap-2"><span className="material-icons text-xs">phone</span>{basics.phone}</div>
        <div className="flex items-center gap-2"><span className="material-icons text-xs">email</span>{basics.email}</div>
        <div className="flex items-center gap-2"><span className="material-icons text-xs">language</span><a href={basics.url.href} className="underline text-blue-300">{basics.url.label}</a></div>
        <div className="flex items-center gap-2"><span className="material-icons text-xs">location_on</span>{basics.location}</div>
        {basics.customFields.map(field => (
          <div key={field.id} className="flex items-center gap-2"><span className="material-icons text-xs">link</span><a href={field.value} className="underline text-blue-300">{field.name}</a></div>
        ))}
      </div>
    </div>
    <div className="w-full mb-4">
      <h3 className="text-lg font-bold tracking-widest mb-4 text-gray-400">SKILLS</h3>
      <ul className="list-disc list-inside space-y-2 text-base">
        {skills.items.map(skill => (
          <li key={skill.id}>{skill.name}</li>
        ))}
      </ul>
    </div>
    {languages?.visible && languages.items.length > 0 && (
      <div className="w-full mb-4">
        <h3 className="text-lg font-bold tracking-widest mb-4 text-gray-400">LANGUAGES</h3>
        <ul className="list-disc list-inside space-y-2 text-base">
          {languages.items.map(lang => (
            <li key={lang.id}>{lang.name} <span className="text-gray-400">({lang.level})</span></li>
          ))}
        </ul>
      </div>
    )}
  </aside>
);

const StraightHorizontalSeparator = () => (
  <div className="my-2">
    <svg width="100%" height="2" viewBox="0 0 600 2" fill="none" xmlns="http://www.w3.org/2000/svg">
      <line x1="0" y1="1" x2="600" y2="1" stroke="#000" strokeWidth="2" />
    </svg>
  </div>
);

const MainContent = ({ basics, summary, experience, education, awards, volunteering, references }) => (
  <main className="flex-1 bg-white p-12">
    <div className="mb-4">
      <h1 className="text-4xl font-bold text-gray-900 leading-tight">{basics.name}</h1>
      <h2 className="text-xl text-gray-500 font-medium mb-4">{basics.headline}</h2>
    </div>
    <section className="mb-4">
      <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{summary.name}</h3>
      <div className="text-gray-700 text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: summary.content }} />
    </section>
    <StraightHorizontalSeparator />
    <section className="mb-4">
      <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{experience.name}</h3>
      <div className="space-y-6">
        {experience.items.map(exp => (
          <div key={exp.id}>
            <div className="flex justify-between items-center mb-1">
              <div>
                <div className="font-semibold text-base text-gray-900">{exp.position}</div>
                <div className="text-sm text-gray-600">{exp.company}</div>
              </div>
              <div className="text-sm text-gray-500 font-medium">{exp.date}</div>
            </div>
            <div className="text-gray-700 text-sm" dangerouslySetInnerHTML={{ __html: exp.summary }} />
          </div>
        ))}
      </div>
    </section>
    <StraightHorizontalSeparator />
    {education?.visible && education.items.length > 0 && (
      <>
        <section className="mb-4">
          <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{education.name}</h3>
          <div className="space-y-4">
            {education.items.map(edu => (
              <div key={edu.id}>
                <div className="flex justify-between items-center mb-1">
                  <div>
                    <div className="font-semibold text-base text-gray-900">{edu.studyType}</div>
                    <div className="text-sm text-gray-600">{edu.institution}</div>
                  </div>
                  <div className="text-sm text-gray-500 font-medium">{edu.date}</div>
                </div>
                <div className="text-gray-700 text-sm">{edu.area}</div>
              </div>
            ))}
          </div>
        </section>
        <StraightHorizontalSeparator />
      </>
    )}
    {awards?.visible && awards.items.length > 0 && (
      <>
        <section className="mb-4">
          <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{awards.name}</h3>
          <ul className="list-disc list-inside space-y-2 text-sm">
            {awards.items.map(award => (
              <li key={award.id}>
                <span className="font-semibold">{award.title}</span> <span className="text-gray-400">- {award.issuer}, {award.year}</span>
              </li>
            ))}
          </ul>
        </section>
        <StraightHorizontalSeparator />
      </>
    )}
    {volunteering?.visible && volunteering.items.length > 0 && (
      <>
        <section className="mb-4">
          <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{volunteering.name}</h3>
          <div className="space-y-6">
            {volunteering.items.map(vol => (
              <div key={vol.id}>
                <div className="flex justify-between items-center mb-1">
                  <div>
                    <div className="font-semibold text-base text-green-900">{vol.organization}</div>
                    <div className="text-sm text-green-700">{vol.position}</div>
                  </div>
                  <div className="text-sm text-green-600 font-medium">{vol.date}</div>
                </div>
                <div className="text-green-800 text-sm" dangerouslySetInnerHTML={{ __html: vol.summary }} />
              </div>
            ))}
          </div>
        </section>
        <StraightHorizontalSeparator />
      </>
    )}
    {references?.visible && references.items.length > 0 && (
      <section className="mb-4">
        <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">{references.name}</h3>
        <div className="space-y-2">
          {references.items.map(ref => (
            <div key={ref.id} className="border-l-4 border-purple-400 pl-4">
              <div className="font-semibold text-purple-900">{ref.name}</div>
              <div className="text-purple-700 text-sm">{ref.detail}</div>
            </div>
          ))}
        </div>
      </section>
    )}
  </main>
);

const Abhi2 = () => {
  const { basics, sections } = sampleResume;
  return (
    <div className="flex min-h-screen bg-gray-200 justify-center items-start py-8">
      <div style={{ width: '210mm', minHeight: '297mm' }} className="flex shadow-lg rounded overflow-hidden">
        <Sidebar
          basics={basics}
          skills={sections.skills}
          languages={sections.languages}
        />
        <MainContent
          basics={basics}
          summary={sections.summary}
          experience={sections.experience}
          education={sections.education}
          awards={sections.awards}
          volunteering={sections.volunteering}
          references={sections.references}
        />
      </div>
    </div>
  );
};

export default Abhi2;
