/* Resume Viewer Styles */
.viewer-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
  }
  
  .viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .viewer-title h1 {
    font-size: 1.8rem;
    color: rgb(25,65,75);
    margin-bottom: 0.5rem;
  }
  
  .viewer-title p {
    color: #666;
    font-size: 0.95rem;
  }
  
  .viewer-actions {
    display: flex;
    gap: 1rem;
  }
  
  .resume-viewer {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  
  
  .resume-container {
    background-color: #fff;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
  }
  
  .viewer-footer {
    display: flex;
    justify-content: flex-start;
  }
  
  /* Responsive styles */
  @media (max-width: 768px) {
    .viewer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  
    .viewer-actions {
      width: 100%;
      justify-content: space-between;
    }
  
    .resume-viewer {
      padding: 1rem;
    }
  }
  
  