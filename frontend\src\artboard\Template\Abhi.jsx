import React, { Fragment, useEffect } from "react";
import Languages from "./Components/Languages";
import { useSkillsStore } from "../../store/skillsStore";
import { useProfileStore } from "../../store/profileStore";
import { useExperienceStore } from "../../store/experienceStore";
import { useEducationStore } from "../../store/educationStore";
import Publications from "./Components/Publications";
import Awards from "./Components/Awards";
import Certifications from "./Components/Certifications";
import { useUserInfoStore } from "../../store/userInfoStore";
import { useAuthStore } from "../../store/authStore";
import ProfilePicture from "./Components/ProfilePicture";
import { useProjectStore } from "../../store/projectStore";
import { useCertificationsStore } from "../../store/certificationsStore";
import { useAwardsStore } from "../../store/awardStore";
const HeaderBar = ({ basics }) => (
  <header className="w-full bg-blue-900 text-white py-8 px-8 rounded-t-lg flex flex-col items-center mb-4">
    <h1 className="text-3xl font-bold">{basics.name}</h1>
    <p className="text-lg">{basics.headline}</p>
    <div className="flex flex-wrap justify-center gap-4 text-sm mt-2">
      <span>📍 {basics.location}</span>
      <span>📞 {basics.phone}</span>
      <span>
        ✉️{" "}
        <a href={`mailto:${basics.email}`} className="underline">
          {basics.email}
        </a>
      </span>
      <span>
        <a href={basics.url.href} className="underline">
          {basics.url.label}
        </a>
      </span>
    </div>
    <div className="flex gap-4 mt-2">
      {basics.customFields.map((field) => (
        <a
          key={field.id}
          href={field.value}
          target="_blank"
          rel="noreferrer"
          className="underline"
        >
          {field.name}
        </a>
      ))}
    </div>
  </header>
);

const SkillsBar = ({ skills }) => (
  <section className="bg-blue-50 p-4 rounded mb-6 flex flex-wrap gap-6 justify-center">
    {skills.map((skill) => (
      <div key={skill._id} className="text-sm font-semibold text-blue-900">
        <span className="uppercase text-blue-700 mr-1">{skill.Skill}:</span>
        <span>{skill.description}</span>
      </div>
    ))}
  </section>
);

const Section = ({ name, items, children }) => {
  if (!items || !items.length) return null;
  return (
    <section className="mb-8">
      <h3 className="text-lg font-bold text-blue-900 uppercase border-b-2 border-blue-200 pb-1 mb-4">
        {name}
      </h3>
      <div className="space-y-4">
        {items.map((item) => (
          <Fragment key={item._id}>{children(item)}</Fragment>
        ))}
      </div>
    </section>
  );
};

const Summary = ({ content }) => (
  <section className="mb-8">
    <h3 className="text-lg font-bold text-blue-900 uppercase border-b-2 border-blue-200 pb-1 mb-4">
      Summary
    </h3>
    <div
      dangerouslySetInnerHTML={{ __html: content }}
      className="text-gray-800 text-base leading-relaxed"
    />
  </section>
);

const AbhiFunctional = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { userInfo, fetchUserInfo } = useUserInfoStore();
  const { experiences, fetchExperiences } = useExperienceStore();
  const { education, fetchEducation } = useEducationStore();
  const { skills, fetchSkills } = useSkillsStore();
  const { projects, fetchProjects } = useProjectStore();
  const { awards, fetchAwards } = useAwardsStore();
  const { certifications, fetchCertifications } = useCertificationsStore();
  const { profiles, fetchProfiles } = useProfileStore();

  useEffect(() => {
    fetchCurrentUser();
    fetchUserInfo();
    fetchExperiences();
    fetchEducation();
    fetchSkills();
    fetchProjects();
    fetchAwards();
    fetchCertifications();
    fetchProfiles();
  }, []);

  const basics = {
    name: user?.name || "",
    headline: "Software Engineer",
    location: userInfo?.Location || "",
    phone: userInfo?.Phone || "",
    email: userInfo?.email || "",
    url: userInfo?.Website
      ? { href: userInfo.Website, label: "Portfolio" }
      : { href: "#", label: "Portfolio" },
    customFields: userInfo?.customFields || [],
  };

  return (
    <div className="flex justify-center items-start min-h-screen bg-gray-100 py-8">
      <div
        className="bg-white rounded-lg shadow-lg p-4 my-8 overflow-auto"
        style={{ width: "210mm", minHeight: "297mm", maxWidth: "100%" }}
      >
        <HeaderBar basics={basics} />
        <SkillsBar skills={skills} />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-1">
            <Section name="Education" items={education}>
              {(edu) => (
                <div>
                  <h4 className="font-bold">{edu.Institution}</h4>
                  <p className="text-sm font-semibold text-blue-900">
                    {edu.Degree}
                  </p>
                  <p className="text-xs text-gray-600">{edu.Score}</p>
                  <p className="text-xs text-gray-600">
                    {edu.StartDate} - {edu.EndDate}
                  </p>
                </div>
              )}
            </Section>

            <Section name="Projects" items={projects}>
              {(proj) => (
                <div>
                  <h4 className="font-bold">{proj.Name}</h4>
                  <p className="text-sm text-gray-700">{proj.Description}</p>
                  {proj.Website && (
                    <a
                      href={proj.Website}
                      className="text-blue-600 text-sm underline"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Visit
                    </a>
                  )}
                </div>
              )}
            </Section>

            <Section name="Certifications" items={certifications}>
              {(cert) => (
                <div>
                  <p className="font-bold">{cert.Name}</p>
                  <p className="text-sm italic text-gray-600">{cert.Issuer}</p>
                </div>
              )}
            </Section>
          </div>

          <div className="md:col-span-2">
            <Summary
              content={user?.summary || "<p>Passionate developer.</p>"}
            />

            <Section name="Experience" items={experiences}>
              {(exp) => (
                <div>
                  <h4 className="font-bold">{exp.Company}</h4>
                  <p className="text-sm font-semibold text-blue-900">
                    {exp.Position}
                  </p>
                  <p className="text-xs text-gray-600">
                    {exp.StartDate} - {exp.EndDate}
                  </p>
                  <div
                    className="text-sm text-gray-700"
                    dangerouslySetInnerHTML={{ __html: exp.Description }}
                  />
                </div>
              )}
            </Section>

            <Section name="Awards" items={awards}>
              {(award) => (
                <div>
                  <p className="font-bold">{award.Title}</p>
                  <p className="text-sm italic text-gray-600">
                    {award.Awarder}
                  </p>
                </div>
              )}
            </Section>

            <Section name="Profiles" items={profiles}>
              {(profile) => (
                <div>
                  <p className="font-bold">{profile.Network}</p>
                  <a
                    href={profile.ProfileLink}
                    className="text-blue-600 underline text-sm"
                    target="_blank"
                    rel="noreferrer"
                  >
                    {profile.Username}
                  </a>
                </div>
              )}
            </Section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AbhiFunctional;
