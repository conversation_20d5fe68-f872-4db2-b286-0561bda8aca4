import React, { useState, useMemo } from "react";
import { DragDrop<PERSON>ontext, Droppable, Draggable } from "@hello-pangea/dnd";
import { useTemplateStore } from "../../../store/templateStore";
import { TEMPLATE_ARRAY } from "../../Template";
import {
  FiChevronLeft,
  FiChevronRight,
  FiGrid,
  FiList,
  FiBookmark,
  FiX,
  FiEye,
} from "react-icons/fi";

const TEMPLATES_PER_PAGE = 4;

const Template = () => {
  const { selectedTemplateId, setSelectedTemplateId } = useTemplateStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [view, setView] = useState("grid");
  const [preview, setPreview] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("All");

  const allCategories = [
    "All",
    ...new Set(TEMPLATE_ARRAY.map((t) => t.category)),
  ];

  const toggleFavorite = (key) => {
    setFavorites((prev) =>
      prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]
    );
  };

  const filtered = useMemo(() => {
    return TEMPLATE_ARRAY.filter((t) => {
      const matchesSearch = t.name
        .toLowerCase()
        .includes(searchQuery.trim().toLowerCase());
      const matchesCategory =
        selectedCategory === "All" || t.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  const totalPages = Math.ceil(filtered.length / TEMPLATES_PER_PAGE);
  const pageItems = filtered.slice(
    (currentPage - 1) * TEMPLATES_PER_PAGE,
    currentPage * TEMPLATES_PER_PAGE
  );

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    const newArr = Array.from(filtered);
    const [moved] = newArr.splice(result.source.index, 1);
    newArr.splice(result.destination.index, 0, moved);
    setSearchQuery("");
  };

  return (
    <div className="p-6 bg-white rounded-2xl shadow-md w-full max-w-7xl mx-auto border border-gray-200 relative overflow-hidden">
      {/* Preview Modal */}
      {preview && (
        <div
          className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center"
          onClick={() => setPreview(null)}
        >
          <div
            className="relative bg-white rounded-xl shadow-lg w-[90%] max-w-4xl max-h-[90vh] overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setPreview(null)}
              className="absolute top-3 right-3 text-gray-600 hover:text-red-500 z-10"
              aria-label="Close Preview"
            >
              <FiX size={24} />
            </button>
            <div className="p-4">
              <img
                src={preview.thumb}
                alt={preview.name}
                className="w-full max-h-[70vh] object-contain rounded-md"
                loading="lazy"
              />
              <div className="text-center mt-4 text-lg font-semibold text-[#29354d]">
                {preview.name}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center mb-4 flex-wrap gap-3">
        <div>
          <h2 className="text-2xl font-semibold text-[#29354d]">
            Template Gallery
          </h2>
          <p className="text-gray-500 text-sm mt-1">
            Search, preview, drag, or favorite templates.
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setView("grid")}
            className={`p-2 border rounded-md text-sm transition ${
              view === "grid"
                ? "bg-[#29354d] text-white"
                : "hover:bg-gray-100 text-gray-700"
            }`}
            title="Grid View"
          >
            <FiGrid />
          </button>
          <button
            onClick={() => setView("list")}
            className={`p-2 border rounded-md text-sm transition ${
              view === "list"
                ? "bg-[#29354d] text-white"
                : "hover:bg-gray-100 text-gray-700"
            }`}
            title="List View"
          >
            <FiList />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row md:items-center gap-3 mb-5">
        <input
          type="text"
          placeholder="Search Templates..."
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setCurrentPage(1);
          }}
          className="w-full md:w-auto flex-1 min-w-[200px] rounded-md border border-gray-300 p-2 px-3 text-sm focus:outline-none focus:border-[#fcc250] focus:ring-1 focus:ring-[#fcc250]"
        />
        <select
          value={selectedCategory}
          onChange={(e) => {
            setSelectedCategory(e.target.value);
            setCurrentPage(1);
          }}
          className="w-full md:w-auto min-w-[150px] rounded-md border border-gray-300 p-2 text-sm focus:outline-none focus:border-[#fcc250] focus:ring-1 focus:ring-[#fcc250]"
        >
          {allCategories.map((cat) => (
            <option key={cat} value={cat}>
              {cat}
            </option>
          ))}
        </select>
      </div>

      {/* Templates */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="templates">
          {(provided) => (
            <div
              className={`mt-5 ${
                view === "grid"
                  ? "grid grid-cols-1 sm:grid-cols-2 gap-5"
                  : "flex flex-col gap-4"
              }`}
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {pageItems.length > 0 ? (
                pageItems.map((template, idx) => {
                  const globalIndex =
                    (currentPage - 1) * TEMPLATES_PER_PAGE + idx;
                  const isSelected = template.key === selectedTemplateId;
                  const isFavorite = favorites.includes(template.key);

                  return (
                    <Draggable
                      key={template.key}
                      draggableId={template.key}
                      index={globalIndex}
                    >
                      {(draggableProvided, snapshot) => (
                        <div
                          ref={draggableProvided.innerRef}
                          {...draggableProvided.draggableProps}
                          {...draggableProvided.dragHandleProps}
                          className={`relative cursor-pointer rounded-lg overflow-hidden border p-1 transition-all duration-200 ${
                            isSelected
                              ? "border-[#29354d] ring-2 ring-[#fcc250]"
                              : "border-gray-200 hover:border-[#29354d] hover:shadow-md"
                          } ${snapshot.isDragging && "bg-gray-100 shadow-lg"}`}
                        >
                          {/* Top Buttons */}
                          <div className="absolute top-2 right-2 flex gap-2 z-10">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setPreview(template);
                              }}
                              title="Preview"
                              className="p-1 bg-white rounded-full shadow hover:bg-gray-100"
                            >
                              <FiEye size={16} className="text-[#29354d]" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleFavorite(template.key);
                              }}
                              title={isFavorite ? "Unfavorite" : "Favorite"}
                              className="p-1 bg-white rounded-full shadow hover:bg-gray-100"
                            >
                              <FiBookmark
                                size={16}
                                className={
                                  isFavorite
                                    ? "text-[#fcc250]"
                                    : "text-gray-500"
                                }
                              />
                            </button>
                          </div>

                          {/* Image */}
                          <img
                            src={template.thumb}
                            alt={template.name}
                            loading="lazy"
                            onClick={() => setSelectedTemplateId(template.key)}
                            className="w-full h-auto rounded"
                          />

                          {/* Name */}
                          <div className="p-2 text-center text-sm font-medium text-gray-700">
                            {template.name}
                          </div>
                        </div>
                      )}
                    </Draggable>
                  );
                })
              ) : (
                <div className="col-span-2 text-gray-500 text-center p-4">
                  No templates found.
                </div>
              )}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 space-x-4">
          <button
            className={`p-2 rounded-full border text-gray-600 transition hover:text-white hover:bg-[#29354d] hover:border-[#29354d] ${
              currentPage === 1 ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={currentPage === 1}
            onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
            title="Previous"
          >
            <FiChevronLeft className="text-xl" />
          </button>
          <span className="text-sm text-gray-700 font-medium">
            Page {currentPage} of {totalPages}
          </span>
          <button
            className={`p-2 rounded-full border text-gray-600 transition hover:text-white hover:bg-[#29354d] hover:border-[#29354d] ${
              currentPage === totalPages ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage((p) => Math.min(p + 1, totalPages))}
            title="Next"
          >
            <FiChevronRight className="text-xl" />
          </button>
        </div>
      )}
    </div>
  );
};

export default Template;
