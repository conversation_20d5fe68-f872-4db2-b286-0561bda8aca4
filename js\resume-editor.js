document.addEventListener('DOMContentLoaded', function() {
  // Initialize with existing resume data if available
  let resumeData = null;
  const resumeDataInput = document.getElementById('resume_data');
  
  if (resumeDataInput.value) {
      try {
          resumeData = JSON.parse(resumeDataInput.value);
          initResumeEditor(resumeData);
      } catch (e) {
          console.error('Error parsing resume data:', e);
          initResumeEditor();
      }
  } else {
      initResumeEditor();
  }
});

function initResumeEditor(resumeData = null) {
  // DOM elements
  const resumeForm = document.getElementById('resume-form');
  const resumeDataInput = document.getElementById('resume_data');
  const previewBtn = document.getElementById('preview-btn');
  const downloadBtn = document.getElementById('download-pdf');
  const resumeTemplate = document.getElementById('resume-template');
  const resumeSections = document.getElementById('resume-sections');
  const availableSections = document.getElementById('available-sections');
  
  // Section templates
  const summaryTemplate = document.getElementById('summary-section-template');
  const experienceTemplate = document.getElementById('experience-section-template');
  const educationTemplate = document.getElementById('education-section-template');
  const skillsTemplate = document.getElementById('skills-section-template');
  const experienceItemTemplate = document.getElementById('experience-item-template');
  const educationItemTemplate = document.getElementById('education-item-template');
  const skillTagTemplate = document.getElementById('skill-tag-template');
  
  // Style controls
  const templateSelector = document.getElementById('template-selector');
  const fontFamilySelector = document.getElementById('font-family-selector');
  const colorOptions = document.querySelectorAll('.color-option');
  const customColorInput = document.getElementById('custom-color');
  const decreaseFontBtn = document.getElementById('decrease-font');
  const increaseFontBtn = document.getElementById('increase-font');
  const currentFontSizeEl = document.getElementById('current-font-size');
  
  // Font size options
  const fontSizes = ['Small', 'Medium', 'Large', 'X-Large'];
  let currentFontSizeIndex = 1; // Default to Medium
  
  // Preview toggle
  const resumePreview = document.querySelector('.resume-preview');
  const closePreviewBtn = document.getElementById('close-preview');
  
  // Initialize sections based on template
  initializeSections();
  
  // Make sections sortable
  initSortable();
  
  // Initialize event listeners
  initEventListeners();
  
  // Initialize style controls
  initStyleControls();
  
  // Update resume preview
  updateResume();
  
  // Initialize with resume data if editing
  if (resumeData) {
      loadResumeData(resumeData);
  }
  
  // Functions
  
  function initializeSections() {
      // Add default sections if not editing an existing resume
      if (!resumeData) {
          addSection('summary');
          addSection('experience');
          addSection('education');
          addSection('skills');
          
          // Add default experience items
          const experienceSection = document.querySelector('[data-section-type="experience"]');
          if (experienceSection) {
              const experienceItems = experienceSection.querySelector('#experience-items');
              addExperienceItem(experienceItems);
              addExperienceItem(experienceItems);
          }
          
          // Add default education items
          const educationSection = document.querySelector('[data-section-type="education"]');
          if (educationSection) {
              const educationItems = educationSection.querySelector('#education-items');
              addEducationItem(educationItems);
              addEducationItem(educationItems);
          }
          
          // Add default skills
          const skillsSection = document.querySelector('[data-section-type="skills"]');
          if (skillsSection) {
              const skillsContainer = skillsSection.querySelector('#skills-container');
              addSkill('JavaScript', skillsContainer);
              addSkill('React.js', skillsContainer);
              addSkill('Node.js', skillsContainer);
              addSkill('Python', skillsContainer);
              addSkill('SQL', skillsContainer);
              addSkill('Git & GitHub', skillsContainer);
              addSkill('REST APIs', skillsContainer);
              addSkill('CI/CD', skillsContainer);
          }
      }
  }
  
  function initSortable() {
      // Make resume sections sortable
      new Sortable(resumeSections, {
          handle: '.drag-handle',
          animation: 150,
          onEnd: function() {
              updateResume();
          }
      });
      
      // Make experience items sortable
      const experienceItems = document.querySelectorAll('#experience-items');
      experienceItems.forEach(container => {
          new Sortable(container, {
              animation: 150,
              onEnd: function() {
                  updateResume();
              }
          });
      });
      
      // Make education items sortable
      const educationItems = document.querySelectorAll('#education-items');
      educationItems.forEach(container => {
          new Sortable(container, {
              animation: 150,
              onEnd: function() {
                  updateResume();
              }
          });
      });
  }
  
  function initEventListeners() {
      // Section toggles
      document.addEventListener('click', function(e) {
          if (e.target.classList.contains('section-toggle') || e.target.closest('.section-toggle')) {
              const toggle = e.target.classList.contains('section-toggle') ? e.target : e.target.closest('.section-toggle');
              const section = toggle.closest('.editor-section');
              const content = section.querySelector('.section-content');
              const icon = toggle.querySelector('i');
              
              if (content.style.display === 'none') {
                  content.style.display = 'block';
                  icon.classList.remove('fa-chevron-down');
                  icon.classList.add('fa-chevron-up');
              } else {
                  content.style.display = 'none';
                  icon.classList.remove('fa-chevron-up');
                  icon.classList.add('fa-chevron-down');
              }
          }
      });
      
      // Section removal
      document.addEventListener('click', function(e) {
          if (e.target.classList.contains('section-remove') || e.target.closest('.section-remove')) {
              const button = e.target.classList.contains('section-remove') ? e.target : e.target.closest('.section-remove');
              const section = button.closest('.editor-section');
              
              if (confirm('Are you sure you want to remove this section?')) {
                  section.remove();
                  updateResume();
              }
          }
      });
      
      // Add section from sidebar
      availableSections.addEventListener('click', function(e) {
          const sectionItem = e.target.closest('.section-item');
          if (sectionItem) {
              const sectionType = sectionItem.dataset.sectionType;
              
              // Check if section already exists
              const existingSection = document.querySelector(`[data-section-type="${sectionType}"]`);
              if (existingSection && sectionType !== 'custom') {
                  alert('This section already exists in your resume.');
                  return;
              }
              
              addSection(sectionType);
              updateResume();
          }
      });
      
      // Experience section events
      document.addEventListener('click', function(e) {
          // Add experience
          if (e.target.id === 'add-experience' || e.target.closest('#add-experience')) {
              const experienceSection = e.target.closest('.editor-section');
              const experienceItems = experienceSection.querySelector('#experience-items');
              addExperienceItem(experienceItems);
              updateResume();
          }
          
          // Edit experience
          if (e.target.classList.contains('item-edit') || e.target.closest('.item-edit')) {
              const button = e.target.classList.contains('item-edit') ? e.target : e.target.closest('.item-edit');
              const item = button.closest('.experience-item, .education-item');
              const form = item.querySelector('.item-form');
              form.style.display = 'block';
          }
          
          // Remove experience
          if (e.target.classList.contains('item-remove') || e.target.closest('.item-remove')) {
              const button = e.target.classList.contains('item-remove') ? e.target : e.target.closest('.item-remove');
              const item = button.closest('.experience-item, .education-item');
              
              if (confirm('Are you sure you want to remove this item?')) {
                  item.remove();
                  updateResume();
              }
          }
          
          // Save experience
          if (e.target.classList.contains('item-save') || e.target.closest('.item-save')) {
              const button = e.target.classList.contains('item-save') ? e.target : e.target.closest('.item-save');
              const item = button.closest('.experience-item, .education-item');
              const form = item.querySelector('.item-form');
              const header = item.querySelector('.item-header h4');
              
              if (item.classList.contains('experience-item')) {
                  const jobTitle = item.querySelector('.exp-job-title').value;
                  const company = item.querySelector('.exp-company').value;
                  header.textContent = `${jobTitle} at ${company}`;
              } else if (item.classList.contains('education-item')) {
                  const degree = item.querySelector('.edu-degree').value;
                  const institution = item.querySelector('.edu-institution').value;
                  header.textContent = `${degree} - ${institution}`;
              }
              
              form.style.display = 'none';
              updateResume();
          }
          
          // Cancel experience edit
          if (e.target.classList.contains('item-cancel') || e.target.closest('.item-cancel')) {
              const button = e.target.classList.contains('item-cancel') ? e.target : e.target.closest('.item-cancel');
              const item = button.closest('.experience-item, .education-item');
              const form = item.querySelector('.item-form');
              form.style.display = 'none';
          }
          
          // Add bullet point
          if (e.target.classList.contains('add-bullet') || e.target.closest('.add-bullet')) {
              const button = e.target.classList.contains('add-bullet') ? e.target : e.target.closest('.add-bullet');
              const container = button.previousElementSibling;
              
              const bulletPoint = document.createElement('div');
              bulletPoint.className = 'bullet-point';
              bulletPoint.innerHTML = `
                  <input type="text" placeholder="Enter a bullet point">
                  <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
              `;
              
              container.appendChild(bulletPoint);
          }
          
          // Remove bullet point
          if (e.target.classList.contains('remove-bullet') || e.target.closest('.remove-bullet')) {
              const button = e.target.classList.contains('remove-bullet') ? e.target : e.target.closest('.remove-bullet');
              const bulletPoint = button.closest('.bullet-point');
              bulletPoint.remove();
              updateResume();
          }
      });
      
      // Education section events
      document.addEventListener('click', function(e) {
          // Add education
          if (e.target.id === 'add-education' || e.target.closest('#add-education')) {
              const educationSection = e.target.closest('.editor-section');
              const educationItems = educationSection.querySelector('#education-items');
              addEducationItem(educationItems);
              updateResume();
          }
      });
      
      // Skills section events
      document.addEventListener('click', function(e) {
          // Add skill
          if (e.target.id === 'add-skill-btn' || e.target.closest('#add-skill-btn')) {
              const skillsSection = e.target.closest('.editor-section');
              const skillInput = skillsSection.querySelector('#add-skill-input');
              const skillsContainer = skillsSection.querySelector('#skills-container');
              
              const skillName = skillInput.value.trim();
              if (skillName) {
                  addSkill(skillName, skillsContainer);
                  skillInput.value = '';
                  updateResume();
              }
          }
          
          // Remove skill
          if (e.target.classList.contains('skill-remove') || e.target.closest('.skill-remove')) {
              const button = e.target.classList.contains('skill-remove') ? e.target : e.target.closest('.skill-remove');
              const skillTag = button.closest('.skill-tag');
              skillTag.remove();
              updateResume();
          }
      });
      
      // Add skill on Enter key
      document.addEventListener('keypress', function(e) {
          if (e.key === 'Enter' && e.target.id === 'add-skill-input') {
              e.preventDefault();
              const skillsSection = e.target.closest('.editor-section');
              const addSkillBtn = skillsSection.querySelector('#add-skill-btn');
              addSkillBtn.click();
          }
      });
      
      // Personal information fields
      const personalFields = document.querySelectorAll('[data-field]');
      personalFields.forEach(field => {
          field.addEventListener('input', updateResume);
      });
      
      // Preview button
      previewBtn.addEventListener('click', function() {
          resumePreview.classList.toggle('active');
          
          // Scroll to preview on mobile
          if (window.innerWidth < 992 && resumePreview.classList.contains('active')) {
              resumePreview.scrollIntoView({ behavior: 'smooth' });
          }
      });
      
      // Close preview button
      closePreviewBtn.addEventListener('click', function() {
          resumePreview.classList.remove('active');
      });
      
      // Download PDF
      downloadBtn.addEventListener('click', function() {
          const element = document.getElementById('resume-template');
          const options = {
              margin: 10,
              filename: document.getElementById('resume_name').value + '.pdf',
              image: { type: 'jpeg', quality: 0.98 },
              html2canvas: { scale: 2 },
              jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
          };
          
          // Temporarily add a class for PDF generation
          element.classList.add('generating-pdf');
          
          // Generate PDF
          html2pdf()
              .set(options)
              .from(element)
              .save()
              .then(() => {
                  element.classList.remove('generating-pdf');
              });
      });
      
      // Form submission
      resumeForm.addEventListener('submit', function(e) {
          // Gather all resume data
          const resumeData = collectResumeData();
          
          // Set resume data input value
          resumeDataInput.value = JSON.stringify(resumeData);
      });
  }
  
  function initStyleControls() {
      // Template selector
      templateSelector.addEventListener('change', function() {
          const template = this.value;
          resumeTemplate.className = template + '-resume';
          updateResume();
      });
      
      // Font family selector
      fontFamilySelector.addEventListener('change', function() {
          const fontFamily = this.value;
          resumeTemplate.style.fontFamily = fontFamily;
          updateResume();
      });
      
      // Color options
      colorOptions.forEach(option => {
          option.addEventListener('click', function() {
              const color = this.dataset.color;
              
              if (color === 'custom') {
                  // Custom color selected, use the color input value
                  setPrimaryColor(customColorInput.value);
              } else {
                  setPrimaryColor(color);
              }
              
              // Update active state
              colorOptions.forEach(opt => opt.classList.remove('active'));
              this.classList.add('active');
              
              updateResume();
          });
      });
      
      // Custom color input
      customColorInput.addEventListener('input', function() {
          const customOption = document.querySelector('.color-option.custom');
          
          // Set the color
          setPrimaryColor(this.value);
          
          // Update active state
          colorOptions.forEach(opt => opt.classList.remove('active'));
          customOption.classList.add('active');
          
          updateResume();
      });
      
      // Font size controls
      decreaseFontBtn.addEventListener('click', function() {
          if (currentFontSizeIndex > 0) {
              currentFontSizeIndex--;
              updateFontSize();
          }
      });
      
      increaseFontBtn.addEventListener('click', function() {
          if (currentFontSizeIndex < fontSizes.length - 1) {
              currentFontSizeIndex++;
              updateFontSize();
          }
      });
  }
  
  function updateFontSize() {
      const size = fontSizes[currentFontSizeIndex];
      currentFontSizeEl.textContent = size;
      
      // Update font size in the resume template
      switch (size) {
          case 'Small':
              resumeTemplate.style.fontSize = '0.9rem';
              break;
          case 'Medium':
              resumeTemplate.style.fontSize = '1rem';
              break;
          case 'Large':
              resumeTemplate.style.fontSize = '1.1rem';
              break;
          case 'X-Large':
              resumeTemplate.style.fontSize = '1.2rem';
              break;
      }
      
      updateResume();
  }
  
  function setPrimaryColor(color) {
      document.documentElement.style.setProperty('--primary-color', color);
      
      // Also update secondary color (slightly lighter)
      const secondaryColor = lightenColor(color, 20);
      document.documentElement.style.setProperty('--secondary-color', secondaryColor);
  }
  
  function lightenColor(hex, percent) {
      // Convert hex to RGB
      let r = parseInt(hex.slice(1, 3), 16);
      let g = parseInt(hex.slice(3, 5), 16);
      let b = parseInt(hex.slice(5, 7), 16);
      
      // Lighten
      r = Math.min(255, Math.floor(r + (255 - r) * (percent / 100)));
      g = Math.min(255, Math.floor(g + (255 - g) * (percent / 100)));
      b = Math.min(255, Math.floor(b + (255 - b) * (percent / 100)));
      
      // Convert back to hex
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }
  
  function addSection(sectionType) {
      let newSection;
      
      switch (sectionType) {
          case 'summary':
              newSection = summaryTemplate.content.cloneNode(true);
              break;
          case 'experience':
              newSection = experienceTemplate.content.cloneNode(true);
              break;
          case 'education':
              newSection = educationTemplate.content.cloneNode(true);
              break;
          case 'skills':
              newSection = skillsTemplate.content.cloneNode(true);
              break;
          case 'custom':
              // Create a custom section
              const customName = prompt('Enter a name for your custom section:');
              if (!customName) return;
              
              newSection = document.createElement('div');
              newSection.className = 'editor-section';
              newSection.dataset.sectionType = 'custom';
              newSection.innerHTML = `
                  <div class="section-header">
                      <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                      <h3><i class="fas fa-star"></i> ${customName}</h3>
                      <div class="section-controls">
                          <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                          <button type="button" class="section-remove"><i class="fas fa-trash"></i></button>
                      </div>
                  </div>
                  <div class="section-content">
                      <div class="form-group">
                          <label>Content</label>
                          <textarea data-field="custom-content" rows="4" placeholder="Enter content for this section"></textarea>
                      </div>
                  </div>
              `;
              break;
          default:
              return;
      }
      
      // Add the new section to the resume
      resumeSections.appendChild(newSection);
      
      // Initialize sortable for new containers if needed
      if (sectionType === 'experience') {
          const experienceItems = newSection.querySelector('#experience-items');
          new Sortable(experienceItems, {
              animation: 150,
              onEnd: function() {
                  updateResume();
              }
          });
      } else if (sectionType === 'education') {
          const educationItems = newSection.querySelector('#education-items');
          new Sortable(educationItems, {
              animation: 150,
              onEnd: function() {
                  updateResume();
              }
          });
      }
  }
  
  function addExperienceItem(container) {
      const newItem = experienceItemTemplate.content.cloneNode(true);
      container.appendChild(newItem);
      
      // Add event listeners to the new item
      const item = container.lastElementChild;
      
      const editBtn = item.querySelector('.item-edit');
      const removeBtn = item.querySelector('.item-remove');
      const cancelBtn = item.querySelector('.item-cancel');
      const saveBtn = item.querySelector('.item-save');
      const form = item.querySelector('.item-form');
      
      editBtn.addEventListener('click', () => {
          form.style.display = 'block';
      });
      
      removeBtn.addEventListener('click', () => {
          if (confirm('Are you sure you want to remove this item?')) {
              item.remove();
              updateResume();
          }
      });
      
      cancelBtn.addEventListener('click', () => {
          form.style.display = 'none';
      });
      
      saveBtn.addEventListener('click', () => {
          const jobTitle = item.querySelector('.exp-job-title').value;
          const company = item.querySelector('.exp-company').value;
          item.querySelector('.item-header h4').textContent = jobTitle + ' at ' + company;
          form.style.display = 'none';
          updateResume();
      });
  }
  
  function addEducationItem(container) {
      const newItem = educationItemTemplate.content.cloneNode(true);
      container.appendChild(newItem);
      
      // Add event listeners to the new item
      const item = container.lastElementChild;
      
      const editBtn = item.querySelector('.item-edit');
      const removeBtn = item.querySelector('.item-remove');
      const cancelBtn = item.querySelector('.item-cancel');
      const saveBtn = item.querySelector('.item-save');
      const form = item.querySelector('.item-form');
      
      editBtn.addEventListener('click', () => {
          form.style.display = 'block';
      });
      
      removeBtn.addEventListener('click', () => {
          if (confirm('Are you sure you want to remove this item?')) {
              item.remove();
              updateResume();
          }
      });
      
      cancelBtn.addEventListener('click', () => {
          form.style.display = 'none';
      });
      
      saveBtn.addEventListener('click', () => {
          const degree = item.querySelector('.edu-degree').value;
          const institution = item.querySelector('.edu-institution').value;
          item.querySelector('.item-header h4').textContent = degree + ' - ' + institution;
          form.style.display = 'none';
          updateResume();
      });
  }
  
  function addSkill(skillName, container) {
    const skillTag = skillTagTemplate.content.cloneNode(true);
    const skillNameElement = skillTag.querySelector('.skill-name');
    skillNameElement.textContent = skillName;
    
    container.appendChild(skillTag);
    
    // Add event listener to remove button
    const removeBtn = container.lastElementChild.querySelector('.skill-remove');
    removeBtn.addEventListener('click', function() {
        this.closest('.skill-tag').remove();
        updateResume();
    });
}

function updateResume() {
    // Get all fields and update the resume template
    const fullName = document.getElementById('full_name').value;
    const jobTitle = document.getElementById('job_title').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    const location = document.getElementById('location').value;
    const website = document.getElementById('website').value;
    
    // Update header section
    const template = document.getElementById('resume-template');
    template.innerHTML = `
        <div class="resume-header">
            <div class="name">${fullName}</div>
            <div class="title">${jobTitle}</div>
            <div class="contact-info">
                <div class="contact-item">${email}</div>
                <div class="contact-item">${phone}</div>
                <div class="contact-item">${location}</div>
                <div class="contact-item">${website}</div>
            </div>
        </div>
    `;
    
    // Get all visible sections and add them to the template
    const sections = document.querySelectorAll('.editor-section:not([data-section-id="header"])');
    
    sections.forEach(section => {
        const sectionType = section.dataset.sectionType;
        let sectionHTML = '';
        
        switch (sectionType) {
            case 'summary':
                const summary = section.querySelector('[data-field="summary"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>Professional Summary</h2>
                        <div class="description">${summary}</div>
                    </div>
                `;
                break;
                
            case 'experience':
                const experienceItems = section.querySelectorAll('.experience-item');
                if (experienceItems.length > 0) {
                    sectionHTML = `
                        <div class="section">
                            <h2>Experience</h2>
                    `;
                    
                    experienceItems.forEach(item => {
                        const jobTitle = item.querySelector('.exp-job-title').value;
                        const company = item.querySelector('.exp-company').value;
                        const startDate = item.querySelector('.exp-start-date').value;
                        const endDate = item.querySelector('.exp-end-date').value;
                        const description = item.querySelector('.exp-description').value;
                        
                        const bulletPoints = [];
                        item.querySelectorAll('.bullet-point input').forEach(bullet => {
                            if (bullet.value.trim()) {
                                bulletPoints.push(`<li>${bullet.value}</li>`);
                            }
                        });
                        
                        sectionHTML += `
                            <div class="section-item">
                                <div class="item-header">
                                    <div class="item-title">${jobTitle}</div>
                                    <div class="date">${startDate} - ${endDate}</div>
                                </div>
                                <div class="item-subtitle">${company}</div>
                                <div class="description">
                                    ${description ? `<p>${description}</p>` : ''}
                                    ${bulletPoints.length > 0 ? `<ul>${bulletPoints.join('')}</ul>` : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    sectionHTML += `</div>`;
                }
                break;
                
            case 'education':
                const educationItems = section.querySelectorAll('.education-item');
                if (educationItems.length > 0) {
                    sectionHTML = `
                        <div class="section">
                            <h2>Education</h2>
                    `;
                    
                    educationItems.forEach(item => {
                        const degree = item.querySelector('.edu-degree').value;
                        const institution = item.querySelector('.edu-institution').value;
                        const startDate = item.querySelector('.edu-start-date').value;
                        const endDate = item.querySelector('.edu-end-date').value;
                        const description = item.querySelector('.edu-description').value;
                        
                        sectionHTML += `
                            <div class="section-item">
                                <div class="item-header">
                                    <div class="item-title">${degree}</div>
                                    <div class="date">${startDate} - ${endDate}</div>
                                </div>
                                <div class="item-subtitle">${institution}</div>
                                <div class="description">${description}</div>
                            </div>
                        `;
                    });
                    
                    sectionHTML += `</div>`;
                }
                break;
                
            case 'skills':
                const skillTags = section.querySelectorAll('.skill-tag');
                if (skillTags.length > 0) {
                    sectionHTML = `
                        <div class="section">
                            <h2>Skills</h2>
                            <div class="skills-list">
                    `;
                    
                    skillTags.forEach(tag => {
                        const skillName = tag.querySelector('.skill-name').textContent;
                        sectionHTML += `<div class="skill-item">${skillName}</div>`;
                    });
                    
                    sectionHTML += `</div></div>`;
                }
                break;
                
            case 'projects':
                const projectContent = section.querySelector('[data-field="custom-content"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>Projects</h2>
                        <div class="description">${projectContent}</div>
                    </div>
                `;
                break;
                
            case 'certifications':
                const certContent = section.querySelector('[data-field="custom-content"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>Certifications</h2>
                        <div class="description">${certContent}</div>
                    </div>
                `;
                break;
                
            case 'languages':
                const langContent = section.querySelector('[data-field="custom-content"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>Languages</h2>
                        <div class="description">${langContent}</div>
                    </div>
                `;
                break;
                
            case 'interests':
                const interestsContent = section.querySelector('[data-field="custom-content"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>Interests</h2>
                        <div class="description">${interestsContent}</div>
                    </div>
                `;
                break;
                
            case 'custom':
                const customTitle = section.querySelector('.section-header h3').textContent.replace('<i class="fas fa-star"></i> ', '');
                const customContent = section.querySelector('[data-field="custom-content"]').value;
                sectionHTML = `
                    <div class="section">
                        <h2>${customTitle}</h2>
                        <div class="description">${customContent}</div>
                    </div>
                `;
                break;
        }
        
        template.innerHTML += sectionHTML;
    });
}

function collectResumeData() {
    const data = {
        personal: {
            full_name: document.getElementById('full_name').value,
            job_title: document.getElementById('job_title').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            location: document.getElementById('location').value,
            website: document.getElementById('website').value
        },
        style: {
            template: document.getElementById('template-selector').value,
            color: document.documentElement.style.getPropertyValue('--primary-color'),
            font_family: document.getElementById('font-family-selector').value,
            font_size: fontSizes[currentFontSizeIndex]
        },
        sections: []
    };
    
    // Collect data from all sections
    const sections = document.querySelectorAll('.editor-section:not([data-section-id="header"])');
    
    sections.forEach(section => {
        const sectionType = section.dataset.sectionType;
        let sectionData = {
            type: sectionType
        };
        
        switch (sectionType) {
            case 'summary':
                sectionData.content = section.querySelector('[data-field="summary"]').value;
                break;
                
            case 'experience':
                sectionData.items = [];
                const experienceItems = section.querySelectorAll('.experience-item');
                
                experienceItems.forEach(item => {
                    const bulletPoints = [];
                    item.querySelectorAll('.bullet-point input').forEach(bullet => {
                        if (bullet.value.trim()) {
                            bulletPoints.push(bullet.value);
                        }
                    });
                    
                    sectionData.items.push({
                        job_title: item.querySelector('.exp-job-title').value,
                        company: item.querySelector('.exp-company').value,
                        start_date: item.querySelector('.exp-start-date').value,
                        end_date: item.querySelector('.exp-end-date').value,
                        description: item.querySelector('.exp-description').value,
                        bullet_points: bulletPoints
                    });
                });
                break;
                
            case 'education':
                sectionData.items = [];
                const educationItems = section.querySelectorAll('.education-item');
                
                educationItems.forEach(item => {
                    sectionData.items.push({
                        degree: item.querySelector('.edu-degree').value,
                        institution: item.querySelector('.edu-institution').value,
                        start_date: item.querySelector('.edu-start-date').value,
                        end_date: item.querySelector('.edu-end-date').value,
                        description: item.querySelector('.edu-description').value
                    });
                });
                break;
                
            case 'skills':
                sectionData.skills = [];
                const skillTags = section.querySelectorAll('.skill-tag');
                
                skillTags.forEach(tag => {
                    sectionData.skills.push(tag.querySelector('.skill-name').textContent);
                });
                break;
                
            case 'custom':
            case 'projects':
            case 'certifications':
            case 'languages':
            case 'interests':
                sectionData.title = section.querySelector('.section-header h3').textContent.replace('<i class="fas fa-star"></i> ', '');
                sectionData.content = section.querySelector('[data-field="custom-content"]').value;
                break;
        }
        
        data.sections.push(sectionData);
    });
    
    return data;
}

function loadResumeData(data) {
    // Load personal information
    if (data.personal) {
        document.getElementById('full_name').value = data.personal.full_name || '';
        document.getElementById('job_title').value = data.personal.job_title || '';
        document.getElementById('email').value = data.personal.email || '';
        document.getElementById('phone').value = data.personal.phone || '';
        document.getElementById('location').value = data.personal.location || '';
        document.getElementById('website').value = data.personal.website || '';
    }
    
    // Load style settings
    if (data.style) {
        // Set template
        if (data.style.template) {
            document.getElementById('template-selector').value = data.style.template;
            resumeTemplate.className = data.style.template + '-resume';
        }
        
        // Set color
        if (data.style.color) {
            setPrimaryColor(data.style.color);
            
            // Update color option selection
            let colorFound = false;
            colorOptions.forEach(option => {
                if (option.dataset.color === data.style.color) {
                    colorOptions.forEach(opt => opt.classList.remove('active'));
                    option.classList.add('active');
                    colorFound = true;
                }
            });
            
            if (!colorFound) {
                // Must be a custom color
                customColorInput.value = data.style.color;
                colorOptions.forEach(opt => opt.classList.remove('active'));
                document.querySelector('.color-option.custom').classList.add('active');
            }
        }
        
        // Set font family
        if (data.style.font_family) {
            document.getElementById('font-family-selector').value = data.style.font_family;
            resumeTemplate.style.fontFamily = data.style.font_family;
        }
        
        // Set font size
        if (data.style.font_size) {
            const sizeIndex = fontSizes.indexOf(data.style.font_size);
            if (sizeIndex !== -1) {
                currentFontSizeIndex = sizeIndex;
                updateFontSize();
            }
        }
    }
    
    // Clear existing sections except header
    const existingSections = document.querySelectorAll('.editor-section:not([data-section-id="header"])');
    existingSections.forEach(section => section.remove());
    
    // Load sections
    if (data.sections && Array.isArray(data.sections)) {
        data.sections.forEach(sectionData => {
            addSection(sectionData.type);
            
            // Get the newly added section
            const section = document.querySelector(`.editor-section[data-section-type="${sectionData.type}"]:last-child`);
            
            if (section) {
                switch (sectionData.type) {
                    case 'summary':
                        if (sectionData.content) {
                            section.querySelector('[data-field="summary"]').value = sectionData.content;
                        }
                        break;
                        
                    case 'experience':
                        if (sectionData.items && Array.isArray(sectionData.items)) {
                            const experienceItems = section.querySelector('#experience-items');
                            
                            // Clear default items
                            experienceItems.innerHTML = '';
                            
                            sectionData.items.forEach(item => {
                                const newItem = experienceItemTemplate.content.cloneNode(true);
                                experienceItems.appendChild(newItem);
                                
                                const itemElement = experienceItems.lastElementChild;
                                
                                // Set values
                                itemElement.querySelector('.exp-job-title').value = item.job_title || '';
                                itemElement.querySelector('.exp-company').value = item.company || '';
                                itemElement.querySelector('.exp-start-date').value = item.start_date || '';
                                itemElement.querySelector('.exp-end-date').value = item.end_date || '';
                                itemElement.querySelector('.exp-description').value = item.description || '';
                                
                                // Update header
                                itemElement.querySelector('.item-header h4').textContent = 
                                    (item.job_title || 'Job Title') + ' at ' + (item.company || 'Company');
                                
                                // Clear default bullet points
                                const bulletContainer = itemElement.querySelector('.bullet-points-container');
                                bulletContainer.innerHTML = '';
                                
                                // Add bullet points
                                if (item.bullet_points && Array.isArray(item.bullet_points)) {
                                    item.bullet_points.forEach(bulletText => {
                                        const bulletPoint = document.createElement('div');
                                        bulletPoint.className = 'bullet-point';
                                        bulletPoint.innerHTML = `
                                            <input type="text" value="${bulletText}">
                                            <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
                                        `;
                                        bulletContainer.appendChild(bulletPoint);
                                        
                                        // Add event listener to remove button
                                        bulletPoint.querySelector('.remove-bullet').addEventListener('click', function() {
                                            this.closest('.bullet-point').remove();
                                            updateResume();
                                        });
                                    });
                                }
                                
                                // Add event listeners
                                const editBtn = itemElement.querySelector('.item-edit');
                                const removeBtn = itemElement.querySelector('.item-remove');
                                const cancelBtn = itemElement.querySelector('.item-cancel');
                                const saveBtn = itemElement.querySelector('.item-save');
                                const form = itemElement.querySelector('.item-form');
                                
                                editBtn.addEventListener('click', () => {
                                    form.style.display = 'block';
                                });
                                
                                removeBtn.addEventListener('click', () => {
                                    if (confirm('Are you sure you want to remove this item?')) {
                                        itemElement.remove();
                                        updateResume();
                                    }
                                });
                                
                                cancelBtn.addEventListener('click', () => {
                                    form.style.display = 'none';
                                });
                                
                                saveBtn.addEventListener('click', () => {
                                    const jobTitle = itemElement.querySelector('.exp-job-title').value;
                                    const company = itemElement.querySelector('.exp-company').value;
                                    itemElement.querySelector('.item-header h4').textContent = jobTitle + ' at ' + company;
                                    form.style.display = 'none';
                                    updateResume();
                                });
                                
                                // Add bullet point button
                                itemElement.querySelector('.add-bullet').addEventListener('click', function() {
                                    const container = this.previousElementSibling;
                                    
                                    const bulletPoint = document.createElement('div');
                                    bulletPoint.className = 'bullet-point';
                                    bulletPoint.innerHTML = `
                                        <input type="text" placeholder="Enter a bullet point">
                                        <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
                                    `;
                                    
                                    container.appendChild(bulletPoint);
                                    
                                    // Add event listener to remove button
                                    bulletPoint.querySelector('.remove-bullet').addEventListener('click', function() {
                                        this.closest('.bullet-point').remove();
                                        updateResume();
                                    });
                                });
                            });
                        }
                        break;
                        
                    case 'education':
                        if (sectionData.items && Array.isArray(sectionData.items)) {
                            const educationItems = section.querySelector('#education-items');
                            
                            // Clear default items
                            educationItems.innerHTML = '';
                            
                            sectionData.items.forEach(item => {
                                const newItem = educationItemTemplate.content.cloneNode(true);
                                educationItems.appendChild(newItem);
                                
                                const itemElement = educationItems.lastElementChild;
                                
                                // Set values
                                itemElement.querySelector('.edu-degree').value = item.degree || '';
                                itemElement.querySelector('.edu-institution').value = item.institution || '';
                                itemElement.querySelector('.edu-start-date').value = item.start_date || '';
                                itemElement.querySelector('.edu-end-date').value = item.end_date || '';
                                itemElement.querySelector('.edu-description').value = item.description || '';
                                
                                // Update header
                                itemElement.querySelector('.item-header h4').textContent = 
                                    (item.degree || 'Degree') + ' - ' + (item.institution || 'Institution');
                                
                                // Add event listeners
                                const editBtn = itemElement.querySelector('.item-edit');
                                const removeBtn = itemElement.querySelector('.item-remove');
                                const cancelBtn = itemElement.querySelector('.item-cancel');
                                const saveBtn = itemElement.querySelector('.item-save');
                                const form = itemElement.querySelector('.item-form');
                                
                                editBtn.addEventListener('click', () => {
                                    form.style.display = 'block';
                                });
                                
                                removeBtn.addEventListener('click', () => {
                                    if (confirm('Are you sure you want to remove this item?')) {
                                        itemElement.remove();
                                        updateResume();
                                    }
                                });
                                
                                cancelBtn.addEventListener('click', () => {
                                    form.style.display = 'none';
                                });
                                
                                saveBtn.addEventListener('click', () => {
                                    const degree = itemElement.querySelector('.edu-degree').value;
                                    const institution = itemElement.querySelector('.edu-institution').value;
                                    itemElement.querySelector('.item-header h4').textContent = degree + ' - ' + institution;
                                    form.style.display = 'none';
                                    updateResume();
                                });
                            });
                        }
                        break;
                        
                    case 'skills':
                        if (sectionData.skills && Array.isArray(sectionData.skills)) {
                            const skillsContainer = section.querySelector('#skills-container');
                            
                            // Clear default skills
                            skillsContainer.innerHTML = '';
                            
                            sectionData.skills.forEach(skillName => {
                                addSkill(skillName, skillsContainer);
                            });
                        }
                        break;
                        
                    case 'custom':
                    case 'projects':
                    case 'certifications':
                    case 'languages':
                    case 'interests':
                        if (sectionData.title) {
                            section.querySelector('.section-header h3').innerHTML = 
                                `<i class="fas fa-star"></i> ${sectionData.title}`;
                        }
                        
                        if (sectionData.content) {
                            section.querySelector('[data-field="custom-content"]').value = sectionData.content;
                        }
                        break;
                }
            }
        });
    }
    
    // Update the preview
    updateResume();
}
}