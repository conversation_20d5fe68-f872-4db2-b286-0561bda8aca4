import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  FaUserFriends,
  FaPlus,
  FaBars,
  FaTimes,
} from "react-icons/fa";
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import DataCard from "../../Components/DataCard";

const References = () => {
  const { resumeId } = useParams();
  const { resume, fetchResume } = useResumeStore();
  const addInfo = useAddInfoStore((state) => state.addInfo);
  const deleteInfo = useDeleteInfoStore((state) => state.deleteInfo);
  const updateInfo = useUpdateInfoStore((state) => state.updateInfo);

  const references = resume?.References || [];

  const [showModal, setShowModal] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  const [formData, setFormData] = useState({
    name: "",
    position: "",
    company: "",
    relationship: "",
    phone: "",
    email: "",
    summary: "",
  });

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const handleChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleCreateOrUpdate = async () => {
    const { name, position, company, relationship, phone, email, summary } = formData;
    if (!name || !position || !company || !relationship || !phone || !email) {
      alert("Please fill all required fields.");
      return;
    }

    const payload = {
      Name: name,
      Position: position,
      Company: company,
      Email: email,
      Phone: phone,
      Summary: summary,
    };

    try {
      if (isEdit && editingId) {
        await updateInfo({
          resumeId,
          section: "references",
          entryId: editingId,
          updatedData: payload,
        });
      } else {
        await addInfo({
          resumeId,
          section: "references",
          newData: payload,
        });
      }
      await fetchResume(resumeId);
      resetForm();
    } catch (error) {
      alert("Failed to save reference.");
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      position: "",
      company: "",
      relationship: "",
      phone: "",
      email: "",
      summary: "",
    });
    setIsEdit(false);
    setEditingId(null);
    setShowModal(false);
  };

  const handleEdit = (ref) => {
    setFormData({
      name: ref.Name || "",
      position: ref.Position || "",
      company: ref.Company || "",
      phone: ref.Phone || "",
      email: ref.Email || "",
      summary: ref.Summary || "",
      relationship: "", // Not stored
    });
    setIsEdit(true);
    setEditingId(ref._id);
    setShowModal(true);
  };

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;
    try {
      await deleteInfo({
        resumeId,
        section: "references",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
    } catch (err) {
      alert("Failed to delete reference.");
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen text-black p-6 font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center gap-3">
          <div className="bg-gray-200 p-2 rounded-full">
            <FaUserFriends size={16} />
          </div>
          <h2 className="text-2xl font-bold">References</h2>
        </div>
        <FaBars size={18} />
      </div>

      {/* Add Button */}
      <button
        onClick={() => {
          setShowModal(true);
          setIsEdit(false);
          setFormData({
            name: "",
            position: "",
            company: "",
            relationship: "",
            phone: "",
            email: "",
            summary: "",
          });
        }}
        className="border border-dashed border-gray-400 rounded-md px-4 py-3 text-gray-700 text-sm cursor-pointer hover:bg-gray-100 flex items-center justify-center gap-2 w-full max-w-md"
      >
        <FaPlus size={14} />
        <span className="font-medium">Add a new item</span>
      </button>

      {/* List */}
      <div className="mt-6 space-y-4">
        {references.length === 0 ? (
          <p className="text-gray-500">No references added yet.</p>
        ) : (
          references.map((ref) => (
            <DataCard
              key={ref._id}
              title={`${ref.Name} — ${ref.Position}`}
              subtitle={`${ref.Company} • ${ref.Email} • ${ref.Phone}`}
              description={ref.Summary || "No summary provided."}
              onEdit={() => handleEdit(ref)}
              onDelete={() => setConfirmDelete(ref)}
              data={ref}
            />
          ))
        )}
      </div>

      {/* Create/Edit Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-full max-w-md relative shadow-lg text-black">
            <button
              onClick={resetForm}
              className="absolute top-3 right-4 text-black text-xl"
            >
              <FaTimes />
            </button>

            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FaPlus /> {isEdit ? "Edit Reference" : "Add Reference"}
            </h3>

            <div className="space-y-4">
              <InputField label="Name" value={formData.name} onChange={(val) => handleChange("name", val)} />
              <InputField label="Position" value={formData.position} onChange={(val) => handleChange("position", val)} />
              <InputField label="Company" value={formData.company} onChange={(val) => handleChange("company", val)} />
              <InputField label="Relationship" value={formData.relationship} onChange={(val) => handleChange("relationship", val)} />
              <InputField label="Phone" type="tel" value={formData.phone} onChange={(val) => handleChange("phone", val)} />
              <InputField label="Email" type="email" value={formData.email} onChange={(val) => handleChange("email", val)} />
              <div>
                <label className="block text-sm mb-1">Summary</label>
                <textarea
                  value={formData.summary}
                  onChange={(e) => handleChange("summary", e.target.value)}
                  className="w-full border border-gray-400 rounded px-3 py-2 resize-none"
                  rows="4"
                />
              </div>
              <div className="flex justify-end">
                <button
                  onClick={handleCreateOrUpdate}
                  className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800"
                >
                  {isEdit ? "Update" : "Create"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center px-4">
          <div className="bg-white p-6 max-w-md w-full rounded-lg text-black relative">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes />
            </button>
            <h3 className="text-lg font-bold mb-2">Delete Reference</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete the reference for{" "}
              <strong>{confirmDelete.Name}</strong> at{" "}
              <strong>{confirmDelete.Company}</strong>?
            </p>
            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Position:</strong> {confirmDelete.Position}
              </p>
              <p>
                <strong>Email:</strong> {confirmDelete.Email}
              </p>
              <p>
                <strong>Phone:</strong> {confirmDelete.Phone}
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const InputField = ({ label, value, onChange, type = "text" }) => (
  <div>
    <label className="block text-sm mb-1">{label}</label>
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full border border-gray-400 rounded px-3 py-2"
    />
  </div>
);

export default References;
