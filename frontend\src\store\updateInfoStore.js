import { create } from "zustand";
import axios from "axios";
import { useResumeStore } from "./useResumeDetailStore";

// const API_BASE = "http://localhost:5000/api/resume/updateinfo";
const API_BASE = "https://resumebuilder-m27v.onrender.com/api/resume/updateinfo";


export const useUpdateInfoStore = create((set) => ({
    loading: false,
    error: null,
    success: null,

    updateInfo: async ({ resumeId, section, entryId, updatedData }) => {
        set({ loading: true, error: null, success: null });

        try {
            await axios.patch(
                `${API_BASE}/${resumeId}/update${section.toLowerCase()}/${entryId}`,
                updatedData,
                {
                    withCredentials: true,
                }
            );
            await useResumeStore.getState().fetchResume(resumeId, true);
            set({
                loading: false,
                success: `${section} updated successfully`,
            });
            return true;
        } catch (err) {
            set({
                loading: false,
                error:
                    err?.response?.data?.error ||
                    `Failed to update ${section.toLowerCase()}`,
            });
            return false;
        }
    },
    resetStatus: () =>
        set({
            loading: false,
            error: null,
            success: null,
        }),
}));
