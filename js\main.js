// Main JavaScript file for shared functionality

document.addEventListener("DOMContentLoaded", () => {
    // Mobile menu toggle
    const menuToggle = document.querySelector(".menu-toggle")
    const navLinks = document.querySelector(".nav-links")
    const authButtons = document.querySelector(".auth-buttons")
  
    if (menuToggle) {
      menuToggle.addEventListener("click", () => {
        navLinks?.classList.toggle("active")
        authButtons?.classList.toggle("active")
        menuToggle.classList.toggle("active")
      })
    }
  
    // Check if user is logged in (from localStorage)
    const isLoggedIn = localStorage.getItem("isLoggedIn") === "true"
    const userMenu = document.getElementById("userMenu")
    const authButtonsElement = document.getElementById("authButtons")
    const userName = document.getElementById("userName")
    const welcomeName = document.getElementById("welcomeName")
    const userAvatar = document.getElementById("userAvatar")
  
    if (isLoggedIn && userMenu && authButtonsElement) {
      userMenu.classList.remove("hidden")
      authButtonsElement.classList.add("hidden")
  
      // Set user name from localStorage
      const storedName = localStorage.getItem("userName")
      if (storedName && userName) {
        userName.textContent = storedName
      }
  
      if (storedName && welcomeName) {
        welcomeName.textContent = storedName.split(" ")[0]
      }
  
      // Set user avatar if available
      const storedAvatar = localStorage.getItem("userAvatar")
      if (storedAvatar && userAvatar) {
        userAvatar.src = storedAvatar
      }
    }
  
    // Logout functionality
    const logoutBtn = document.getElementById("logoutBtn")
    const logoutBtnDropdown = document.getElementById("logoutBtnDropdown")
  
    const handleLogout = () => {
      localStorage.removeItem("isLoggedIn")
      localStorage.removeItem("userName")
      localStorage.removeItem("userEmail")
      window.location.href = "index.html"
    }
  
    if (logoutBtn) {
      logoutBtn.addEventListener("click", handleLogout)
    }
  
    if (logoutBtnDropdown) {
      logoutBtnDropdown.addEventListener("click", handleLogout)
    }
  
    // Testimonial slider functionality
    const testimonialSlider = document.querySelector(".testimonials-slider")
    const indicators = document.querySelectorAll(".testimonial-indicators .indicator")
  
    if (testimonialSlider && indicators.length > 0) {
      indicators.forEach((indicator, index) => {
        indicator.addEventListener("click", () => {
          // Update active indicator
          document.querySelector(".indicator.active").classList.remove("active")
          indicator.classList.add("active")
  
          // Scroll to the corresponding testimonial
          testimonialSlider.scrollTo({
            left: testimonialSlider.clientWidth * index,
            behavior: "smooth",
          })
        })
      })
    }
  
    // Password visibility toggle
    const passwordToggles = document.querySelectorAll(".password-toggle")
  
    if (passwordToggles) {
      passwordToggles.forEach((toggle) => {
        toggle.addEventListener("click", function () {
          const passwordField = this.previousElementSibling
          const icon = this.querySelector("i")
  
          if (passwordField.type === "password") {
            passwordField.type = "text"
            icon.classList.remove("fa-eye")
            icon.classList.add("fa-eye-slash")
          } else {
            passwordField.type = "password"
            icon.classList.remove("fa-eye-slash")
            icon.classList.add("fa-eye")
          }
        })
      })
    }
  })
  