import React, { useEffect, useState } from "react";
import { FaTimes, FaSave } from "react-icons/fa";

const ProjectModal = ({ initial, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    Title: "",
    Description: "",
    Link: "",
    Technologies: "",
    StartDate: "",
    EndDate: "",
  });

  useEffect(() => {
    if (initial) {
      setFormData({
        Title: initial.Title || "",
        Description: initial.Description || "",
        Link: initial.Link || "",
        Technologies: initial.Technologies?.join(", ") || "",
        StartDate: initial.StartDate?.substring(0, 10) || "",
        EndDate: initial.EndDate?.substring(0, 10) || "",
      });
    }
  }, [initial]);

  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.Title) {
      alert("Project title is required.");
      return;
    }

    const payload = {
      Title: formData.Title,
      Description: formData.Description,
      Link: formData.Link,
      Technologies: formData.Technologies
        ? formData.Technologies.split(",").map((tech) => tech.trim())
        : [],
      StartDate: formData.StartDate || null,
      EndDate: formData.EndDate || null,
    };

    onSave(payload, initial?._id);
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center px-4">
      <div className="relative bg-white text-black p-6 rounded-xl w-full max-w-xl shadow-lg">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-black text-xl"
        >
          <FaTimes />
        </button>

        <h2 className="text-xl font-bold mb-4">
          {initial ? "Edit Project" : "Add Project"}
        </h2>

        <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block mb-1 font-medium">Title *</label>
            <input
              type="text"
              value={formData.Title}
              onChange={(e) => handleChange("Title", e.target.value)}
              className="w-full border border-gray-300 rounded px-4 py-2"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block mb-1 font-medium">Description</label>
            <textarea
              rows={3}
              value={formData.Description}
              onChange={(e) => handleChange("Description", e.target.value)}
              className="w-full border border-gray-300 rounded px-4 py-2"
            />
          </div>

          {/* Link */}
          <div>
            <label className="block mb-1 font-medium">
              Website / GitHub Link
            </label>
            <input
              type="url"
              value={formData.Link}
              onChange={(e) => handleChange("Link", e.target.value)}
              className="w-full border border-gray-300 rounded px-4 py-2"
              placeholder="https://github.com/your-project"
            />
          </div>

          {/* Technologies */}
          <div>
            <label className="block mb-1 font-medium">Technologies</label>
            <input
              type="text"
              value={formData.Technologies}
              onChange={(e) => handleChange("Technologies", e.target.value)}
              className="w-full border border-gray-300 rounded px-4 py-2"
              placeholder="e.g. React, Node.js, MongoDB"
            />
          </div>

          {/* Dates */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block mb-1 font-medium">Start Date</label>
              <input
                type="date"
                value={formData.StartDate}
                onChange={(e) => handleChange("StartDate", e.target.value)}
                className="w-full border border-gray-300 rounded px-4 py-2"
              />
            </div>
            <div className="flex-1">
              <label className="block mb-1 font-medium">End Date</label>
              <input
                type="date"
                value={formData.EndDate}
                onChange={(e) => handleChange("EndDate", e.target.value)}
                className="w-full border border-gray-300 rounded px-4 py-2"
              />
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <button
              type="button"
              onClick={handleSubmit}
              className="bg-[#73716c] hover:bg-[#000000] text-[#f9f9f9]  px-6 py-2 rounded-lg font-semibold flex items-center gap-2"
            >
              <FaSave /> Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectModal;
