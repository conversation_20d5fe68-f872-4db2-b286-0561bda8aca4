import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";

const ClassicTemplate = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user) {
    return <p className="text-center mt-20 text-gray-500">Loading resume...</p>;
  }

  const {
    Title,
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;
  const { name } = user;

  return (
    <div className="max-w-5xl mx-auto bg-white text-black p-6 rounded-md font-sans">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        {ProfilePic && (
          <img
            src={ProfilePic}
            alt="Profile"
            className="h-20 w-20 rounded-full object-cover border"
          />
        )}
        <div>
          <h1 className="text-3xl font-bold">{name}</h1>
          {Headline && <p className="text-lg text-gray-600">{Headline}</p>}
          <p className="text-sm text-gray-500">
            ✉️{" "}
            <a href={`mailto:${Email}`} className="underline">
              {Email}
            </a>
          </p>
          {Phone && <p className="text-sm">📞 {Phone}</p>}
          {Location && <p className="text-sm">📍 {Location}</p>}
          {Website && (
            <p className="text-sm">
              🌐{" "}
              <a
                href={Website}
                className="text-blue-600 underline"
                target="_blank"
                rel="noreferrer"
              >
                {Website}
              </a>
            </p>
          )}
        </div>
      </div>

      {/* Summary */}
      {summery && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Summary</h2>
          <p>{summery}</p>
        </div>
      )}

      {/* Profiles */}
      {Profiles.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Profiles</h2>
          {Profiles.map((p) => (
            <p key={p._id}>
              <strong>{p.Network}:</strong>{" "}
              <a
                href={p.ProfileLink}
                className="text-blue-600 underline"
                target="_blank"
                rel="noreferrer"
              >
                {p.Username}
              </a>
            </p>
          ))}
        </div>
      )}

      {/* Experience */}
      {Experience.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Experience</h2>
          {Experience.map((exp) => (
            <div key={exp._id} className="mb-3">
              <h3 className="font-medium">
                {exp.Position} at {exp.Company}
              </h3>
              <p className="text-sm text-gray-600 italic">{exp.Location}</p>
              <p className="text-sm">
                {new Date(exp.StartDate).toLocaleDateString()} -{" "}
                {new Date(exp.EndDate).toLocaleDateString()}
              </p>
              {exp.Description && <p className="text-sm">{exp.Description}</p>}
              {exp.Website && (
                <a
                  href={exp.Website}
                  className="text-blue-600 text-sm underline"
                  target="_blank"
                  rel="noreferrer"
                >
                  {exp.Website}
                </a>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Projects */}
      {Projects.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Projects</h2>
          {Projects.map((proj) => (
            <div key={proj._id} className="mb-3">
              <h3 className="font-medium">{proj.Title}</h3>
              <p className="text-sm">{proj.Description}</p>
              {proj.Technologies?.length > 0 && (
                <p className="text-sm text-gray-600">
                  Technologies: {proj.Technologies.join(", ")}
                </p>
              )}
              {proj.Link && (
                <a
                  href={proj.Link}
                  className="text-blue-600 underline"
                  target="_blank"
                  rel="noreferrer"
                >
                  {proj.Link}
                </a>
              )}
              <p className="text-xs text-gray-500">
                {new Date(proj.StartDate).toLocaleDateString()} -{" "}
                {new Date(proj.EndDate).toLocaleDateString()}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* Education */}
      {Education.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Education</h2>
          {Education.map((edu) => (
            <div key={edu._id}>
              <p className="font-medium">
                {edu.Degree} at {edu.Institution}
              </p>
              <p className="text-sm text-gray-600">
                {new Date(edu.StartDate).toLocaleDateString()} -{" "}
                {new Date(edu.EndDate).toLocaleDateString()}
              </p>
              <p className="text-sm italic">{edu.Location}</p>
            </div>
          ))}
        </div>
      )}

      {/* Skills */}
      {Skills.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Skills</h2>
          {Skills.map((skill) => (
            <div key={skill._id}>
              <p className="font-medium">
                {skill.Skill} ({skill.Proficiency})
              </p>
              <p className="text-sm">{skill.Description}</p>
              {skill.Keywords?.length > 0 && (
                <p className="text-sm text-gray-600">
                  Keywords: {skill.Keywords.join(", ")}
                </p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Languages */}
      {Languages.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Languages</h2>
          {Languages.map((lang) => (
            <p key={lang._id}>
              {lang.Name} - {lang.Proficiency}{" "}
              {lang.Description && `(${lang.Description})`}
            </p>
          ))}
        </div>
      )}

      {/* Certifications */}
      {Certifications.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">
            Certifications
          </h2>
          {Certifications.map((cert) => (
            <div key={cert._id}>
              <p>
                {cert.Title} by {cert.Issuer}
              </p>
              <p className="text-sm text-gray-600">
                {new Date(cert.Date).toLocaleDateString()}
              </p>
              {cert.Description && (
                <p className="text-sm">{cert.Description}</p>
              )}
              {cert.Website && (
                <a
                  href={cert.Website}
                  className="text-blue-600 underline"
                  target="_blank"
                  rel="noreferrer"
                >
                  {cert.Website}
                </a>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Awards */}
      {Awards.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Awards</h2>
          {Awards.map((award) => (
            <p key={award._id}>
              {award.Title} by {award.Issuer} -{" "}
              {new Date(award.Date).toLocaleDateString()}
            </p>
          ))}
        </div>
      )}

      {/* Publications */}
      {Publications.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Publications</h2>
          {Publications.map((pub) => (
            <div key={pub._id}>
              <p className="font-medium">{pub.Title}</p>
              <p className="text-sm">{pub.Publisher}</p>
              <p className="text-sm text-gray-600">
                {new Date(pub.Date).toLocaleDateString()}
              </p>
              <a
                href={pub.Website}
                className="text-blue-600 underline"
                target="_blank"
                rel="noreferrer"
              >
                {pub.Website}
              </a>
            </div>
          ))}
        </div>
      )}

      {/* Volunteering */}
      {Volunteering.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Volunteering</h2>
          {Volunteering.map((vol) => (
            <div key={vol._id}>
              <p>
                {vol.Position} at {vol.Organization}
              </p>
              <p className="text-sm">{vol.Location}</p>
            </div>
          ))}
        </div>
      )}

      {/* References */}
      {References.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">References</h2>
          {References.map((ref) => (
            <div key={ref._id}>
              <p className="font-medium">{ref.Name}</p>
              <p className="text-sm">
                {ref.Position} at {ref.Company}
              </p>
              <p className="text-sm">
                {ref.Email} | {ref.Phone}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* Interests */}
      {Interests.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold border-b mb-2">Interests</h2>
          <ul className="list-disc pl-5">
            {Interests.map((i, idx) => (
              <li key={idx}>{i.Interest || i}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ClassicTemplate;
