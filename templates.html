<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resume Templates | ResumeForge</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="templates.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <header>
    <nav class="navbar">
      <div class="container">
        <div class="logo">
          <a href="index.html">
            <h1>Resume<span>Forge</span></h1>
          </a>
        </div>
        <div class="nav-links">
          <ul>
            <li><a href="index.html">Home</a></li>
            <li><a href="templates.html" class="active">Templates</a></li>
            <li><a href="index.html#features">Features</a></li>
            <li><a href="index.html#testimonials">Testimonials</a></li>
          </ul>
        </div>
        <div class="auth-buttons" id="authButtons">
          <a href="login.html" class="btn btn-outline">Log In</a>
          <a href="signup.html" class="btn btn-primary">Sign Up</a>
        </div>
        <div class="user-menu hidden" id="userMenu">
          <div class="user-avatar">
            <img src="/placeholder.svg?height=40&width=40" alt="User" id="userAvatar">
            <span id="userName">User</span>
          </div>
          <div class="dropdown-menu">
            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            <a href="profile.html"><i class="fas fa-user-circle"></i> Profile</a>
            <a href="my-resumes.html"><i class="fas fa-file-alt"></i> My Resumes</a>
            <a href="#" id="logoutBtn"><i class="fas fa-sign-out-alt"></i> Logout</a>
          </div>
        </div>
        <div class="menu-toggle">
          <i class="fas fa-bars"></i>
        </div>
      </div>
    </nav>
  </header>

  <main>
    <section class="templates-hero">
      <div class="container">
        <div class="templates-hero-content">
          <h1>Choose Your Perfect Resume Template</h1>
          <p>Browse our collection of professionally designed templates and find the perfect match for your career goals.</p>
        </div>
      </div>
    </section>

    <section class="templates-filters">
      <div class="container">
        <div class="filters-container">
          <div class="search-filter">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Search templates..." id="templateSearch">
          </div>
          <div class="category-filters">
            <button class="filter-btn active" data-filter="all">All Templates</button>
            <button class="filter-btn" data-filter="professional">Professional</button>
            <button class="filter-btn" data-filter="modern">Modern</button>
            <button class="filter-btn" data-filter="creative">Creative</button>
            <button class="filter-btn" data-filter="simple">Simple</button>
            <button class="filter-btn" data-filter="student">Student</button>
          </div>
          <div class="sort-filter">
            <label for="sortTemplates">Sort by:</label>
            <select id="sortTemplates">
              <option value="popular">Most Popular</option>
              <option value="newest">Newest</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <section class="templates-grid-section">
      <div class="container">
        <div class="templates-grid" id="templatesGrid">
          <!-- Templates will be loaded dynamically from the database -->
          <div class="templates-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading templates...</p>
          </div>
        </div>
        
        <div class="templates-pagination" id="templatesPagination">
          <!-- Pagination will be generated dynamically -->
        </div>
      </div>
    </section>

    <section class="templates-cta">
      <div class="container">
        <div class="cta-content">
          <h2>Can't find what you're looking for?</h2>
          <p>We're constantly adding new templates. Let us know what you need!</p>
          <a href="contact.html" class="btn btn-primary btn-lg">Request a Template</a>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <h2>Resume<span>Forge</span></h2>
          <p>Create professional resumes in minutes</p>
          <div class="social-links">
            <a href="#"><i class="fab fa-facebook-f"></i></a>
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-linkedin-in"></i></a>
            <a href="#"><i class="fab fa-instagram"></i></a>
          </div>
        </div>
        <div class="footer-links">
          <div class="footer-column">
            <h3>Product</h3>
            <ul>
              <li><a href="templates.html">Templates</a></li>
              <li><a href="#features">Features</a></li>
              <li><a href="pricing.html">Pricing</a></li>
              <li><a href="faq.html">FAQ</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h3>Company</h3>
            <ul>
              <li><a href="about.html">About Us</a></li>
              <li><a href="careers.html">Careers</a></li>
              <li><a href="blog.html">Blog</a></li>
              <li><a href="contact.html">Contact Us</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h3>Legal</h3>
            <ul>
              <li><a href="privacy.html">Privacy Policy</a></li>
              <li><a href="terms.html">Terms of Service</a></li>
              <li><a href="cookies.html">Cookie Policy</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2023 ResumeForge. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Template Card Template (for JavaScript) -->
  <template id="templateCardTemplate">
    <div class="template-card" data-category="">
      <div class="template-image">
        <img src="/placeholder.svg" alt="Resume Template">
        <div class="template-overlay">
          <a href="#" class="btn btn-primary use-template-btn">Use Template</a>
          <a href="#" class="btn btn-outline preview-template-btn">Preview</a>
        </div>
        <div class="template-badge"></div>
      </div>
      <div class="template-info">
        <h3></h3>
        <p></p>
        <div class="template-meta">
          <span class="downloads"><i class="fas fa-download"></i> <span class="download-count"></span></span>
          <span class="rating"><i class="fas fa-star"></i> <span class="rating-value"></span></span>
        </div>
      </div>
    </div>
  </template>

  <script src="templates/main.js"></script>
  <script src="templates/templates.js"></script>
</body>
</html>
