import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { FaBriefcase, FaBars, FaPlus, FaTimes } from "react-icons/fa";
import { format } from "date-fns";

/* ---------- global stores ---------- */
import { useResumeStore } from "../../../store/useResumeDetailStore";
import { useAddInfoStore } from "../../../store/addInfoStore";
import { useUpdateInfoStore } from "../../../store/updateInfoStore";
import { useDeleteInfoStore } from "../../../store/deleteInfoStore";

/* ---------- UI ---------- */
import DataCard from "../../Components/DataCard";
import ExperienceModal from "../../Components/ExperienceModal";

const Experience = () => {
  const { resumeId } = useParams();
  const { resume, fetchResume, loading } = useResumeStore();
  const { addInfo } = useAddInfoStore();
  const { updateInfo } = useUpdateInfoStore();
  const { deleteInfo } = useDeleteInfoStore();

  const experiences = resume?.Experience || [];

  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(null);

  useEffect(() => {
    if (resumeId) fetchResume(resumeId);
  }, [resumeId]);

  const formatRange = (start, end) => {
    if (!start && !end) return "";
    const from = start ? format(new Date(start), "MMM yyyy") : "";
    const to = end ? format(new Date(end), "MMM yyyy") : "Present";
    return `${from} – ${to}`;
  };

  const handleAddOrUpdate = async (payload, entryId = null) => {
    if (!resumeId) return;

    try {
      if (entryId) {
        await updateInfo({
          resumeId,
          section: "experience",
          entryId,
          updatedData: payload,
        });
      } else {
        await addInfo({
          resumeId,
          section: "experience",
          newData: payload,
        });
      }

      await fetchResume(resumeId);
      setModalOpen(false);
      setEditData(null);
    } catch (err) {
      alert("Failed to save experience.");
    }
  };

  const handleDeleteConfirmed = async () => {
    if (!resumeId || !confirmDelete) return;

    try {
      await deleteInfo({
        resumeId,
        section: "experience",
        entryId: confirmDelete._id,
      });
      await fetchResume(resumeId);
    } catch (err) {
      alert("Failed to delete experience.");
    } finally {
      setConfirmDelete(null);
    }
  };

  return (
    <div className="bg-white min-h-screen py-10 px-4 md:px-8 text-black font-sans">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaBriefcase className="text-black" />
          <h2 className="text-2xl font-extrabold">Experience</h2>
        </div>
        <FaBars className="text-black" />
      </div>

      {/* Add Button */}
      <button
        onClick={() => {
          setEditData(null);
          setModalOpen(true);
        }}
        className="cursor-pointer border-2 border-dashed border-gray-400 rounded-lg py-5 px-8 flex items-center justify-center bg-gray-200 hover:bg-gray-300 text-lg font-medium mb-8 w-full md:w-auto"
      >
        <FaPlus className="mr-2" /> Add a new item
      </button>

      {/* Experience List */}
      {loading ? (
        <p className="text-gray-500">Loading…</p>
      ) : experiences.length === 0 ? (
        <p className="text-gray-500">No experience added yet.</p>
      ) : (
        <div className="flex flex-wrap gap-4">
          {experiences.map((exp) => (
            <div key={exp._id} className="w-fit min-w-[250px]">
              <DataCard
                title={`${exp.Company} — ${exp.Position}`}
                subtitle={`${formatRange(exp.StartDate, exp.EndDate)} • ${
                  exp.Location
                }`}
                description={exp.Description}
                onEdit={() => {
                  setEditData(exp);
                  setModalOpen(true);
                }}
                onDelete={() => setConfirmDelete(exp)}
              />
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      {modalOpen && (
        <ExperienceModal
          initial={editData}
          onClose={() => {
            setModalOpen(false);
            setEditData(null);
          }}
          onSave={handleAddOrUpdate}
        />
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center px-4">
          <div className="bg-white max-w-md w-full p-6 rounded-lg shadow-xl text-[#29354d]">
            <button
              onClick={() => setConfirmDelete(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
            >
              <FaTimes size={20} />
            </button>
            <h3 className="text-lg font-bold mb-2">Delete Experience</h3>
            <p className="text-sm mb-4">
              Are you sure you want to delete your experience at{" "}
              <strong>{confirmDelete.Company}</strong>?
            </p>

            <div className="border p-3 rounded bg-gray-50 text-sm mb-4">
              <p>
                <strong>Role:</strong> {confirmDelete.Position}
              </p>
              <p>
                <strong>Duration:</strong>{" "}
                {formatRange(confirmDelete.StartDate, confirmDelete.EndDate)}
              </p>
              <p>
                <strong>Location:</strong> {confirmDelete.Location}
              </p>
              {confirmDelete.Description && (
                <p className="mt-2">
                  <strong>Description:</strong> {confirmDelete.Description}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirmed}
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Experience;
