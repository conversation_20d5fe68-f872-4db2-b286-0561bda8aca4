<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);

// Check if profile is complete
if (!isProfileComplete($user_id)) {
    setMessage('Please complete your profile before creating a resume.', 'error');
    redirect('profile.php');
}

// Check if template_id is provided
if (!isset($_GET['template_id']) || empty($_GET['template_id'])) {
    setMessage('Please select a template first.', 'error');
    // redirect('templates.php');
}

$template_id = (int)$_GET['template_id'];
// Get template
$db->query("SELECT * FROM resume_templates WHERE id = :id");
$db->bind(':id', $template_id);
$template = $db->single();

if (!$template) {
    setMessage('Template not found.', 'error');
    redirect('templates.php');
}

// Get user data
$skills = getUserSkills($user_id);
$experiences = getUserWorkExperience($user_id);
$educations = getUserEducation($user_id);
$projects = getUserProjects($user_id);
$certifications = getUserCertifications($user_id);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        echo "working";
        // Log form submission for debugging
        error_log("Form submitted with data: " . print_r($_POST, true));
        
        // Validate required fields
        if (!isset($_POST['resume_name']) || empty($_POST['resume_name'])) {
            throw new Exception("Resume name is required");
        }
        
        if (!isset($_POST['color_scheme']) || empty($_POST['color_scheme'])) {
            throw new Exception("Color scheme is required");
        }
        
        if (!isset($_POST['font_family']) || empty($_POST['font_family'])) {
            throw new Exception("Font family is required");
        }
        
        if (!isset($_POST['sections']) || empty($_POST['sections'])) {
            throw new Exception("At least one section must be selected");
        }
        
        // Get form data
        $resume_name = sanitize($_POST['resume_name']);
        $color_scheme = sanitize($_POST['color_scheme']);
        $font_family = sanitize($_POST['font_family']);
        $sections = $_POST['sections'];
        
        // Generate professional objective if selected
        $objective = in_array('objective', $sections) ? generateObjective($profile) : '';
        
        // Prepare resume data
        $resume_data = [
            'profile' => $profile,
            'skills' => $skills,
            'experiences' => $experiences,
            'educations' => $educations,
            'projects' => $projects,
            'certifications' => $certifications,
            'sections' => $sections,
            'objective' => $objective
        ];
        
        // Convert to JSON
        $resume_data_json = json_encode($resume_data);
        
        if ($resume_data_json === false) {
            throw new Exception("Error encoding resume data: " . json_last_error_msg());
        }
        
        // Save resume
        $db->query("INSERT INTO saved_resumes (user_id, template_id, resume_name, resume_data, color_scheme, font_family, created_at) 
                    VALUES (:user_id, :template_id, :resume_name, :resume_data, :color_scheme, :font_family, NOW())");
        
        $db->bind(':user_id', $user_id);
        $db->bind(':template_id', $template_id);
        $db->bind(':resume_name', $resume_name);
        $db->bind(':resume_data', $resume_data_json);
        $db->bind(':color_scheme', $color_scheme);
        $db->bind(':font_family', $font_family);
        
        if ($db->execute()) {
            $resume_id = $db->lastInsertId();
            setMessage('Resume created successfully!', 'success');
            redirect('edit-resume.php?id=' . $resume_id);
        } else {
            throw new Exception("Database error: " . print_r($db->errorInfo(), true));
        }
    } catch (Exception $e) {
        error_log("Error creating resume: " . $e->getMessage());
        setMessage('Error: ' . $e->getMessage(), 'error');
    }
}

// Get color schemes
$color_schemes = json_decode($template['color_schemes'], true);
if (!$color_schemes || !is_array($color_schemes)) {
    $color_schemes = ['blue', 'gray', 'black', 'green']; // Default fallback
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Resume - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/create-resume.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="dashboard-container">
        <div class="sidebar">
            <div class="user-info">
                <div class="user-avatar">
                    <?php if (!empty($profile['profile_image'])): ?>
                        <img src="<?php echo UPLOAD_DIR . 'profiles/' . $profile['profile_image']; ?>" alt="Profile Image">
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <h3><?php echo !empty($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : $_SESSION['username']; ?></h3>
                <p><?php echo !empty($profile['title']) ? $profile['title'] : 'Complete your profile'; ?></p>
            </div>
            
            <nav class="sidebar-nav">
                <ul style="gap:0">
                    <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                    <li><a href="skills.php"><i class="fas fa-star"></i> Skills</a></li>
                    <li><a href="experience.php"><i class="fas fa-briefcase"></i> Experience</a></li>
                    <li><a href="education.php"><i class="fas fa-graduation-cap"></i> Education</a></li>
                    <li><a href="projects.php"><i class="fas fa-project-diagram"></i> Projects</a></li>
                    <li><a href="certifications.php"><i class="fas fa-certificate"></i> Certifications</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>Create Resume</h1>
                <div class="action-buttons">
                    <a href="templates.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Templates</a>
                </div>
            </div>
            
            <?php echo displayMessage(); ?>
            
            <div class="create-resume-container">
                <div class="template-preview">
                    <h2>Selected Template</h2>
                    <div class="template-card">
                        <div class="template-image">
                            <img src="<?php echo $template['preview_image']; ?>" alt="<?php echo $template['template_name']; ?>">
                        </div>
                        <div class="template-info">
                            <h3><?php echo $template['template_name']; ?></h3>
                            <p><?php echo $template['description']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="resume-options">
                    <h2>Resume Options</h2>
                    <form action="create-resume.php?template_id=<?php echo $template_id; ?>" method="POST" class="resume-options-form">
                        <div class="form-group">
                            <label for="resume_name">Resume Name</label>
                            <input type="text" id="resume_name" name="resume_name" value="My Resume" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Color Scheme</label>
                            <div class="color-schemes">
                                <?php foreach ($color_schemes as $index => $color): ?>
                                    <label class="color-option <?php echo $index === 0 ? 'active' : ''; ?>">
                                        <input type="radio" name="color_scheme" value="<?php echo $color; ?>" <?php echo $index === 0 ? 'checked' : ''; ?>>
                                        <span class="color-swatch" style="background-color: var(--<?php echo $color; ?>);"></span>
                                        <span class="color-name"><?php echo ucfirst($color); ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Font Family</label>
                            <div class="font-families">
                                <label class="font-option active">
                                    <input type="radio" name="font_family" value="default" checked>
                                    <span class="font-preview" style="font-family: 'Arial', sans-serif;">Aa</span>
                                    <span class="font-name">Default</span>
                                </label>
                                <label class="font-option">
                                    <input type="radio" name="font_family" value="serif">
                                    <span class="font-preview" style="font-family: 'Georgia', serif;">Aa</span>
                                    <span class="font-name">Serif</span>
                                </label>
                                <label class="font-option">
                                    <input type="radio" name="font_family" value="monospace">
                                    <span class="font-preview" style="font-family: 'Courier New', monospace;">Aa</span>
                                    <span class="font-name">Monospace</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Sections to Include</label>
                            <div class="section-options">
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="contact" checked>
                                    <span>Contact Information</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="objective" checked>
                                    <span>Professional Objective</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="skills" checked>
                                    <span>Skills</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="experience" checked>
                                    <span>Work Experience</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="education" checked>
                                    <span>Education</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="projects" checked>
                                    <span>Projects</span>
                                </label>
                                <label class="section-option">
                                    <input type="checkbox" name="sections[]" value="certifications" checked>
                                    <span>Certifications</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Create Resume</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="js/create-resume.js"></script>
</body>
</html>