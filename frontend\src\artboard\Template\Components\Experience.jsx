import React from "react";

const Experience = ({
  awards = [],
  title = "Awards",
  sectionStyle = "",
  titleStyle = "",
  itemTitleStyle = "",
  awarderStyle = "",
  dateStyle = "",
  summaryStyle = "",
}) => {
  if (!awards.length) return null;

  return (
    <section className={sectionStyle}>
      <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      {awards.map((award, index) => (
        <div key={index} className="mb-4">
          <div className="flex justify-between">
            <strong className={`text-base ${itemTitleStyle}`}>{award.title}</strong>
            <span className={`text-sm italic ${dateStyle}`}>{award.date}</span>
          </div>
          <div className={`text-sm italic ${awarderStyle}`}>
            {award.awarder}
          </div>
          {award.summary && (
            <div
              className={`text-sm mt-1 ${summaryStyle}`}
              dangerouslySetInnerHTML={{ __html: award.summary }}
            />
          )}
        </div>
      ))}
    </section>
  );
};

export default Experience;