// ✅ store/useAdminStore.js
import { create } from 'zustand';
import axios from 'axios';

const API_BASE = 'http://localhost:5000/api/admin';

export const useAdminStore = create((set, get) => ({
    users: [],
    resumes: [],
    userResumes: {}, // ✅ Cached resumes per user
    selectedResume: null,
    selectedUserId: null,
    currentPage: 1,
    itemsPerPage: 5,
    paginatedResumes: [],
    loading: false,
    error: null,

    fetchAllUsers: async () => {
        set({ loading: true });
        try {
            const res = await axios.get(`${API_BASE}/users`, { withCredentials: true });
            set({ users: res.data, loading: false });
            console.log(res.data);
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch users",
            });
        }
    },

    fetchUserResumes: async (userId) => {
        const { userResumes } = get();

        // ✅ Use cache if exists
        if (userResumes[userId]) {
            set({ resumes: userResumes[userId], selectedUserId: userId });
            get().paginateResumes(1);
            return;
        }

        set({ loading: true, selectedUserId: userId });
        try {
            const res = await axios.get(`${API_BASE}/user/${userId}`, {
                withCredentials: true,
            });

            const data = res.data;
            set((state) => ({
                resumes: data,
                userResumes: {
                    ...state.userResumes,
                    [userId]: data,
                },
                loading: false,
            }));
            get().paginateResumes(1);
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resumes",
            });
        }
    },

    fetchResumeDetails: async (resumeId) => {
        set({ loading: true });
        try {
            const res = await axios.get(`${API_BASE}/resume/${resumeId}`, {
                withCredentials: true,
            });
            set({ selectedResume: res.data, loading: false });
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resume details",
            });
        }
    },

    deleteResume: async (resumeId) => {
        try {
            await axios.delete(`${API_BASE}/resume/${resumeId}`, {
                withCredentials: true,
            });

            const { selectedUserId, resumes, userResumes } = get();

            // Remove from current resumes and update pagination
            const updatedResumes = resumes.filter((r) => r._id !== resumeId);

            // Update main resumes and userResumes cache
            set((state) => ({
                resumes: updatedResumes,
                userResumes: {
                    ...state.userResumes,
                    [selectedUserId]: updatedResumes,
                },
            }));

            get().paginateResumes(get().currentPage);
        } catch (err) {
            console.error("Failed to delete resume:", err);
        }
    },

    paginateResumes: (page) => {
        const { resumes, itemsPerPage } = get();
        const start = (page - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        set({
            currentPage: page,
            paginatedResumes: resumes.slice(start, end),
        });
    },

    fetchAllResumes: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axios.get(`${API_BASE}/all`, {
                withCredentials: true,
            });
            set({ resumes: res.data, loading: false });
            get().paginateResumes(1);
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resumes",
            });
        }
    },

    setSelectedResumeId: (resumeId) => {
        get().fetchResumeDetails(resumeId);
    },

    clearResumeSelection: () => {
        set({ selectedResume: null });
    },

    // ✅ Selector for getting cached resumes by user
    getUserResumes: (userId) => {
        return get().userResumes[userId] || [];
    },
}));
