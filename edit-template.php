<?php
error_reporting(E_ALL);
ini_set('display_errors',1);
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$template_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$template_id) {
    redirect('templates.php');
}

// Get template details
$db->query("SELECT * FROM templates WHERE id = :id");
$db->bind(':id', $template_id);
$template = $db->single();

if (!$template) {
    setMessage('Template not found.', 'error');
    redirect('templates.php');
}

// Get user profile data
$profile = getUserProfile($user_id);

// Get user's additional data
$db->query("SELECT * FROM skills WHERE user_id = :user_id ORDER BY skill_name");
$db->bind(':user_id', $user_id);
$skills = $db->resultSet();

$db->query("SELECT * FROM user_experience WHERE user_id = :user_id ORDER BY start_date DESC");
$db->bind(':user_id', $user_id);
$experiences = $db->resultSet();

$db->query("SELECT * FROM user_education WHERE user_id = :user_id ORDER BY start_date DESC");
$db->bind(':user_id', $user_id);
$education = $db->resultSet();

$db->query("SELECT * FROM projects WHERE user_id = :user_id ORDER BY start_date DESC");
$db->bind(':user_id', $user_id);
$projects = $db->resultSet();

$db->query("SELECT * FROM certifications WHERE user_id = :user_id ORDER BY issue_date DESC");
$db->bind(':user_id', $user_id);
$certifications = $db->resultSet();

// Handle save resume
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_resume'])) {
    $resume_name = sanitize($_POST['resume_name']);
    $resume_data = $_POST['resume_data']; // JSON data
    
    if (empty($resume_name)) {
        $resume_name = $template['name'] . ' - ' . date('Y-m-d');
    }
    
    // Save resume
    $db->query("INSERT INTO user_resumes (user_id, template_id, resume_name, resume_data, created_at, updated_at) 
                VALUES (:user_id, :template_id, :resume_name, :resume_data, NOW(), NOW())");
    $db->bind(':user_id', $user_id);
    $db->bind(':template_id', $template_id);
    $db->bind(':resume_name', $resume_name);
    $db->bind(':resume_data', $resume_data);
    
    if ($db->execute()) {
        $resume_id = $db->lastInsertId();
        setMessage('Resume saved successfully!', 'success');
        redirect('my-resumes.php');
    } else {
        setMessage('Failed to save resume.', 'error');
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Resume - <?php echo htmlspecialchars($template['name']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--light);
            min-height: 100vh;
        }
        
        /* Editor Layout */
        .editor-container {
            display: flex;
            height: calc(100vh - 70px);
            position: relative;
        }
        
        .editor-sidebar {
            width: 300px;
            background-color: var(--white);
            border-right: 1px solid var(--light-gray);
            overflow-y: auto;
            position: relative;
            z-index: 10;
        }
        
        .editor-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--light);
        }
        
        .editor-toolbar {
            background-color: var(--white);
            border-bottom: 1px solid var(--light-gray);
            padding: var(--spacing-sm) var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .template-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .zoom-btn {
            width: 32px;
            height: 32px;
            border: 1px solid var(--light-gray);
            background-color: var(--white);
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .zoom-btn:hover {
            background-color: var(--light);
        }
        
        .zoom-level {
            font-size: 0.9rem;
            color: var(--gray);
            min-width: 50px;
            text-align: center;
        }
        
        .resume-preview {
            flex: 1;
            padding: var(--spacing-lg);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            overflow: auto;
        }
        
        .resume-container {
            background-color: var(--white);
            box-shadow: var(--shadow-lg);
            border-radius: var(--radius);
            overflow: hidden;
            transform-origin: top center;
            transition: transform 0.3s ease;
        }
        
        /* Sidebar Sections */
        .sidebar-section {
            border-bottom: 1px solid var(--light-gray);
        }
        
        .section-header {
            padding: var(--spacing-md);
            background-color: var(--light);
            border-bottom: 1px solid var(--light-gray);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: var(--primary);
        }
        
        .section-header:hover {
            background-color: var(--primary-light);
        }
        
        .section-content {
            padding: var(--spacing-md);
            display: none;
        }
        
        .section-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: var(--spacing-md);
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
            font-size: 0.9rem;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--light-gray);
            border-radius: var(--radius);
            font-family: inherit;
            font-size: 0.9rem;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(45, 95, 139, 0.1);
            outline: none;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        /* Dynamic Lists */
        .dynamic-list {
            margin-bottom: var(--spacing-md);
        }
        
        .list-item {
            background-color: var(--light);
            border-radius: var(--radius);
            padding: var(--spacing-sm);
            margin-bottom: 0.5rem;
            border: 1px solid var(--light-gray);
        }
        
        .list-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .list-item-title {
            font-weight: 500;
            color: var(--dark);
        }
        
        .list-item-actions {
            display: flex;
            gap: 0.25rem;
        }
        
        .btn-icon {
            width: 24px;
            height: 24px;
            border: none;
            background-color: transparent;
            color: var(--gray);
            cursor: pointer;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }
        
        .btn-icon:hover {
            background-color: var(--white);
            color: var(--primary);
        }
        
        .btn-icon.delete:hover {
            color: var(--danger);
        }
        
        .add-item-btn {
            width: 100%;
            padding: 0.5rem;
            border: 1px dashed var(--light-gray);
            background-color: transparent;
            color: var(--gray);
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .add-item-btn:hover {
            border-color: var(--primary);
            color: var(--primary);
            background-color: var(--primary-light);
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.9rem;
            text-align: center;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary);
            color: var(--white);
        }
        
        .btn-success {
            background-color: var(--success);
            color: var(--white);
        }
        
        .btn-success:hover {
            background-color: #059669;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        /* Resume Template Styles */
        .resume-template {
            width: 210mm;
            min-height: 297mm;
            padding: 20mm;
            background-color: var(--white);
            font-family: 'Inter', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
        }
        
        .resume-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--primary);
        }
        
        .resume-name {
            font-size: 28pt;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .resume-title {
            font-size: 14pt;
            color: var(--gray);
            margin-bottom: 15px;
        }
        
        .resume-contact {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 10pt;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .resume-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 14pt;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--light-gray);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .experience-item,
        .education-item,
        .project-item {
            margin-bottom: 20px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
        }
        
        .item-title {
            font-weight: 600;
            color: var(--dark);
        }
        
        .item-company,
        .item-school {
            color: var(--primary);
            font-weight: 500;
        }
        
        .item-date {
            color: var(--gray);
            font-size: 10pt;
        }
        
        .item-description {
            margin-top: 8px;
            color: var(--dark);
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .skill-item {
            background-color: var(--light);
            padding: 8px 12px;
            border-radius: var(--radius);
            text-align: center;
            font-size: 10pt;
        }
        
        /* Editable Elements */
        .editable {
            position: relative;
            cursor: text;
            transition: var(--transition);
        }
        
        .editable:hover {
            background-color: rgba(45, 95, 139, 0.05);
            outline: 1px dashed var(--primary);
        }
        
        .editable:focus {
            background-color: var(--white);
            outline: 2px solid var(--primary);
            box-shadow: 0 0 0 3px rgba(45, 95, 139, 0.1);
        }
        
        /* Save Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            max-width: 400px;
            width: 90%;
            transform: scale(0.9);
            transition: var(--transition);
        }
        
        .modal.active .modal-content {
            transform: scale(1);
        }
        
        .modal-header {
            margin-bottom: var(--spacing-md);
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .modal-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
            margin-top: var(--spacing-md);
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .editor-container {
                flex-direction: column;
                height: auto;
            }
            
            .editor-sidebar {
                width: 100%;
                height: auto;
                max-height: 300px;
            }
            
            .resume-template {
                width: 100%;
                min-height: auto;
                padding: 15px;
                transform: scale(0.8);
            }
        }
        
        @media (max-width: 768px) {
            .toolbar-left,
            .toolbar-right {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .zoom-controls {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <?php include "includes/header.php" ?>
    
    <div class="editor-container">
        <!-- Sidebar -->
        <div class="editor-sidebar">
            <!-- Personal Information Section -->
            <div class="sidebar-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span><i class="fas fa-user"></i> Personal Info</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="section-content active">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" value="<?php echo htmlspecialchars($profile['first_name'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" value="<?php echo htmlspecialchars($profile['last_name'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="title">Professional Title</label>
                        <input type="text" id="title" value="<?php echo htmlspecialchars($profile['title'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" value="<?php echo htmlspecialchars($_SESSION['email']); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="tel" id="phone" value="<?php echo htmlspecialchars($profile['phone'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" value="<?php echo htmlspecialchars($profile['address'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                    <div class="form-group">
                        <label for="website">Website/LinkedIn</label>
                        <input type="url" id="website" value="<?php echo htmlspecialchars($profile['website'] ?? ''); ?>" onchange="updateResume()">
                    </div>
                </div>
            </div>
            
            <!-- Summary Section -->
            <div class="sidebar-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span><i class="fas fa-align-left"></i> Summary</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <label for="summary">Professional Summary</label>
                        <textarea id="summary" rows="4" onchange="updateResume()"><?php echo htmlspecialchars($profile['summary'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Experience Section -->
            <div class="sidebar-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span><i class="fas fa-briefcase"></i> Experience</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="section-content">
                    <div class="dynamic-list" id="experience-list">
                        <?php foreach ($experiences as $index => $exp): ?>
                            <div class="list-item" data-index="<?php echo $index; ?>">
                                <div class="list-item-header">
                                    <span class="list-item-title"><?php echo htmlspecialchars($exp['job_title']); ?></span>
                                    <div class="list-item-actions">
                                        <button class="btn-icon" onclick="editExperience(<?php echo $index; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="removeExperience(<?php echo $index; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="experience-data" style="display: none;">
                                    <?php echo json_encode($exp); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button class="add-item-btn" onclick="addExperience()">
                        <i class="fas fa-plus"></i> Add Experience
                    </button>
                </div>
            </div>
            
            <!-- Education Section -->
            <div class="sidebar-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span><i class="fas fa-graduation-cap"></i> Education</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="section-content">
                    <div class="dynamic-list" id="education-list">
                        <?php foreach ($education as $index => $edu): ?>
                            <div class="list-item" data-index="<?php echo $index; ?>">
                                <div class="list-item-header">
                                    <span class="list-item-title"><?php echo htmlspecialchars($edu['degree']); ?></span>
                                    <div class="list-item-actions">
                                        <button class="btn-icon" onclick="editEducation(<?php echo $index; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="removeEducation(<?php echo $index; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="education-data" style="display: none;">
                                    <?php echo json_encode($edu); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <button class="add-item-btn" onclick="addEducation()">
                        <i class="fas fa-plus"></i> Add Education
                    </button>
                </div>
            </div>
            
            <!-- Skills Section -->
            <div class="sidebar-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <span><i class="fas fa-star"></i> Skills</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="section-content">
                    <div class="dynamic-list" id="skills-list">
                        <?php foreach ($skills as $index => $skill): ?>
                            <div class="list-item" data-index="<?php echo $index; ?>">
                                <div class="list-item-header">
                                    <span class="list-item-title"><?php echo htmlspecialchars($skill['skill_name']); ?></span>
                                    <div class="list-item-actions">
                                        <button class="btn-icon delete" onclick="removeSkill(<?php echo $index; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="skill-data" style="display: none;">
                                    <?php echo json_encode($skill); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="form-group">
                        <input type="text" id="new-skill" placeholder="Add a skill..." onkeypress="addSkillOnEnter(event)">
                    </div>
                    <button class="add-item-btn" onclick="addSkill()">
                        <i class="fas fa-plus"></i> Add Skill
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Editor -->
        <div class="editor-content">
            <!-- Toolbar -->
            <div class="editor-toolbar">
                <div class="toolbar-left">
                    <span class="template-name"><?php echo htmlspecialchars($template['name']); ?></span>
                </div>
                <div class="toolbar-right">
                    <div class="zoom-controls">
                        <button class="zoom-btn" onclick="zoomOut()">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="zoom-level" id="zoom-level">100%</span>
                        <button class="zoom-btn" onclick="zoomIn()">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="btn btn-outline btn-sm" onclick="previewResume()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button class="btn btn-success btn-sm" onclick="saveResume()">
                        <i class="fas fa-save"></i> Save Resume
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="downloadResume()">
                        <i class="fas fa-download"></i> Download PDF
                    </button>
                </div>
            </div>
            
            <!-- Resume Preview -->
            <div class="resume-preview">
                <div class="resume-container" id="resume-container">
                    <div class="resume-template" id="resume-template">
                        <!-- Resume Header -->
                        <div class="resume-header">
                            <div class="resume-name editable" contenteditable="true" id="resume-name">
                                <?php echo htmlspecialchars(($profile['first_name'] ?? '') . ' ' . ($profile['last_name'] ?? '')); ?>
                            </div>
                            <div class="resume-title editable" contenteditable="true" id="resume-title">
                                <?php echo htmlspecialchars($profile['title'] ?? 'Professional Title'); ?>
                            </div>
                            <div class="resume-contact">
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span id="contact-email"><?php echo htmlspecialchars($_SESSION['email']); ?></span>
                                </div>
                                <?php if (!empty($profile['phone'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span id="contact-phone"><?php echo htmlspecialchars($profile['phone']); ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($profile['address'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span id="contact-location"><?php echo htmlspecialchars($profile['address']); ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($profile['website'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <span id="contact-website"><?php echo htmlspecialchars($profile['website']); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Summary Section -->
                        <?php if (!empty($profile['summary'])): ?>
                        <div class="resume-section">
                            <div class="section-title">Professional Summary</div>
                            <div class="editable" contenteditable="true" id="resume-summary">
                                <?php echo htmlspecialchars($profile['summary']); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Experience Section -->
                        <?php if (!empty($experiences)): ?>
                        <div class="resume-section">
                            <div class="section-title">Professional Experience</div>
                            <div id="resume-experience">
                                <?php foreach ($experiences as $exp): ?>
                                <div class="experience-item">
                                    <div class="item-header">
                                        <div>
                                            <div class="item-title"><?php echo htmlspecialchars($exp['job_title']); ?></div>
                                            <div class="item-company"><?php echo htmlspecialchars($exp['company_name']); ?></div>
                                        </div>
                                        <div class="item-date">
                                            <?php echo date('M Y', strtotime($exp['start_date'])); ?> - 
                                            <?php echo $exp['end_date'] ? date('M Y', strtotime($exp['end_date'])) : 'Present'; ?>
                                        </div>
                                    </div>
                                    <?php if (!empty($exp['description'])): ?>
                                    <div class="item-description"><?php echo nl2br(htmlspecialchars($exp['description'])); ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Education Section -->
                        <?php if (!empty($education)): ?>
                        <div class="resume-section">
                            <div class="section-title">Education</div>
                            <div id="resume-education">
                                <?php foreach ($education as $edu): ?>
                                <div class="education-item">
                                    <div class="item-header">
                                        <div>
                                            <div class="item-title"><?php echo htmlspecialchars($edu['degree']); ?></div>
                                            <div class="item-school"><?php echo htmlspecialchars($edu['institution']); ?></div>
                                        </div>
                                        <div class="item-date">
                                            <?php echo date('Y', strtotime($edu['start_date'])); ?> - 
                                            <?php echo $edu['end_date'] ? date('Y', strtotime($edu['end_date'])) : 'Present'; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Skills Section -->
                        <?php if (!empty($skills)): ?>
                        <div class="resume-section">
                            <div class="section-title">Skills</div>
                            <div class="skills-grid" id="resume-skills">
                                <?php foreach ($skills as $skill): ?>
                                <div class="skill-item"><?php echo htmlspecialchars($skill['skill_name']); ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Projects Section -->
                        <?php if (!empty($projects)): ?>
                        <div class="resume-section">
                            <div class="section-title">Projects</div>
                            <div id="resume-projects">
                                <?php foreach ($projects as $project): ?>
                                <div class="project-item">
                                    <div class="item-header">
                                        <div>
                                            <div class="item-title"><?php echo htmlspecialchars($project['project_name']); ?></div>
                                        </div>
                                        <div class="item-date">
                                            <?php echo date('M Y', strtotime($project['start_date'])); ?> - 
                                            <?php echo $project['end_date'] ? date('M Y', strtotime($project['end_date'])) : 'Present'; ?>
                                        </div>
                                    </div>
                                    <?php if (!empty($project['description'])): ?>
                                    <div class="item-description"><?php echo nl2br(htmlspecialchars($project['description'])); ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Certifications Section -->
                        <?php if (!empty($certifications)): ?>
                        <div class="resume-section">
                            <div class="section-title">Certifications</div>
                            <div id="resume-certifications">
                                <?php foreach ($certifications as $cert): ?>
                                <div class="certification-item">
                                    <div class="item-header">
                                        <div>
                                            <div class="item-title"><?php echo htmlspecialchars($cert['certification_name']); ?></div>
                                            <div class="item-company"><?php echo htmlspecialchars($cert['issuing_organization']); ?></div>
                                        </div>
                                        <div class="item-date">
                                            <?php echo date('M Y', strtotime($cert['date_obtained'])); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Save Resume Modal -->
    <div class="modal" id="save-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Save Resume</h3>
                <p>Give your resume a name to save it to your account.</p>
            </div>
            <form id="save-form" method="POST">
                <div class="form-group">
                    <label for="resume_name">Resume Name</label>
                    <input type="text" id="resume_name" name="resume_name" value="<?php echo htmlspecialchars($template['name']); ?> - <?php echo date('Y-m-d'); ?>" required>
                </div>
                <input type="hidden" name="resume_data" id="resume_data">
                <input type="hidden" name="save_resume" value="1">
                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeSaveModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Resume</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        let currentZoom = 100;
        let resumeData = {};
        
        // Initialize resume data
        document.addEventListener('DOMContentLoaded', function() {
            updateResumeData();
            
            // Add event listeners for editable elements
            const editableElements = document.querySelectorAll('.editable');
            editableElements.forEach(element => {
                element.addEventListener('input', updateResumeData);
                element.addEventListener('blur', updateResumeData);
            });
        });
        
        // Toggle sidebar sections
        function toggleSection(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('i:last-child');
            
            content.classList.toggle('active');
            
            if (content.classList.contains('active')) {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            } else {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            }
        }
        
        // Zoom functions
        function zoomIn() {
            if (currentZoom < 150) {
                currentZoom += 10;
                updateZoom();
            }
        }
        
        function zoomOut() {
            if (currentZoom > 50) {
                currentZoom -= 10;
                updateZoom();
            }
        }
        
        function updateZoom() {
            const container = document.getElementById('resume-container');
            const zoomLevel = document.getElementById('zoom-level');
            
            container.style.transform = `scale(${currentZoom / 100})`;
            zoomLevel.textContent = currentZoom + '%';
        }
        
        // Update resume content
        function updateResume() {
            const firstName = document.getElementById('first_name').value;
            const lastName = document.getElementById('last_name').value;
            const title = document.getElementById('title').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const location = document.getElementById('location').value;
            const website = document.getElementById('website').value;
            const summary = document.getElementById('summary').value;
            
            // Update resume display
            document.getElementById('resume-name').textContent = `${firstName} ${lastName}`;
            document.getElementById('resume-title').textContent = title;
            document.getElementById('contact-email').textContent = email;
            
            if (phone) {
                document.getElementById('contact-phone').textContent = phone;
                document.getElementById('contact-phone').parentElement.style.display = 'flex';
            } else {
                document.getElementById('contact-phone').parentElement.style.display = 'none';
            }
            
            if (location) {
                document.getElementById('contact-location').textContent = location;
                document.getElementById('contact-location').parentElement.style.display = 'flex';
            } else {
                document.getElementById('contact-location').parentElement.style.display = 'none';
            }
            
            if (website) {
                document.getElementById('contact-website').textContent = website;
                document.getElementById('contact-website').parentElement.style.display = 'flex';
            } else {
                document.getElementById('contact-website').parentElement.style.display = 'none';
            }
            
            if (summary) {
                document.getElementById('resume-summary').textContent = summary;
                document.getElementById('resume-summary').parentElement.style.display = 'block';
            } else {
                document.getElementById('resume-summary').parentElement.style.display = 'none';
            }
            
            updateResumeData();
        }
        
        // Update resume data object
        function updateResumeData() {
            resumeData = {
                personal: {
                    firstName: document.getElementById('first_name').value,
                    lastName: document.getElementById('last_name').value,
                    title: document.getElementById('title').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value,
                    location: document.getElementById('location').value,
                    website: document.getElementById('website').value
                },
                summary: document.getElementById('summary').value,
                experience: getExperienceData(),
                education: getEducationData(),
                skills: getSkillsData(),
                template_id: <?php echo $template_id; ?>
            };
        }
        
        // Get experience data
        function getExperienceData() {
            const experiences = [];
            const experienceItems = document.querySelectorAll('#experience-list .list-item');
            
            experienceItems.forEach(item => {
                const dataElement = item.querySelector('.experience-data');
                if (dataElement) {
                    try {
                        const data = JSON.parse(dataElement.textContent);
                        experiences.push(data);
                    } catch (e) {
                        console.error('Error parsing experience data:', e);
                    }
                }
            });
            
            return experiences;
        }
        
        // Get education data
        function getEducationData() {
            const education = [];
            const educationItems = document.querySelectorAll('#education-list .list-item');
            
            educationItems.forEach(item => {
                const dataElement = item.querySelector('.education-data');
                if (dataElement) {
                    try {
                        const data = JSON.parse(dataElement.textContent);
                        education.push(data);
                    } catch (e) {
                        console.error('Error parsing education data:', e);
                    }
                }
            });
            
            return education;
        }
        
        // Get skills data
        function getSkillsData() {
            const skills = [];
            const skillItems = document.querySelectorAll('#skills-list .list-item');
            
            skillItems.forEach(item => {
                const dataElement = item.querySelector('.skill-data');
                if (dataElement) {
                    try {
                        const data = JSON.parse(dataElement.textContent);
                        skills.push(data);
                    } catch (e) {
                        console.error('Error parsing skill data:', e);
                    }
                }
            });
            
            return skills;
        }
        
        // Add skill
        function addSkill() {
            const skillInput = document.getElementById('new-skill');
            const skillName = skillInput.value.trim();
            
            if (skillName) {
                const skillsList = document.getElementById('skills-list');
                const index = skillsList.children.length;
                
                const skillItem = document.createElement('div');
                skillItem.className = 'list-item';
                skillItem.setAttribute('data-index', index);
                skillItem.innerHTML = `
                    <div class="list-item-header">
                        <span class="list-item-title">${skillName}</span>
                        <div class="list-item-actions">
                            <button class="btn-icon delete" onclick="removeSkill(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="skill-data" style="display: none;">
                        ${JSON.stringify({skill_name: skillName})}
                    </div>
                `;
                
                skillsList.appendChild(skillItem);
                skillInput.value = '';
                
                // Add to resume display
                const skillsGrid = document.getElementById('resume-skills');
                const skillElement = document.createElement('div');
                skillElement.className = 'skill-item';
                skillElement.textContent = skillName;
                skillsGrid.appendChild(skillElement);
                
                updateResumeData();
            }
        }
        
        // Add skill on Enter key
        function addSkillOnEnter(event) {
            if (event.key === 'Enter') {
                addSkill();
            }
        }
        
        // Remove skill
        function removeSkill(index) {
            const skillItem = document.querySelector(`#skills-list .list-item[data-index="${index}"]`);
            if (skillItem) {
                skillItem.remove();
                
                // Remove from resume display
                const skillsGrid = document.getElementById('resume-skills');
                const skillElements = skillsGrid.children;
                if (skillElements[index]) {
                    skillElements[index].remove();
                }
                
                updateResumeData();
            }
        }
        
        // Experience functions
        function addExperience() {
            // This would open a modal or form to add new experience
            alert('Add Experience functionality would be implemented here');
        }
        
        function editExperience(index) {
            // This would open a modal or form to edit experience
            alert('Edit Experience functionality would be implemented here');
        }
        
        function removeExperience(index) {
            const experienceItem = document.querySelector(`#experience-list .list-item[data-index="${index}"]`);
            if (experienceItem && confirm('Are you sure you want to remove this experience?')) {
                experienceItem.remove();
                updateResumeData();
                // Also update the resume display
                updateExperienceDisplay();
            }
        }
        
        // Education functions
        function addEducation() {
            alert('Add Education functionality would be implemented here');
        }
        
        function editEducation(index) {
            alert('Edit Education functionality would be implemented here');
        }
        
        function removeEducation(index) {
            const educationItem = document.querySelector(`#education-list .list-item[data-index="${index}"]`);
            if (educationItem && confirm('Are you sure you want to remove this education?')) {
                educationItem.remove();
                updateResumeData();
                // Also update the resume display
                updateEducationDisplay();
            }
        }
        
        // Update experience display
        function updateExperienceDisplay() {
            const experienceContainer = document.getElementById('resume-experience');
            experienceContainer.innerHTML = '';
            
            const experiences = getExperienceData();
            experiences.forEach(exp => {
                const expElement = document.createElement('div');
                expElement.className = 'experience-item';
                expElement.innerHTML = `
                    <div class="item-header">
                        <div>
                            <div class="item-title">${exp.job_title}</div>
                            <div class="item-company">${exp.company_name}</div>
                        </div>
                        <div class="item-date">
                            ${new Date(exp.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - 
                            ${exp.end_date ? new Date(exp.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'}) : 'Present'}
                        </div>
                    </div>
                    ${exp.description ? `<div class="item-description">${exp.description.replace(/\n/g, '<br>')}</div>` : ''}
                `;
                experienceContainer.appendChild(expElement);
            });
        }
        
        // Update education display
        function updateEducationDisplay() {
            const educationContainer = document.getElementById('resume-education');
            educationContainer.innerHTML = '';
            
            const education = getEducationData();
            education.forEach(edu => {
                const eduElement = document.createElement('div');
                eduElement.className = 'education-item';
                eduElement.innerHTML = `
                    <div class="item-header">
                        <div>
                            <div class="item-title">${edu.degree}</div>
                            <div class="item-school">${edu.institution}</div>
                        </div>
                        <div class="item-date">
                            ${new Date(edu.start_date).getFullYear()} - 
                            ${edu.end_date ? new Date(edu.end_date).getFullYear() : 'Present'}
                        </div>
                    </div>
                `;
                educationContainer.appendChild(eduElement);
            });
        }
        
        // Preview resume
        function previewResume() {
            const resumeContent = document.getElementById('resume-template').innerHTML;
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Resume Preview</title>
                    <style>
                        body { font-family: 'Inter', sans-serif; margin: 0; padding: 20px; }
                        .resume-template { max-width: 210mm; margin: 0 auto; }
                        /* Include relevant CSS styles here */
                    </style>
                </head>
                <body>
                    <div class="resume-template">${resumeContent}</div>
                </body>
                </html>
            `);
            previewWindow.document.close();
        }
        
        // Save resume
        function saveResume() {
            updateResumeData();
            document.getElementById('resume_data').value = JSON.stringify(resumeData);
            document.getElementById('save-modal').classList.add('active');
        }
        
        // Close save modal
        function closeSaveModal() {
            document.getElementById('save-modal').classList.remove('active');
        }
        
        // Download resume as PDF
        function downloadResume() {
            // This would integrate with a PDF generation service
            alert('PDF download functionality would be implemented here using a service like jsPDF or server-side PDF generation');
        }
        
        // Close modal when clicking outside
        document.getElementById('save-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSaveModal();
            }
        });
    </script>
</body>
</html>