import React from "react";
import { FaTimes, FaPlus } from "react-icons/fa";

const AddCertificationModal = ({
    showModal,
    closeModal,
    formData,
    handleInputChange,
    handleCreateOrUpdate,
    editingId,
    adding,
    updating,
}) => {
    if (!showModal) return null;

    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
            <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
                {/* Close Button */}
                <button
                    onClick={closeModal}
                    className="absolute top-4 right-4 text-[#29354d] hover:text-black transition"
                >
                    <FaTimes size={20} />
                </button>

                {/* Header */}
                <h2 className="text-xl font-bold mb-6 text-[#29354d] flex items-center gap-2">
                    <FaPlus />
                    {editingId ? "Update Certification" : "Add New Certification"}
                </h2>

                {/* Form */}
                <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Certification Title"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
                    />
                    <input
                        type="text"
                        name="organization"
                        value={formData.organization}
                        onChange={handleInputChange}
                        placeholder="Issuing Organization"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
                    />
                    <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
                    />
                    <input
                        type="url"
                        name="url"
                        value={formData.url}
                        onChange={handleInputChange}
                        placeholder="Credential URL"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d]"
                    />
                    <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="Description (optional)"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-[#29354d] resize-vertical min-h-[80px]"
                    />
                    <div className="flex justify-end">
                        <button
                            type="submit"
                            onClick={handleCreateOrUpdate}
                            disabled={adding || updating}
                            className={`${adding || updating ? "opacity-60 cursor-not-allowed" : ""
                                } bg-[#73716c] hover:bg-[#000000] text-[#f9f9f9] font-semibold px-6 py-2 rounded-md transition`}
                        >
                            {adding || updating
                                ? "Saving..."
                                : editingId
                                    ? "Update"
                                    : "Create"}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddCertificationModal;
