<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);


// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $first_name = sanitize($_POST['first_name']);
    $last_name = sanitize($_POST['last_name']);
    $title = sanitize($_POST['title']);
    $summary = sanitize($_POST['summary']);
    $phone = sanitize($_POST['phone']);
    $location = sanitize($_POST['location']);
    $website = sanitize($_POST['website']);
    
    // Validate form data
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = 'First name is required';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required';
    }
    
    // If no errors, update profile
    if (empty($errors)) {
        // Check if profile exists
        if ($profile) {
            // Update existing profile
            $db->query("UPDATE user_profiles SET 
                first_name = :first_name, 
                last_name = :last_name, 
                title = :title, 
                summary = :summary, 
                phone = :phone, 
                address = :location, 
                website = :website, 
                updated_at = NOW() 
                WHERE user_id = :user_id");
        } else {
            // Create new profile
            $db->query("INSERT INTO user_profiles (
                user_id, first_name, last_name, title, summary, phone, address, website, created_at, updated_at
                ) VALUES (
                :user_id, :first_name, :last_name, :title, :summary, :phone, :location, :website, NOW(), NOW()
                )");
        }
        
        $db->bind(':user_id', $user_id);
        $db->bind(':first_name', $first_name);
        $db->bind(':last_name', $last_name);
        $db->bind(':title', $title);
        $db->bind(':summary', $summary);
        $db->bind(':phone', $phone);
        $db->bind(':location', $location);
        $db->bind(':website', $website);
        
        if ($db->execute()) {
            setMessage('Profile updated successfully.', 'success');
            redirect('profile.php');
        } else {
            setMessage('Failed to update profile.', 'error');
        }
    }
}

// Get updated profile
$profile = getUserProfile($user_id);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - Medini Resume Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        /* Header Styles */
        header {
            background-color: var(--white);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            height: 70px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            text-decoration: none;
        }
        
        .logo i {
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--spacing-lg);
        }
        
        nav a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: var(--spacing-xs) 0;
            position: relative;
            font-size: 0.95rem;
        }
        
        nav a:hover {
            color: var(--primary);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary);
            transition: var(--transition);
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        .user-dropdown {
            position: relative;
            cursor: pointer;
        }
        
        .user-dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius);
            transition: var(--transition);
        }
        
        .user-dropdown-toggle:hover {
            background-color: var(--light);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-weight: 600;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            width: 200px;
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
            z-index: 100;
        }
        
        .user-dropdown.active .user-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-menu a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--dark);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .user-dropdown-menu a:hover {
            background-color: var(--light);
            color: var(--primary);
        }
        
        .user-dropdown-menu .divider {
            height: 1px;
            background-color: var(--light-gray);
            margin: 0.5rem 0;
        }
        
        .user-dropdown-menu .logout {
            color: var(--danger);
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            flex: 1;
            position: relative;
        }
        
        .sidebar {
            width: 260px;
            background-color: var(--white);
            border-right: 1px solid var(--light-gray);
            height: calc(100vh - 70px);
            position: sticky;
            top: 70px;
            overflow-y: auto;
            transition: var(--transition);
            z-index: 90;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar.collapsed .sidebar-toggle {
            right: -15px;
        }
        
        .user-info {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info {
            padding: var(--spacing-sm) 0;
        }
        
        .sidebar .user-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-sm);
            font-size: 1.5rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .user-info h3 {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .user-info p {
            color: var(--gray);
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info h3,
        .sidebar.collapsed .user-info p {
            opacity: 0;
            height: 0;
            margin: 0;
            overflow: hidden;
        }
        
        .sidebar-nav {
            padding: var(--spacing-md) 0;
        }
        
        .sidebar-nav ul {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem var(--spacing-md);
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .sidebar-nav a:hover {
            color: var(--primary);
            background-color: var(--primary-light);
        }
        
        .sidebar-nav li.active a {
            color: var(--primary);
            border-left-color: var(--primary);
            background-color: var(--primary-light);
            font-weight: 500;
        }
        
        .sidebar-nav i {
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .sidebar-nav a span {
            opacity: 0;
            width: 0;
            height: 0;
            overflow: hidden;
        }
        
        .sidebar-toggle {
            position: absolute;
            bottom: 20px;
            right: 2px;
            width: 30px;
            height: 30px;
            background-color: var(--primary);
            color: var(--white);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
            z-index: 10;
            transition: var(--transition);
        }
        
        .sidebar-toggle:hover {
            background-color: var(--primary-dark);
            transform: scale(1.1);
        }
        
        .dashboard-content {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .dashboard-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--dark);
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* Profile Form Styles */
        .profile-container {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }
        
        .profile-form {
            padding: var(--spacing-md);
        }
        
        .form-section {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .form-section h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--primary);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            margin-bottom: var(--spacing-md);
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--light-gray);
            border-radius: var(--radius);
            font-family: inherit;
            font-size: 0.95rem;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(45, 95, 139, 0.1);
            outline: none;
        }
        
        .form-group input:disabled {
            background-color: var(--light);
            cursor: not-allowed;
        }
        
        .field-hint {
            margin-top: 0.5rem;
            font-size: 0.85rem;
            color: var(--gray);
        }
        
        .field-hint a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .field-hint a:hover {
            text-decoration: underline;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: var(--spacing-md);
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.6rem 1.25rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.95rem;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        /* Alerts */
        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius);
            margin-bottom: var(--spacing-md);
            animation: fadeInUp 0.6s ease-out;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success);
            color: var(--success);
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger);
            color: var(--danger);
        }
        
        /* Mobile Sidebar Toggle */
        .mobile-sidebar-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--dark);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        /* Footer */
        footer {
            background-color: var(--white);
            border-top: 1px solid var(--light-gray);
            padding: var(--spacing-md) 0;
            text-align: center;
        }
        
        footer p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sections {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: fixed;
                left: -260px;
                height: 100vh;
                top: 0;
                z-index: 1000;
            }
            
            .sidebar.active {
                left: 0;
            }
            
            .sidebar-toggle {
                display: none;
            }
            
            .mobile-sidebar-toggle {
                display: block;
            }
            
            .dashboard-content {
                margin-left: 0;
                width: 100%;
            }
            
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }
            
            .overlay.active {
                opacity: 1;
                visibility: visible;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            nav ul {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">
                <i class="fas fa-file-alt"></i>
                <span>Medini</span>
            </a>
            
            <nav>
                <ul>
                    <li><a href="templates.html">Templates</a></li>
                    <li><a href="features.html">Features</a></li>
                    <li><a href="pricing.html">Pricing</a></li>
                    <li><a href="blog.html">Resources</a></li>
                </ul>
            </nav>
            
            <div class="user-dropdown">
                <div class="user-dropdown-toggle">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span><?php echo $profile['first_name'] ?></span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="user-dropdown-menu">
                    <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    <a href="my-resumes.php"><i class="fas fa-file"></i> My Resumes</a>
                    <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    <div class="divider"></div>
                    <a href="logout.php" class="logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </div>
            
            <button class="mobile-sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>
    
    <div class="overlay"></div>
    
    <main class="dashboard-container">
        <div class="sidebar">
            <div class="user-info">
                <div class="user-avatar">
                    <?php if (!empty($profile['profile_image'])): ?>
                        <img src="<?php echo UPLOAD_DIR . 'profiles/' . $profile['profile_image']; ?>" alt="Profile Image">
                    <?php else: ?>
                        <i class="fas fa-user"></i>
                    <?php endif; ?>
                </div>
                <h3><?php echo !empty($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : $_SESSION['username']; ?></h3>
                <p><?php echo !empty($profile['title']) ? $profile['title'] : 'Complete your profile'; ?></p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> <span>Dashboard</span></a></li>
                    <li class="active"><a href="profile.php"><i class="fas fa-user"></i> <span>My Profile</span></a></li>
                    <li><a href="skills.php"><i class="fas fa-star"></i> <span>Skills</span></a></li>
                    <li><a href="experience.php"><i class="fas fa-briefcase"></i> <span>Experience</span></a></li>
                    <li><a href="education.php"><i class="fas fa-graduation-cap"></i> <span>Education</span></a></li>
                    <li><a href="projects.php"><i class="fas fa-project-diagram"></i> <span>Projects</span></a></li>
                    <li><a href="certifications.php"><i class="fas fa-certificate"></i> <span>Certifications</span></a></li>
                    <li><a href="my-resumes.php"><i class="fas fa-file"></i> <span>My Resumes</span></a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> <span>Settings</span></a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> <span>Logout</span></a></li>
                </ul>
            </nav>
            
            <button class="sidebar-toggle">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>My Profile</h1>
            </div>
            
            <?php 
            // Display messages if any
            if (isset($_SESSION['message'])) {
                $message = $_SESSION['message'];
                $message_type = $_SESSION['message_type'];
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
                echo '<div class="alert alert-' . $message_type . '">' . $message . '</div>';
            }
            ?>
            
            <div class="profile-container">
                <form action="profile.php" method="POST" class="profile-form">
                    <div class="form-section">
                        <h2>Personal Information</h2>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first_name">First Name *</label>
                                <input type="text" id="first_name" name="first_name" value="<?php echo isset($profile['first_name']) ? $profile['first_name'] : ''; ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="last_name">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" value="<?php echo isset($profile['last_name']) ? $profile['last_name'] : ''; ?>" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="title">Professional Title</label>
                            <input type="text" id="title" name="title" value="<?php echo isset($profile['title']) ? $profile['title'] : ''; ?>" placeholder="e.g. Software Developer, Marketing Specialist">
                        </div>
                        
                        <div class="form-group">
                            <label for="summary">Professional Summary</label>
                            <textarea id="summary" name="summary" rows="4" placeholder="Write a brief summary of your professional background and key strengths"><?php echo isset($profile['summary']) ? $profile['summary'] : ''; ?></textarea>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h2>Contact Information</h2>
                        
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" value="<?php echo $_SESSION['email']; ?>" disabled>
                            <p class="field-hint">Email cannot be changed here. Go to <a href="settings.php">Settings</a> to update your email.</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" value="<?php echo isset($profile['phone']) ? $profile['phone'] : ''; ?>" placeholder="e.g. (*************">
                        </div>
                        
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" name="location" value="<?php echo isset($profile['address']) ? $profile['address'] : ''; ?>" placeholder="e.g. New York, NY">
                        </div>
                        
                        <div class="form-group">
                            <label for="website">Website/LinkedIn</label>
                            <input type="url" id="website" name="website" value="<?php echo isset($profile['website']) ? $profile['website'] : ''; ?>" placeholder="e.g. https://linkedin.com/in/yourprofile">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Save Profile</button>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <span id="current-year">2023</span> Medini Resume Builder. All rights reserved.</p>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update current year
            document.getElementById('current-year').textContent = new Date().getFullYear();
            
            // User dropdown toggle
            const userDropdown = document.querySelector('.user-dropdown');
            const userDropdownToggle = document.querySelector('.user-dropdown-toggle');
            
            if (userDropdownToggle) {
                userDropdownToggle.addEventListener('click', function() {
                    userDropdown.classList.toggle('active');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!userDropdown.contains(event.target)) {
                        userDropdown.classList.remove('active');
                    }
                });
            }
            
            // Sidebar toggle
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    
                    // Update toggle icon
                    const icon = this.querySelector('i');
                    if (sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-left');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-left');
                    }
                });
            }
            
            // Mobile sidebar toggle
            const mobileSidebarToggle = document.querySelector('.mobile-sidebar-toggle');
            const overlay = document.querySelector('.overlay');
            
            if (mobileSidebarToggle && sidebar && overlay) {
                mobileSidebarToggle.addEventListener('click', function() {
                    sidebar.classList.add('active');
                    overlay.classList.add('active');
                });
                
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }
            
            // Animate progress bar
            const progressBar = document.querySelector('.progress');
            if (progressBar) {
                setTimeout(() => {
                    progressBar.style.width = progressBar.parentElement.getAttribute('data-progress') || '75%';
                }, 300);
            }
            
            // Number counter animation
            const counters = document.querySelectorAll('.count-up');
            const speed = 200; // The lower the faster
            
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / speed;
                let count = 0;
                
                const updateCount = () => {
                    if (count < target) {
                        count += increment;
                        if (count > target) count = target;
                        counter.textContent = Math.floor(count);
                        requestAnimationFrame(updateCount);
                    }
                };
                
                updateCount();
            });
        });
    </script>
</body>
</html>