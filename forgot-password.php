<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = sanitize($_POST['email']);
    
    // Validate form data
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    // If no errors, process reset request
    if (empty($errors)) {
        // Check if user exists
        $db->query("SELECT * FROM users WHERE email = :email");
        $db->bind(':email', $email);
        $user = $db->single();
        
        if ($user) {
            // Generate reset token
            $reset_token = generateToken();
            $reset_expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Update user with reset token
            $db->query("UPDATE users SET reset_token = :reset_token, reset_expires = :reset_expires WHERE id = :id");
            $db->bind(':reset_token', $reset_token);
            $db->bind(':reset_expires', $reset_expires);
            $db->bind(':id', $user['id']);
            
            if ($db->execute()) {
                // Send reset email (in a real application)
                // sendResetEmail($email, $reset_token);
                
                $success = true;
            } else {
                $errors[] = 'Something went wrong. Please try again.';
            }
        } else {
            // Don't reveal that the email doesn't exist for security reasons
            $success = true;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="auth-container">
        <div class="auth-form-container">
            <h1>Forgot Password</h1>
            <p class="auth-subtitle">Enter your email address to reset your password</p>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <p>If your email exists in our system, you will receive a password reset link shortly.</p>
                </div>
            <?php else: ?>
                <form action="forgot-password.php" method="POST" class="auth-form">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-envelope"></i></span>
                            <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">Reset Password</button>
                    </div>
                    
                    <div class="auth-links">
                        <p>Remember your password? <a href="login.php">Login</a></p>
                    </div>
                </form>
            <?php endif; ?>
        </div>
        
        <div class="auth-image">
            <img src="images/forgot-password-image.jpg" alt="Forgot Password">
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
</body>
</html>