import React from "react";

const EducationItem = ({
  institution,
  degree,
  area,
  date,
  gpa,
  location,
  summary,
  containerStyle = "",
  institutionStyle = "",
  dateStyle = "",
  degreeStyle = "",
  areaStyle = "",
  gpaStyle = "",
  summaryStyle = "",
}) => (
  <div className={`mb-4 ${containerStyle}`}>
    <div className="flex justify-between">
      <strong className={`text-base ${institutionStyle}`}>{institution}</strong>
      <span className={`text-sm italic ${dateStyle}`}>{date}</span>
    </div>
    <div className={`text-sm italic ${degreeStyle}`}>
      {degree} {area && `in ${area}`}
    </div>
    {gpa && <div className={`text-sm ${gpaStyle}`}>GPA: {gpa}</div>}
    {location && <div className="text-sm text-gray-500">{location}</div>}
    {summary && (
      <div
        className={`text-sm mt-1 ${summaryStyle}`}
        dangerouslySetInnerHTML={{ __html: summary }}
      />
    )}
  </div>
);

export default EducationItem;
