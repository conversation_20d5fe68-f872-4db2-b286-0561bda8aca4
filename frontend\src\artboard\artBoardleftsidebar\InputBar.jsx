import React from "react";

const InputBar = ({
  label = "",
  value = "",
  onChange,
  placeholder = "",
  icon = null,
  disabled = false,
  type = "text",
  className = "",
  inputClassName = "",
  style = {},
  inputStyle = {},
  ...props
}) => {
  return (
    <div className={`w-full mb-4 ${className}`} style={style}>
      {label && (
        <label className="block text-sm font-semibold text-white mb-1">
          {label}
        </label>
      )}
      <div
        className={`flex items-center bg-[#18191A] border border-[#23262b] rounded-md px-3 h-11 transition-opacity ${
          disabled ? "opacity-60" : "opacity-100"
        }`}
      >
        <input
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`flex-1 bg-transparent border-none outline-none text-white placeholder-gray-400 text-base py-2 ${inputClassName}`}
          style={inputStyle}
          {...props}
        />
        {icon && (
          <span className="ml-2 text-gray-400 text-lg flex items-center">
            {icon}
          </span>
        )}
      </div>
    </div>
  );
};

export default InputBar;
