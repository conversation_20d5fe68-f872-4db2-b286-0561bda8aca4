// Resume Editor functionality

document.addEventListener("DOMContentLoaded", () => {
    // Get resume name from URL if available
    const urlParams = new URLSearchParams(window.location.search)
    const resumeName = urlParams.get("resume")
    const templateName = urlParams.get("template")
  
    const documentNameEl = document.querySelector(".document-name")
  
    if (resumeName && documentNameEl) {
      documentNameEl.textContent = decodeURIComponent(resumeName)
    } else if (templateName && documentNameEl) {
      // Format template name (e.g., "professional-1" -> "Professional Resume")
      const formattedName = templateName
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
  
      documentNameEl.textContent = formattedName + " Resume"
    }
  
    // Section navigation
    const sectionItems = document.querySelectorAll(".section-item")
    const resumeSections = document.querySelectorAll(".resume-section")
  
    if (sectionItems && resumeSections) {
      sectionItems.forEach((item) => {
        item.addEventListener("click", function () {
          const sectionId = this.getAttribute("data-section")
          const targetSection = document.getElementById(sectionId + "-section")
  
          // Update active section in sidebar
          document.querySelector(".section-item.active").classList.remove("active")
          this.classList.add("active")
  
          // Update active section in resume
          document.querySelector(".resume-section.active").classList.remove("active")
          targetSection.classList.add("active")
  
          // Scroll to section
          targetSection.scrollIntoView({ behavior: "smooth", block: "center" })
        })
      })
    }
  
    // Section settings panel
    const section = null
  })
  