import React from "react";

const Summary = ({
  title = "Summary",
  content = "",
  sectionStyle = "",
  titleStyle = "",
  contentStyle = "",
}) => {
  if (!content) return null;

  return (
    <section className={sectionStyle}>
      {title && (
        <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      )}
      <div
        className={`text-sm leading-relaxed text-gray-800 ${contentStyle}`}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </section>
  );
};

export default Summary;
