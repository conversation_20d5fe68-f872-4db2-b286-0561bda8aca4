import React, { useEffect } from 'react';
import { useAdminStore } from '../store/useAdminStore';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const AdminDashboard = () => {
  const { users, fetchAllUsers } = useAdminStore();

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(
      users.map(({ name, email, role }, index) => ({
        '#': index + 1,
        Name: name,
        Email: email,
        Role: role || 'user',
      }))
    );
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(data, 'users.xlsx');
  };

  const exportToPDF = () => {
    const doc = new jsPDF();
    doc.text('User List', 14, 15);
    const tableColumn = ['#', 'Name', 'Email', 'Role'];
    const tableRows = users.map((u, i) => [i + 1, u.name, u.email, u.role || 'user']);

    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 20,
      theme: 'striped',
      styles: {
        halign: 'center',
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [255, 194, 80],
        textColor: [41, 53, 77],
      },
    });

    doc.save('users.pdf');
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4 mt-20">
        <h1 className="text-2xl font-bold text-[#29354d]">User List</h1>
        <div className="space-x-2">
          <button
            onClick={exportToExcel}
            className="px-4 py-2 bg-[#fcc250] text-[#29354d] font-semibold rounded shadow hover:bg-yellow-400"
          >
            Download Excel
          </button>
          {/* <button
            onClick={exportToPDF}
            className="px-4 py-2 bg-[#29354d] text-white font-semibold rounded shadow hover:bg-[#1c263a]"
          >
            Download PDF
          </button> */}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border rounded shadow-md">
          <thead className="bg-[#fcc250] text-[#29354d]">
            <tr>
              <th className="px-4 py-2 border">#</th>
              <th className="px-4 py-2 border">Name</th>
              <th className="px-4 py-2 border">Email</th>
              <th className="px-4 py-2 border">Role</th>
            </tr>
          </thead>
          <tbody>
            {users && users.length > 0 ? (
              users.map((user, index) => (
                <tr key={user._id || index} className="text-center hover:bg-gray-100">
                  <td className="px-4 py-2 border">{index + 1}</td>
                  <td className="px-4 py-2 border">{user.name}</td>
                  <td className="px-4 py-2 border">{user.email}</td>
                  <td className="px-4 py-2 border capitalize">{user.role || 'user'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="4" className="text-center py-4 text-gray-500">
                  No users found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AdminDashboard;
