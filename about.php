<?php
    session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About | Medini Resume Builder</title>
    <style>
        :root {
            --primary-color: rgb(25, 65, 85);
            --primary-light: rgba(25, 65, 75, 0.1);
            --primary-medium: rgba(25, 65, 75, 0.5);
            --accent-color: white;
            --text-light: #f5f5f5;
            --text-dark: #333;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 2rem;
            --spacing-lg: 4rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f9f9f9;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-sm);
        }

        .hero {
            background: linear-gradient(135deg, rgb(11,12,14),rgb(2, 5, 8));
            color: var(--text-light);
            padding: calc(var(--spacing-lg) * 2) 0 var(--spacing-lg);
            margin-top: 60px;
            position: relative;
            border-radius: 20px;
            font-family: 'sans-serif';
            overflow: hidden;
            width:80%;
            margin: 20px auto;
        }

        .hero:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/api/placeholder/600/400') center/cover;
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h2 {
            font-size: 2.5rem;
            margin-bottom: var(--spacing-sm);
            font-weight: 700;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: var(--spacing-md);
            opacity: 0.9;
        }

        .about-section {
            padding: var(--spacing-lg) 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: var(--spacing-md);
            position: relative;
            padding-bottom: var(--spacing-sm);
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--primary-color);
        }

        .section-title h3 {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .about-content {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .about-text {
            flex: 1;
        }

        .about-text p {
            margin-bottom: var(--spacing-sm);
        }

        .about-image {
            flex: 1;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }


        .testimonials {
            padding: var(--spacing-lg) 0;
            background-color: var(--primary-light);
        }

        .testimonial-card {
            background: white;
            border-radius: 10px;
            padding: var(--spacing-md);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin: 0 auto;
            max-width: 800px;
            position: relative;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: var(--spacing-sm);
            position: relative;
            padding-left: var(--spacing-md);
        }

        .testimonial-text:before {
            content: '"';
            font-size: 3rem;
            position: absolute;
            left: -10px;
            top: -20px;
            color: var(--primary-medium);
            font-family: serif;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .author-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: var(--spacing-sm);
        }

        .author-info h5 {
            color: var(--primary-color);
            margin-bottom: 2px;
        }

        .author-info p {
            color: #777;
            font-size: 0.9rem;
        }

        
        @media (max-width: 768px) {
            .about-content {
                flex-direction: column;
            }

            .hero h2 {
                font-size: 2rem;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <?php include "includes/header.php" ?>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>About Medini Resume Builder</h2>
                <p>Crafting professional resumes that stand out in today's competitive job market. Our mission is to help you land your dream job with powerful, ATS-optimized resumes.</p>
            </div>
        </div>
    </section>

    <section class="about-section">
        <div class="container">
            <div class="section-title">
                <h3>Our Story</h3>
                <p>A journey of passion and innovation</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <p>Founded in 2021, Medini Resume Builder was born from a simple observation: creating impressive, professional resumes shouldn't be complicated or expensive. Our founder, after struggling with outdated resume builders and costly professional services during her job search, decided there had to be a better way.</p>
                    <p>What started as a small project quickly grew into a comprehensive platform that has now helped over 500,000 professionals worldwide land interviews at top companies. Our team combines expertise in HR, design, and technology to create a resume-building experience that truly works.</p>
                    <p>At Medini, we believe your resume should be as unique as your career journey. That's why we offer customizable templates that maintain professional standards while allowing your personality to shine through.</p>
                </div>
                <div class="about-image">
                    <img src="images/about.jpg" alt="Team working at Medini" style="width: 100%; height: 100%; object-fit: cover;">
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials">
        <div class="container">
            <div class="section-title">
                <h3>What Our Users Say</h3>
                <p>Success stories from Medini users</p>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-text">
                    <p>After using Medini Resume Builder, I received interview calls from 4 out of 6 companies I applied to, including my dream company. The AI suggestions helped me highlight achievements I would have otherwise overlooked. This is by far the best resume builder I've ever used!</p>
                </div>
                <div class="testimonial-author">
                    <div class="author-image">
                        <img src="/api/placeholder/100/100" alt="Testimonial Author" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="author-info">
                        <h5>Michael Torres</h5>
                        <p>Software Engineer at TechCorp</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php include "includes/footer.php" ?>
</body>
</html>