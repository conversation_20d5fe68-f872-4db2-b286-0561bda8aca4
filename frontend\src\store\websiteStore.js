import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS } from "../lib/constants";

export const useWebsiteStore = create((set) => ({
    website: null,
    isLoading: false,
    error: null,
    addWebsite: async (data) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.WEBSITE, data, {
                withCredentials: true,
            });
            set({ website: res.data.website, isLoading: false });
        } catch (err) {
            set({ error: "Failed to add website", isLoading: false });
        }
    },
}));
