<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];

// Get resume ID from URL
$resume_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Check if resume exists and belongs to user
$db->query("SELECT r.*, t.template_name, t.template_file 
            FROM user_resumes r 
            JOIN resume_templates t ON r.template_id = t.id 
            WHERE r.id = :id AND r.user_id = :user_id");
$db->bind(':id', $resume_id);
$db->bind(':user_id', $user_id);
$resume = $db->single();

if (!$resume) {
    setMessage('Resume not found.', 'error');
    redirect('my-resumes.php');
}

// Parse resume data
$resume_data = json_decode($resume['resume_data'], true);

// Set up PDF generation
require_once 'vendor/autoload.php'; // Assuming you have Dompdf installed via Composer

use Dompdf\Dompdf;
use Dompdf\Options;

// Initialize dompdf
$options = new Options();
$options->set('isHtml5ParserEnabled', true);
$options->set('isRemoteEnabled', true);
$options->set('defaultFont', 'Helvetica');

$dompdf = new Dompdf($options);

// Start output buffering to capture HTML
ob_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?php echo $resume['resume_name']; ?></title>
    <style>
        <?php include 'templates/' . $resume['template_file'] . '/pdf-style.css'; ?>
    </style>
</head>
<body>
    <?php include 'templates/' . $resume['template_file'] . '/pdf.php'; ?>
</body>
</html>
<?php
$html = ob_get_clean();

// Load HTML into dompdf
$dompdf->loadHtml($html);

// Set paper size and orientation
$dompdf->setPaper('A4', 'portrait');

// Render PDF
$dompdf->render();

// Output PDF
$dompdf->stream($resume['resume_name'] . '.pdf', array('Attachment' => true));
exit;
?>

