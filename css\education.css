/* Education Container Styles */
.education-container {
    padding: 20px 0;
}

/* Timeline Styles */
.education-timeline {
    position: relative;
    margin: 30px 0;
}

.education-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 16px;
    width: 4px;
    background-color: #e0e0e0;
    border-radius: 4px;
}

/* Education Item Styles */
.education-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 45px;
}

.education-marker {
    position: absolute;
    left: 10px;
    top: 6px;
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: 50%;
    z-index: 1;
}

.education-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* Education Header Styles */
.education-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.education-header h2 {
    margin: 0 0 5px;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.education-institution {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.education-field {
    font-style: italic;
    color: #666;
    margin-bottom: 5px;
}

.education-period {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.education-location, .education-gpa {
    color: #666;
    font-size: 0.9rem;
    margin-top: 8px;
}

.education-location i, .education-gpa i {
    margin-right: 5px;
    color: var(--primary-color);
}

/* Education Description and Achievements Styles */
.education-description h3, .education-achievements h3 {
    font-size: 1rem;
    margin: 15px 0 8px;
    color: #333;
}

.education-description p, .education-achievements p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* Education Actions Styles */
.education-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.delete-education-form {
    margin: 0;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: #333;
}

.empty-state p {
    margin: 0 0 20px;
    color: #666;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.close {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #f0f0f0;
}

.modal-body {
    padding: 20px;
}

/* Form Styles */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.form-check label {
    margin-bottom: 0;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .education-actions {
        flex-direction: column;
    }
    
    .education-actions button {
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}