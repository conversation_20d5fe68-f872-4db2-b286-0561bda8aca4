/* Templates Page Styles */
.templates-hero {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    padding: 4rem 0;
    color: white;
    text-align: center;
  }
  
  .templates-hero-content {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .templates-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .templates-hero p {
    font-size: 1.125rem;
    opacity: 0.9;
  }
  
  .templates-filters {
    background-color: var(--background);
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 70px;
    z-index: 10;
  }
  
  .filters-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .search-filter {
    display: flex;
    align-items: center;
    background-color: var(--background-alt);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    width: 300px;
  }
  
  .search-filter i {
    color: var(--text-light);
    margin-right: 0.5rem;
  }
  
  .search-filter input {
    background: none;
    border: none;
    outline: none;
    width: 100%;
    font-size: 0.875rem;
  }
  
  .category-filters {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .filter-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .filter-btn:hover {
    background-color: var(--background-alt);
  }
  
  .filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .sort-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .sort-filter label {
    font-size: 0.875rem;
    color: var(--text-light);
  }
  
  .sort-filter select {
    background-color: var(--background-alt);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    font-size: 0.875rem;
    outline: none;
    cursor: pointer;
  }
  
  .templates-grid-section {
    padding: 3rem 0;
    background-color: var(--background-alt);
  }
  
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }
  
  .template-card {
    background-color: var(--background);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
  }
  
  .template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .template-image {
    position: relative;
    height: 380px;
    overflow: hidden;
  }
  
  .template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
  }
  
  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: var(--transition);
  }
  
  .template-card:hover .template-overlay {
    opacity: 1;
  }
  
  .template-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    z-index: 1;
  }
  
  .template-info {
    padding: 1.5rem;
  }
  
  .template-info h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }
  
  .template-info p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
  
  .template-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-light);
  }
  
  .template-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .templates-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: var(--background);
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .pagination-btn:hover {
    background-color: var(--background-alt);
  }
  
  .pagination-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }
  
  .templates-cta {
    background-color: var(--primary-color);
    color: white;
    padding: 4rem 0;
  }
  
  .cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .cta-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  .cta-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  
  .cta-content .btn {
    background-color: white;
    color: var(--primary-color);
  }
  
  .cta-content .btn:hover {
    background-color: var(--background-alt);
  }
  
  /* Responsive Styles */
  @media (max-width: 992px) {
    .templates-hero h1 {
      font-size: 2rem;
    }
  
    .filters-container {
      flex-direction: column;
      align-items: stretch;
    }
  
    .search-filter {
      width: 100%;
    }
  
    .category-filters {
      justify-content: center;
    }
  
    .sort-filter {
      justify-content: center;
    }
  }
  
  @media (max-width: 768px) {
    .templates-hero {
      padding: 3rem 0;
    }
  
    .templates-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 1.5rem;
    }
  
    .template-image {
      height: 300px;
    }
  }
  
  @media (max-width: 576px) {
    .templates-hero h1 {
      font-size: 1.75rem;
    }
  
    .templates-grid {
      grid-template-columns: 1fr;
      max-width: 300px;
      margin: 0 auto 2rem;
    }
  
    .templates-filters {
      padding: 1rem 0;
    }
  
    .filter-btn {
      font-size: 0.75rem;
      padding: 0.4rem 0.75rem;
    }
  }
  