document.addEventListener('DOMContentLoaded', function() {
    // Modal functionality
    const modal = document.getElementById('add-skill-modal');
    const addBtn = document.getElementById('add-skill-btn');
    const emptyAddBtn = document.getElementById('empty-add-skill-btn');
    const closeBtn = modal.querySelector('.close');
    
    function openModal(e) {
        // Prevent any default behavior
        if (e) e.preventDefault();
        modal.style.display = 'block';
        // Debug
        console.log('Modal opened');
    }
    
    function closeModal(e) {
        // If called from an event handler, prevent default
        if (e) e.preventDefault();
        modal.style.display = 'none';
        // Debug
        console.log('Modal closed');
    }
    
    // Add event listeners with proper error handling
    if (addBtn) {
        addBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Stop click from bubbling up
            openModal();
        });
        console.log('Add button listener attached');
    }
    
    if (emptyAddBtn) {
        emptyAddBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Stop click from bubbling up
            openModal();
        });
        console.log('Empty add button listener attached');
    }
    
    closeBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation(); // Stop click from bubbling up
        closeModal();
    });
    
    // Stop propagation on modal content more explicitly
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
        modalContent.addEventListener('click', function(e) {
            e.stopPropagation(); // This prevents the click from reaching the window click handler
        });
    }
    
    // Only close when clicking outside the modal content
    window.addEventListener('click', function(event) {
        // Make sure we're not accidentally closing when clicking inside
        if (event.target === modal) {
            closeModal();
        }
    });
    
    // Fix form submission to prevent automatic closing
    const addForm = document.getElementById('add-skill-form');
    if (addForm) {
        // Prevent the form from submitting normally if there are validation issues
        addForm.addEventListener('submit', function(e) {
            // Let the form submit normally, but prevent any click events from bubbling up
            e.stopPropagation();
        });
        
        const formStars = addForm.querySelectorAll('.proficiency-stars .star');
        const proficiencyInput = document.getElementById('proficiency');
        
        formStars.forEach(star => {
            star.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation(); // Stop click from bubbling up
                const value = this.getAttribute('data-value');
                const stars = this.parentElement.querySelectorAll('.star');
                
                stars.forEach(s => {
                    if (parseInt(s.getAttribute('data-value')) <= parseInt(value)) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                
                if (proficiencyInput) {
                    proficiencyInput.value = value;
                }
            });
        });
    }
    
    // Badge upload toggle - add proper event handling
    const hasBadgeCheckbox = document.getElementById('has_badge');
    const badgeUploadDiv = document.querySelector('.badge-upload');
    
    if (hasBadgeCheckbox && badgeUploadDiv) {
        hasBadgeCheckbox.addEventListener('change', function(e) {
            e.stopPropagation(); // Stop event from bubbling up
            badgeUploadDiv.style.display = this.checked ? 'block' : 'none';
        });
    }
    
    // Update proficiency in skill cards with proper event handling
    const skillCards = document.querySelectorAll('.skill-card');
    const updateForm = document.getElementById('update-skill-form');
    const updateSkillId = document.getElementById('update_skill_id');
    const updateProficiency = document.getElementById('update_proficiency');
    
    skillCards.forEach(card => {
        const stars = card.querySelectorAll('.proficiency-stars .star');
        
        stars.forEach(star => {
            star.addEventListener('click', function(e) {
                e.stopPropagation(); // Stop event from bubbling up
                const skillId = card.getAttribute('data-id');
                const value = this.getAttribute('data-value');
                
                updateSkillId.value = skillId;
                updateProficiency.value = value;
                
                // Submit the form
                updateForm.submit();
            });
        });
    });
});