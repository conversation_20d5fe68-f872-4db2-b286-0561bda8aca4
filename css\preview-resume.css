/* Resume Preview Styles - Enhanced Version */
body.preview-mode {
    background-color: #f5f7fa;
    padding-top: 70px;
  }
  
  .preview-controls {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    padding: 1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    transition: all 0.3s ease;
  }
  
  .preview-controls.scrolled {
    padding: 0.5rem 0;
  }
  
  .preview-info {
    display: flex;
    align-items: center;
  }
  
  .preview-info h1 {
    margin-bottom: 0;
    font-size: 1.5rem;
    margin-right: 1rem;
  }
  
  .preview-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .preview-actions {
    display: flex;
    gap: 1rem;
  }
  
  .preview-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  
  .preview-action-btn i {
    margin-right: 0.5rem;
  }
  
  .preview-action-btn.primary {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 14px rgba(67, 97, 238, 0.3);
  }
  
  .preview-action-btn.primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
  }
  
  .preview-action-btn.secondary {
    background-color: white;
    color: var(--dark);
    border: 1px solid #ddd;
  }
  
  .preview-action-btn.secondary:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.05);
  }
  
  .resume-preview-container {
    max-width: 850px;
    margin: 2rem auto 4rem;
    position: relative;
  }
  
  .resume-preview-wrapper {
    background-color: white;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    transform-origin: top center;
  }
  
  .resume-preview-wrapper:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  }
  
  .resume-preview {
    padding: 3rem;
    transition: all 0.3s ease;
  }
  
  .resume-preview.scale-75 {
    transform: scale(0.75);
  }
  
  .resume-preview.scale-50 {
    transform: scale(0.5);
  }
  
  .resume-preview.scale-100 {
    transform: scale(1);
  }
  
  .preview-toolbar {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: var(--border-radius);
  }
  
  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: white;
    color: var(--dark);
    border: 1px solid #ddd;
  }
  
  .toolbar-btn i {
    margin-right: 0.5rem;
  }
  
  .toolbar-btn:hover {
    background-color: #f0f0f0;
  }
  
  .toolbar-btn.active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
  }
  
  .zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .zoom-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: white;
    color: var(--dark);
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .zoom-btn:hover {
    background-color: #f0f0f0;
  }
  
  .zoom-level {
    font-weight: 500;
    font-size: 0.875rem;
    min-width: 60px;
    text-align: center;
  }
  
  .preview-footer {
    text-align: center;
    padding: 2rem 0;
    color: var(--gray);
  }
  
  .preview-footer a {
    color: var(--primary);
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .preview-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }
  
  /* Resume Template Styles - Enhanced */
  .resume-template {
    font-family: 'Inter', sans-serif;
    color: #333;
    line-height: 1.6;
    position: relative;
  }
  
  .resume-template.serif {
    font-family: 'Georgia', serif;
  }
  
  .resume-template.sans-serif {
    font-family: 'Inter', sans-serif;
  }
  
  .resume-template.monospace {
    font-family: 'JetBrains Mono', monospace;
  }
  
  .resume-header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
  }
  
  .resume-header::after {
    content: '';
    position: absolute;
    bottom: -1.25rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background-color: var(--primary);
    border-radius: 1px;
  }
  
  .resume-name {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: var(--primary);
    letter-spacing: -0.02em;
  }
  
  .resume-title {
    font-size: 1.25rem;
    color: var(--gray);
    margin-bottom: 1.25rem;
    font-weight: 500;
  }
  
  .resume-contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
  }
  
  .resume-contact-item {
    display: flex;
    align-items: center;
  }
  
  .resume-contact-item i {
    margin-right: 0.5rem;
    color: var(--primary);
  }
  
  .resume-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 1.5rem;
    border: 3px solid var(--primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .resume-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .resume-section {
    margin-bottom: 2.5rem;
    position: relative;
  }
  
  .resume-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    color: var(--primary);
    border-bottom: 2px solid var(--primary);
    padding-bottom: 0.5rem;
    display: inline-block;
  }
  
  .resume-objective {
    margin-bottom: 2.5rem;
    font-style: italic;
    color: var(--gray);
    line-height: 1.7;
    padding: 1.5rem;
    background-color: rgba(67, 97, 238, 0.05);
    border-radius: var(--border-radius);
    position: relative;
  }
  
  .resume-objective::before {
    content: '\201C';
    position: absolute;
    top: 0;
    left: 0.5rem;
    font-size: 3rem;
    color: rgba(67, 97, 238, 0.2);
    font-family: Georgia, serif;
    line-height: 1;
  }
  
  .resume-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .skill-badge {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    border-radius: 3px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .skill-badge:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-2px);
  }
  
  .experience-item,
  .education-item,
  .project-item,
  .certification-item {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
  }
  
  .experience-item:last-child,
  .education-item:last-child,
  .project-item:last-child,
  .certification-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .item-header {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
  }
  
  .item-title {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: var(--dark);
  }
  
  .item-subtitle {
    font-weight: 500;
    color: var(--gray);
    margin-bottom: 0.25rem;
  }
  
  .item-period {
    font-size: 0.875rem;
    color: var(--gray);
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-weight: 500;
  }
  
  .item-description {
    font-size: 0.9rem;
    line-height: 1.7;
    color: var(--gray-dark);
  }
  
  .item-description ul {
    padding-left: 1.5rem;
    margin-top: 0.75rem;
  }
  
  .item-description li {
    margin-bottom: 0.5rem;
    position: relative;
  }
  
  .item-description li::before {
    content: '•';
    position: absolute;
    left: -1rem;
    color: var(--primary);
    font-weight: bold;
  }
  
  /* Color Schemes */
  .resume-template.blue .resume-name,
  .resume-template.blue .resume-section-title,
  .resume-template.blue .resume-contact-item i {
    color: var(--blue);
  }
  
  .resume-template.blue .resume-header::after,
  .resume-template.blue .resume-section-title {
    border-color: var(--blue);
  }
  
  .resume-template.blue .resume-photo {
    border-color: var(--blue);
  }
  
  .resume-template.blue .skill-badge {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--blue);
  }
  
  .resume-template.blue .skill-badge:hover {
    background-color: var(--blue);
    color: white;
  }
  
  .resume-template.blue .resume-objective {
    background-color: rgba(67, 97, 238, 0.05);
  }
  
  .resume-template.blue .resume-objective::before {
    color: rgba(67, 97, 238, 0.2);
  }
  
  .resume-template.blue .item-description li::before {
    color: var(--blue);
  }
  
  .resume-template.green .resume-name,
  .resume-template.green .resume-section-title,
  .resume-template.green .resume-contact-item i {
    color: var(--green);
  }
  
  .resume-template.green .resume-header::after,
  .resume-template.green .resume-section-title {
    border-color: var(--green);
  }
  
  .resume-template.green .resume-photo {
    border-color: var(--green);
  }
  
  .resume-template.green .skill-badge {
    background-color: rgba(56, 176, 0, 0.1);
    color: var(--green);
  }
  
  .resume-template.green .skill-badge:hover {
    background-color: var(--green);
    color: white;
  }
  
  .resume-template.green .resume-objective {
    background-color: rgba(56, 176, 0, 0.05);
  }
  
  .resume-template.green .resume-objective::before {
    color: rgba(56, 176, 0, 0.2);
  }
  
  .resume-template.green .item-description li::before {
    color: var(--green);
  }
  
  .resume-template.purple .resume-name,
  .resume-template.purple .resume-section-title,
  .resume-template.purple .resume-contact-item i {
    color: var(--purple);
  }
  
  .resume-template.purple .resume-header::after,
  .resume-template.purple .resume-section-title {
    border-color: var(--purple);
  }
  
  .resume-template.purple .resume-photo {
    border-color: var(--purple);
  }
  
  .resume-template.purple .skill-badge {
    background-color: rgba(114, 9, 183, 0.1);
    color: var(--purple);
  }
  
  .resume-template.purple .skill-badge:hover {
    background-color: var(--purple);
    color: white;
  }
  
  .resume-template.purple .resume-objective {
    background-color: rgba(114, 9, 183, 0.05);
  }
  
  .resume-template.purple .resume-objective::before {
    color: rgba(114, 9, 183, 0.2);
  }
  
  .resume-template.purple .item-description li::before {
    color: var(--purple);
  }
  
  @media print {
    body {
      background-color: white;
    }
    
    .preview-controls,
    .preview-toolbar,
    .preview-footer {
      display: none;
    }
    
    .resume-preview-container {
      margin: 0;
      max-width: 100%;
    }
    
    .resume-preview-wrapper {
      box-shadow: none;
      border-radius: 0;
    }
    
    .resume-preview {
      padding: 0;
      transform: scale(1) !important;
    }
  }
  
  @media (max-width: 992px) {
    .preview-controls .container {
      flex-direction: column;
      gap: 1rem;
    }
    
    .preview-info {
      justify-content: center;
    }
    
    .preview-actions {
      justify-content: center;
    }
    
    .resume-preview {
      padding: 2rem;
    }
  }
  
  @media (max-width: 768px) {
    body.preview-mode {
      padding-top: 120px;
    }
    
    .preview-toolbar {
      flex-wrap: wrap;
    }
    
    .resume-preview {
      padding: 1.5rem;
    }
    
    .resume-name {
      font-size: 2rem;
    }
    
    .resume-title {
      font-size: 1.1rem;
    }
    
    .resume-contact {
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
    }
  }
  
  @media (max-width: 576px) {
    body.preview-mode {
      padding-top: 170px;
    }
    
    .preview-actions {
      flex-direction: column;
      width: 100%;
    }
    
    .preview-action-btn {
      width: 100%;
    }
    
    .resume-preview {
      padding: 1rem;
    }
    
    .resume-name {
      font-size: 1.75rem;
    }
    
    .item-header {
      flex-direction: column;
    }
    
    .item-period {
      margin-top: 0.5rem;
    }
  }