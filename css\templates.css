:root {
  --primary: rgb(25, 65, 75);
  --primary-light: rgba(25, 65, 75, 0.1);
  --primary-medium: rgba(25, 65, 75, 0.2);
  --primary-dark: rgb(20, 52, 60);
  --primary-gradient: linear-gradient(135deg, rgb(25, 65, 75), rgb(20, 55, 70));
  --accent: rgb(255, 170, 60);
  --text-dark: #333;
  --text-medium: #555;
  --text-light: #777;
  --white: #fff;
  --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 15px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.12);
  --transition: all 0.3s ease;
  --border-radius-sm: 6px;
  --border-radius-md: 10px;
  --border-radius-lg: 15px;
}

/* Templates Container */
.templates-container {
  display: flex;
  gap: 2.5rem;
  margin-top: 1rem;
}

/* Filters Panel */
.templates-filters {
  width: 280px;
  flex-shrink: 0;
  position: sticky;
  top: 100px;
  height: fit-content;
}

.filter-group {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 1.75rem;
  margin-bottom: 1.75rem;
  border: 1px solid rgba(25, 65, 75, 0.05);
  transition: var(--transition);
}

.filter-group:hover {
  box-shadow: var(--shadow-md);
}

.filter-group h3 {
  font-size: 1.1rem;
  margin-bottom: 1.25rem;
  color: var(--primary);
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--primary-light);
  position: relative;
}

.filter-group h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--primary);
}

.category-filters {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-filters li {
  margin-bottom: 0.5rem;
}

.filter-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--text-medium);
  transition: var(--transition);
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.filter-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--primary);
  opacity: 0;
  transition: var(--transition);
}

.filter-btn:hover {
  color: var(--primary);
  background-color: var(--primary-light);
}

.filter-btn:hover::before {
  opacity: 0.5;
}

.filter-btn.active {
  color: var(--primary);
  font-weight: 600;
  background-color: var(--primary-light);
  padding-left: 1rem;
}

.filter-btn.active::before {
  opacity: 1;
}

.feature-filters {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-filter {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.feature-filter:hover {
  background-color: var(--primary-light);
}

.feature-filter input {
  margin-right: 0.75rem;
  cursor: pointer;
  accent-color: var(--primary);
  width: 18px;
  height: 18px;
}

.feature-filter span {
  color: var(--text-medium);
  font-size: 0.95rem;
}

/* Templates Grid */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  flex: 1;
}

/* Template Card */
.template-card {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid rgba(25, 65, 75, 0.05);
  position: relative;
}

.template-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.template-preview {
  position: relative;
  height: 380px;
  overflow: hidden;
}

.template-preview::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  z-index: 1;
}

.template-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.template-card:hover .template-preview img {
  transform: scale(1.05);
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(25, 65, 75, 0.85);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.25rem;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 2;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-info {
  padding: 1.5rem;
  border-top: 1px solid rgba(25, 65, 75, 0.05);
}

.template-info h3 {
  font-size: 1.15rem;
  margin-bottom: 0.75rem;
  color: var(--primary);
  font-weight: 600;
}

.template-info p {
  font-size: 0.925rem;
  color: var(--text-light);
  margin-bottom: 1.25rem;
  line-height: 1.6;
}

.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.template-tag {
  background-color: var(--primary-light);
  color: var(--primary);
  font-size: 0.8rem;
  padding: 0.35rem 0.85rem;
  border-radius: 20px;
  font-weight: 500;
  transition: var(--transition);
}

.template-tag:hover {
  background-color: var(--primary-medium);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  border-radius: 30px;
  transition: all 0.3s ease;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-size: 0.95rem;
  border: none;
  text-decoration: none;
}

.btn-sm {
  padding: 0.65rem 1.25rem;
  font-size: 0.875rem;
  border-radius: 25px;
}

.btn i {
  font-size: 1rem;
}

.btn-outline {
  background-color: transparent;
  color: var(--white);
  border: 2px solid var(--white);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--white);
  z-index: -1;
  transition: width 0.3s ease;
}

.btn-outline:hover {
  color: var(--primary);
}

.btn-outline:hover::before {
  width: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(25, 65, 75, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: -1;
  transition: width 0.3s ease;
}

.btn-primary:hover {
  box-shadow: 0 6px 20px rgba(25, 65, 75, 0.4);
  transform: translateY(-2px);
}

.btn-primary:hover::before {
  width: 100%;
}

.btn-primary.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}

.btn-primary.disabled:hover {
  transform: none;
}

.btn-primary.disabled::before {
  display: none;
}

/* Category Badge */
.template-card::before {
  content: attr(data-category);
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: var(--primary);
  color: var(--white);
  padding: 0.35rem 0.85rem;
  font-size: 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  z-index: 3;
  opacity: 0.9;
}

/* Template Status Indicator */
.template-card::after {
  content: '';
  position: absolute;
  top: 1rem;
  left: 1rem;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--accent);
  z-index: 3;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.5);
}

/* Animation effects */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 65, 75, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 65, 75, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 65, 75, 0);
  }
}

/* Empty State */
.empty-templates {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 3rem;
  text-align: center;
  margin: 2rem auto;
  max-width: 600px;
  box-shadow: var(--shadow-md);
}

.empty-templates-icon {
  font-size: 4rem;
  color: var(--primary-light);
  margin-bottom: 1.5rem;
}

.empty-templates h3 {
  color: var(--primary);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.empty-templates p {
  color: var(--text-light);
  margin-bottom: 2rem;
}

/* Alert styles */
.alert {
  border-radius: var(--border-radius-md);
  padding: 1.25rem;
  margin-bottom: 2rem;
  border-left: 4px solid;
}

.alert-warning {
  background-color: rgba(255, 170, 60, 0.1);
  border-color: var(--accent);
}

.alert p {
  margin-bottom: 0.75rem;
}

.alert ul {
  margin-left: 1.5rem;
}

.alert a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.alert a:hover {
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 992px) {
  .templates-container {
    flex-direction: column;
  }

  .templates-filters {
    width: 100%;
    position: static;
  }

  .filter-group {
    margin-bottom: 1.5rem;
  }
  
  /* Convert filter groups to horizontal scrollable tabs */
  .category-filters {
    display: flex;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
    scrollbar-width: thin;
  }
  
  .category-filters::-webkit-scrollbar {
    height: 4px;
  }
  
  .category-filters li {
    flex-shrink: 0;
    margin-right: 0.5rem;
    margin-bottom: 0;
  }
  
  .filter-btn {
    white-space: nowrap;
    padding: 0.5rem 1.25rem;
  }
}

@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
  }
  
  .template-preview {
    height: 320px;
  }
  
  .template-info {
    padding: 1.25rem;
  }
}

@media (max-width: 576px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .template-preview {
    height: 300px;
  }
  
  .template-card::before {
    top: 0.75rem;
    right: 0.75rem;
  }
  
  .template-card::after {
    top: 0.75rem;
    left: 0.75rem;
  }
}