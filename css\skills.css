/* Skills page styling */
.skills-container {
    padding: 20px 0;
}

.skills-category {
    margin-bottom: 30px;
}

.skills-category h2 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 8px;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.skill-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: all 0.3s ease;
}

.skill-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.skill-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.skill-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #4a6cf7;
    font-size: 18px;
}

.skill-badge {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.skill-badge img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.skill-header h3 {
    font-size: 1.1rem;
    margin: 0;
    font-weight: 600;
}

.skill-proficiency {
    margin-bottom: 15px;
}

.proficiency-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.proficiency-stars {
    display: flex;
}

.star {
    color: #d1d1d1;
    margin-right: 5px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.star:hover,
.star.active {
    color: #ffc107;
}

.skill-actions {
    display: flex;
    justify-content: flex-end;
}

.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.empty-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.empty-state p {
    color: #666;
    margin-bottom: 20px;
}

/* Modal styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s;
}

@keyframes slideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.close {
    font-size: 24px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input[type="text"]:focus,
.form-group select:focus {
    border-color: #4a6cf7;
    outline: none;
}

.form-group input[type="file"] {
    padding: 8px 0;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check input[type="checkbox"] {
    margin-right: 10px;
}

.upload-info {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Make the modal responsive */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
}