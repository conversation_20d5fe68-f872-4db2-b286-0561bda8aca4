// store/skillsStore.js
import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, GETINFO_ENDPOINT, DELETE_INFO } from "../lib/constants";

export const useSkillsStore = create((set) => ({
    skills: [],
    user: null,
    isLoading: false,
    error: null,

    fetchSkills: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, {
                withCredentials: true,
            });
            const user = res.data.user;
            const skills = user?.Skills || [];
            set({ user, skills, isLoading: false });
        } catch (err) {
            console.error("Error fetching skills:", err.response?.data || err.message);
            set({
                error: err.response?.data?.error || "Failed to load skills",
                isLoading: false,
            });
        }
    },

    addSkill: async (data) => {
        set({ isLoading: true, error: null });
        try {
            await axios.post(ADDINFO_ENDPOINTS.SKILLS, data, {
                withCredentials: true,
            });
            // Auto-fetch updated skills list after successful addition
            const { fetchSkills } = useSkillsStore.getState();
            await fetchSkills();

            set({ isLoading: false });
        } catch (err) {
            console.error("Error adding skill:", err.response?.data || err.message);
            set({
                error: err.response?.data?.error || "Failed to add skill",
                isLoading: false,
            });
        }
    },

    deleteSkill: async (skill) => {
        try {
            await axios.delete(DELETE_INFO.SKILLS(skill._id), {
                withCredentials: true,
            });
            const { fetchSkills } = useSkillsStore.getState();
            fetchSkills(); // Refresh list after delete
        } catch (error) {
            console.error("Failed to delete skill:", error);
        }
    },

    setSkills: (skills) => set({ skills }),
    clearSkills: () => set({ skills: [], user: null, error: null }),
}));
