import React, { useState } from "react";
import { toast } from "react-toastify";

const CreateResumeModal = ({ onClose, onSuccess }) => {
  const [title, setTitle] = useState("");
  const [creating, setCreating] = useState(false);

  const createResume = async () => {
    if (!title.trim()) {
      toast.warn("Please enter a resume title");
      return;
    }

    setCreating(true);
    try {
      const res = await fetch("http://localhost:5000/api/resume", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // ⬅️ Important for sending the AuthToken cookie
        body: JSON.stringify({
          Title: title.trim(),
          Template: "default",
        }),
      });

      const data = await res.json();

      if (res.ok) {
        toast.success("Resume created successfully");
        setTitle("");
        onSuccess(data.resume._id); // Pass resume ID back
        onClose(); // Close modal
      } else {
        toast.error(data.error || "Failed to create resume");
      }
    } catch (err) {
      console.error(err);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Create Resume</h2>
        <input
          type="text"
          placeholder="Enter resume title"
          className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#29354d]"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
        />
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 hover:text-gray-900"
          >
            Cancel
          </button>
          <button
            onClick={createResume}
            disabled={creating}
            className="px-4 py-2 bg-[#29354d] text-[#fcc250] rounded hover:opacity-90"
          >
            {creating ? "Creating..." : "Create"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateResumeModal;
