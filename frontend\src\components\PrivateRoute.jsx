// import React from "react";
// import { Navigate, useLocation } from "react-router-dom";
// import { useAuthStore } from "../store/authStore";

// const PrivateRoute = ({ children }) => {
//   const { user, isAuthenticated, isLoading } = useAuthStore();
//   const location = useLocation();

//   if (isLoading) {
//     return (
//       <div className="min-h-screen flex justify-center items-center">
//         <div className="text-gray-500 text-lg animate-pulse">Loading...</div>
//       </div>
//     );
//   }

//   if (!isAuthenticated || !user) {
//     return <Navigate to="/" state={{ from: location }} replace />;
//   }

//   return children;
// };

// export default PrivateRoute;
import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuthStore } from "../store/authStore";

const PrivateRoute = ({ children, adminOnly = false }) => {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="text-gray-500 text-lg animate-pulse">Loading...</div>
      </div>
    );
  }

  // If user is not logged in
  if (!isAuthenticated || !user) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // If route is admin-only but user is not admin
  if (adminOnly && user.role !== "admin") {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

export default PrivateRoute;
