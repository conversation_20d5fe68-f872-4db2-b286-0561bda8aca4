import React, { useRef, useEffect, useState } from "react";
import { motion } from "framer-motion";
import registicon from "../../assets/WITregistration.png";
import logingIcon from "../../assets/login.png";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
const AnimatedIcon = ({ src, alt, iconType = "img", size }) => {
  if (iconType === "img") {
    return (
      <motion.img
        src={src}
        alt={alt}
        className={`object-contain ${size}`}
        loading="lazy"
      />
    );
  }
  return (
    <DotLottieReact
      src={src}
      loop
      autoplay
      style={{ width: "100%", height: "100%" }}
    />
  );
};

const steps = [
  [
    {
      label: "Registration",
      desc: "Create your account and set up your profile.",
      icon: registicon,
      iconType: "img",
    },
    {
      label: "Login",
      desc: "Login securely to your dashboard.",
      icon: logingIcon,
      iconType: "img",
    },
  ],

  [
    {
      label: "Choose Template",
      desc: "Browse through our collection and select a template that fits your style.",
      iconType: "lottie",
      iconSrc:
        "https://lottie.host/c9057335-7d23-4e61-ab00-5720cc630986/M7KKfhUFub.lottie",
    },
    {
      label: "Fill Details",
      desc: "Add your personal details, experience, and education easily.",
      iconType: "lottie",
      iconSrc:
        "https://lottie.host/fdb0010d-e7bf-4d33-aafc-043b2f55f663/HbinTMFADD.lottie",
    },
  ],

  [
    {
      label: "Download & Apply",
      desc: "Save your finished CV in multiple formats and start applying for jobs.",
      iconType: "lottie",
      iconSrc:
        "https://lottie.host/9d893b24-5948-4269-878e-14b7cceee9be/AoU78hVxKe.lottie",
    },
    {
      label: "Apply for the Job",
      desc: "Your resume is ready — now you can apply with confidence!",
      iconType: "lottie",
      iconSrc:
        "https://lottie.host/405d5cd3-b8e3-4338-9edd-6c794d01fead/fACDF0ITdJ.lottie",
    },
  ],
];

const Process = ({ iconSize = "w-16 h-16 md:w-20 md:h-20" }) => {
  const [activeRow, setActiveRow] = useState(0);
  const [carTrigger, setCarTrigger] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const rows = document.querySelectorAll(".hiw-row");
      let found = 0;

      for (let i = 0; i < rows.length; i++) {
        const rect = rows[i].getBoundingClientRect();
        if (
          rect.top < window.innerHeight * 0.44 &&
          rect.bottom > window.innerHeight * 0.2
        ) {
          found = i;
        }
      }
      setActiveRow(found);
      setCarTrigger(found === steps.length - 1);
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section className="relative py-10 md:py-24 min-h-screen flex flex-col items-center bg-[#f5f7fb]">
      <motion.h2
        className="text-3xl md:text-5xl font-bold text-[#29354d] mb-10 md:mb-16 text-center tracking-tight"
        initial={{ scale: 0.9, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        viewport={{ once: false, amount: 0.75 }}
        transition={{ duration: 0.75 }}
      >
        How It Works
      </motion.h2>
      <div className="relative flex flex-row w-full max-w-5xl mx-auto">
        <div className="absolute left-1/2 -translate-x-1/2 h-full flex flex-col items-center z-0 pointer-events-none">
          <div
            className="absolute top-0 left-1/2 -translate-x-1/2 w-1 bg-[#29354d] h-full"
            style={{ minHeight: 420 }}
          />
          {steps.map((_, idx) => (
            <div
              key={idx}
              className="relative"
              style={{
                top: `${idx * 33 + 5}%`,
                marginTop: idx === 0 ? "0" : "-16px",
                zIndex: 3,
              }}
            >
              <span
                className={`block mx-auto rounded-full border-2 
                  ${
                    activeRow === idx
                      ? "bg-[#fcc250] border-[#fcc250]"
                      : "bg-white border-[#29354d]"
                  }
                `}
                style={{
                  width: 20,
                  height: 20,
                  boxShadow: activeRow === idx ? "0 0 0 4px #fcc25030" : "none",
                }}
              />
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-16 gap-x-8 w-full z-10">
          {steps.map((row, rIdx) =>
            row.map((step, cIdx) => (
              <motion.div
                key={rIdx + "_" + cIdx}
                className="hiw-row flex flex-col items-center justify-center px-2"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: false, amount: 0.33 }}
                transition={{
                  duration: 0.7,
                  delay: rIdx * 0.28 + cIdx * 0.14,
                }}
              >
                <div
                  className={`w-full rounded-2xl border-2 border-[#29354d] p-6 sm:p-8 flex flex-col items-center shadow-md
                    ${cIdx === 0 ? "mr-auto" : "ml-auto"}
                  `}
                  style={{
                    minWidth: 260,
                    maxWidth: 360,
                    minHeight: 176,
                    position: "relative",
                    boxShadow:
                      step.iconType === "car"
                        ? "0 6px 32px 0 #29354d12, 0 1.5px 6px 0 #29354d30"
                        : "0 2px 10px 0 #29354d09",
                    backgroundColor: "#fff",
                  }}
                >
                  <div className="mb-4 flex justify-center">
                    <AnimatedIcon
                      src={step.iconSrc || step.icon}
                      alt={step.label}
                      iconType={step.iconType}
                      size={iconSize}
                    />
                  </div>
                  <div
                    className={`flex flex-col ${
                      cIdx === 0
                        ? "items-start text-left"
                        : "items-end text-right"
                    } w-full`}
                  >
                    <motion.h4
                      className="font-extrabold text-lg md:text-xl text-[#29354d] mb-2"
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.42, delay: 0.11 }}
                    >
                      {step.label}
                    </motion.h4>
                    <motion.div
                      className="text-[#29354d] text-base font-normal w-full"
                      initial={{ y: 18, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.46, delay: 0.2 }}
                    >
                      {step.desc}
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default Process;
