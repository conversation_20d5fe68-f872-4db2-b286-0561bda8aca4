<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$skills = getUserSkills($user_id);
$categories = getSkillCategories();
$errors = [];
$success = false;

// Process add skill form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    // Get form data
    $skill_name = sanitize($_POST['skill_name']);
    $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : null;
    $proficiency = isset($_POST['proficiency']) ? (int)$_POST['proficiency'] : 0;
    $has_badge = isset($_POST['has_badge']) ? 1 : 0;
    
    // Validate form data
    if (empty($skill_name)) {
        $errors[] = 'Skill name is required';
    }
    
    // Check if skill already exists for this user
    $db->query("SELECT id FROM skills WHERE user_id = :user_id AND skill_name = :skill_name");
    $db->bind(':user_id', $user_id);
    $db->bind(':skill_name', $skill_name);
    $db->execute();
    
    if ($db->rowCount() > 0) {
        $errors[] = 'You already have this skill in your profile';
    }
    
    // Handle badge upload
    $badge_url = null;
    
    if ($has_badge && isset($_FILES['badge_image']) && $_FILES['badge_image']['error'] == 0) {
        $uploaded_badge = uploadFile($_FILES['badge_image'], UPLOAD_DIR . 'badges/');
        
        if ($uploaded_badge) {
            $badge_url = $uploaded_badge;
        } else {
            $errors[] = 'Failed to upload badge image. Please try again.';
        }
    }
    
    // If no errors, add skill
    if (empty($errors)) {
        $db->query("INSERT INTO skills (user_id, skill_name, category_id, proficiency, has_badge, badge_url) 
                    VALUES (:user_id, :skill_name, :category_id, :proficiency, :has_badge, :badge_url)");
        
        $db->bind(':user_id', $user_id);
        $db->bind(':skill_name', $skill_name);
        $db->bind(':category_id', $category_id);
        $db->bind(':proficiency', $proficiency);
        $db->bind(':has_badge', $has_badge);
        $db->bind(':badge_url', $badge_url);
        
        if ($db->execute()) {
            $success = true;
            $skills = getUserSkills($user_id); // Refresh skills
        } else {
            $errors[] = 'Something went wrong. Please try again.';
        }
    }
}

// Process delete skill
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $skill_id = (int)$_POST['skill_id'];
    
    // Get skill to check if it belongs to the user
    $db->query("SELECT * FROM skills WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $skill_id);
    $db->bind(':user_id', $user_id);
    $skill = $db->single();
    
    if ($skill) {
        // Delete badge image if exists
        if ($skill['has_badge'] && !empty($skill['badge_url']) && file_exists(UPLOAD_DIR . 'badges/' . $skill['badge_url'])) {
            unlink(UPLOAD_DIR . 'badges/' . $skill['badge_url']);
        }
        
        // Delete skill
        $db->query("DELETE FROM skills WHERE id = :id");
        $db->bind(':id', $skill_id);
        
        if ($db->execute()) {
            $success = true;
            $skills = getUserSkills($user_id); // Refresh skills
        } else {
            $errors[] = 'Something went wrong. Please try again.';
        }
    } else {
        $errors[] = 'Skill not found or you do not have permission to delete it.';
    }
}

// Process update skill
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    $skill_id = (int)$_POST['skill_id'];
    $proficiency = (int)$_POST['proficiency'];
    
    // Get skill to check if it belongs to the user
    $db->query("SELECT * FROM skills WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $skill_id);
    $db->bind(':user_id', $user_id);
    $skill = $db->single();
    
    if ($skill) {
        // Update skill
        $db->query("UPDATE skills SET proficiency = :proficiency WHERE id = :id");
        $db->bind(':proficiency', $proficiency);
        $db->bind(':id', $skill_id);
        
        if ($db->execute()) {
            $success = true;
            $skills = getUserSkills($user_id); // Refresh skills
        } else {
            $errors[] = 'Something went wrong. Please try again.';
        }
    } else {
        $errors[] = 'Skill not found or you do not have permission to update it.';
    }
}

// Group skills by category
$skills_by_category = [];
foreach ($skills as $skill) {
    $category = $skill['category_name'] ?? 'Other';
    if (!isset($skills_by_category[$category])) {
        $skills_by_category[$category] = [];
    }
    $skills_by_category[$category][] = $skill;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Skills - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/skills.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        /* Header Styles */
        header {
            background-color: var(--white);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            height: 70px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            text-decoration: none;
        }
        
        .logo i {
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--spacing-lg);
        }
        
        nav a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: var(--spacing-xs) 0;
            position: relative;
            font-size: 0.95rem;
        }
        
        nav a:hover {
            color: var(--primary);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary);
            transition: var(--transition);
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        .user-dropdown {
            position: relative;
            cursor: pointer;
        }
        
        .user-dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius);
            transition: var(--transition);
        }
        
        .user-dropdown-toggle:hover {
            background-color: var(--light);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-weight: 600;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            width: 200px;
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
            z-index: 100;
        }
        
        .user-dropdown.active .user-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .user-dropdown-menu a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--dark);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .user-dropdown-menu a:hover {
            background-color: var(--light);
            color: var(--primary);
        }
        
        .user-dropdown-menu .divider {
            height: 1px;
            background-color: var(--light-gray);
            margin: 0.5rem 0;
        }
        
        .user-dropdown-menu .logout {
            color: var(--danger);
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            flex: 1;
            position: relative;
        }
        
        .sidebar {
            width: 260px;
            background-color: var(--white);
            border-right: 1px solid var(--light-gray);
            height: calc(100vh - 70px);
            position: sticky;
            top: 70px;
            overflow-y: auto;
            transition: var(--transition);
            z-index: 90;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar.collapsed .sidebar-toggle {
            right: -15px;
        }
        
        .user-info {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info {
            padding: var(--spacing-sm) 0;
        }
        
        .sidebar .user-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-sm);
            font-size: 1.5rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .user-info h3 {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .user-info p {
            color: var(--gray);
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info h3,
        .sidebar.collapsed .user-info p {
            opacity: 0;
            height: 0;
            margin: 0;
            overflow: hidden;
        }
        
        .sidebar-nav {
            padding: var(--spacing-md) 0;
        }
        
        .sidebar-nav ul {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem var(--spacing-md);
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .sidebar-nav a:hover {
            color: var(--primary);
            background-color: var(--primary-light);
        }
        
        .sidebar-nav li.active a {
            color: var(--primary);
            border-left-color: var(--primary);
            background-color: var(--primary-light);
            font-weight: 500;
        }
        
        .sidebar-nav i {
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .sidebar-nav a span {
            opacity: 0;
            width: 0;
            height: 0;
            overflow: hidden;
        }
        
        .sidebar-toggle {
            position: absolute;
            bottom: 20px;
            right: 2px;
            width: 30px;
            height: 30px;
            background-color: var(--primary);
            color: var(--white);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
            z-index: 10;
            transition: var(--transition);
        }
        
        .sidebar-toggle:hover {
            background-color: var(--primary-dark);
            transform: scale(1.1);
        }
        
        .dashboard-content {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .dashboard-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--dark);
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* Profile Form Styles */
        .profile-container {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }
        
        .profile-form {
            padding: var(--spacing-md);
        }
        
        .form-section {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .form-section h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--primary);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .form-group {
            margin-bottom: var(--spacing-md);
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--light-gray);
            border-radius: var(--radius);
            font-family: inherit;
            font-size: 0.95rem;
            color: var(--dark);
            transition: var(--transition);
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(45, 95, 139, 0.1);
            outline: none;
        }
        
        .form-group input:disabled {
            background-color: var(--light);
            cursor: not-allowed;
        }
        
        .field-hint {
            margin-top: 0.5rem;
            font-size: 0.85rem;
            color: var(--gray);
        }
        
        .field-hint a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .field-hint a:hover {
            text-decoration: underline;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: var(--spacing-md);
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.6rem 1.25rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.95rem;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        /* Alerts */
        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius);
            margin-bottom: var(--spacing-md);
            animation: fadeInUp 0.6s ease-out;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success);
            color: var(--success);
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger);
            color: var(--danger);
        }
        
        /* Mobile Sidebar Toggle */
        .mobile-sidebar-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--dark);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sections {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: fixed;
                left: -260px;
                height: 100vh;
                top: 0;
                z-index: 1000;
            }
            
            .sidebar.active {
                left: 0;
            }
            
            .sidebar-toggle {
                display: none;
            }
            
            .mobile-sidebar-toggle {
                display: block;
            }
            
            .dashboard-content {
                margin-left: 0;
                width: 100%;
            }
            
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }
            
            .overlay.active {
                opacity: 1;
                visibility: visible;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            nav ul {
                display: none;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="dashboard-container">
        <div class="sidebar">
            <div class="user-info">
                <?php $profile = getUserProfile($user_id); ?>
                <div class="user-avatar">
                    <?php if (!empty($profile['profile_image'])): ?>
                        <img src="<?php echo UPLOAD_DIR . 'profiles/' . $profile['profile_image']; ?>" alt="Profile Image">
                    <?php else: ?>
                        <div class="avatar-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <h3><?php echo !empty($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : $_SESSION['username']; ?></h3>
                <p><?php echo !empty($profile['title']) ? $profile['title'] : 'Complete your profile'; ?></p>
            </div>
            
            <nav class="sidebar-nav">
                <ul style="gap:0">
                    <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                    <li class="active"><a href="skills.php"><i class="fas fa-star"></i> Skills</a></li>
                    <li><a href="experience.php"><i class="fas fa-briefcase"></i> Experience</a></li>
                    <li><a href="education.php"><i class="fas fa-graduation-cap"></i> Education</a></li>
                    <li><a href="projects.php"><i class="fas fa-project-diagram"></i> Projects</a></li>
                    <li><a href="certifications.php"><i class="fas fa-certificate"></i> Certifications</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>My Skills</h1>
                <div class="action-buttons">
                <button id="add-skill-btn" class="btn btn-primary" type="button"><i class="fas fa-plus"></i> Add Skill</button>                </div>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <p>Skills updated successfully!</p>
                </div>
            <?php endif; ?>
            
            <?php echo displayMessage(); ?>
            
            <div class="skills-container">
                <?php if (count($skills) > 0): ?>
                    <?php foreach ($skills_by_category as $category => $category_skills): ?>
                        <div class="skills-category">
                            <h2><?php echo $category; ?></h2>
                            <div class="skills-grid">
                                <?php foreach ($category_skills as $skill): ?>
                                    <div class="skill-card" data-id="<?php echo $skill['id']; ?>">
                                        <div class="skill-header">
                                            <?php if ($skill['has_badge'] && !empty($skill['badge_url'])): ?>
                                                <div class="skill-badge">
                                                    <img src="<?php echo UPLOAD_DIR . 'badges/' . $skill['badge_url']; ?>" alt="<?php echo $skill['skill_name']; ?>">
                                                </div>
                                            <?php else: ?>
                                                <div class="skill-icon">
                                                    <i class="fas fa-<?php echo $skill['category_icon'] ?? 'star'; ?>"></i>
                                                </div>
                                            <?php endif; ?>
                                            <h3><?php echo $skill['skill_name']; ?></h3>
                                        </div>
                                        
                                        <div class="skill-proficiency">
                                            <div class="proficiency-label">Proficiency</div>
                                            <div class="proficiency-stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <span class="star <?php echo $i <= $skill['proficiency'] ? 'active' : ''; ?>" data-value="<?php echo $i; ?>">
                                                        <i class="fas fa-star"></i>
                                                    </span>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="skill-actions">
                                            <form action="skills.php" method="POST" class="delete-skill-form">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this skill?')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>No Skills Added Yet</h3>
                        <p>Add your skills to showcase your expertise in your resume.</p>
                        <button id="empty-add-skill-btn" class="btn btn-primary">Add Skill</button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
    
    <!-- Add Skill Modal -->
    <div id="add-skill-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Skill</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form action="skills.php" method="POST" enctype="multipart/form-data" id="add-skill-form">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="form-group">
                        <label for="skill_name">Skill Name *</label>
                        <input type="text" id="skill_name" name="skill_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="category_id">Category</label>
                        <select id="category_id" name="category_id">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="proficiency">Proficiency</label>
                        <div class="proficiency-input">
                            <div class="proficiency-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <span class="star" data-value="<?php echo $i; ?>">
                                        <i class="fas fa-star"></i>
                                    </span>
                                <?php endfor; ?>
                            </div>
                            <input type="hidden" id="proficiency" name="proficiency" value="0">
                        </div>
                    </div>
                    
                    <div class="form-group form-check">
                        <input type="checkbox" id="has_badge" name="has_badge">
                        <label for="has_badge">Add Badge Image</label>
                    </div>
                    
                    <div class="form-group badge-upload" style="display: none;">
                        <label for="badge_image">Badge Image</label>
                        <input type="file" id="badge_image" name="badge_image" accept="image/*">
                        <p class="upload-info">Recommended size: 100x100px, Max size: 2MB</p>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Add Skill</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Update Skill Form (Hidden) -->
    <form id="update-skill-form" action="skills.php" method="POST" style="display: none;">
        <input type="hidden" name="action" value="update">
        <input type="hidden" name="skill_id" id="update_skill_id">
        <input type="hidden" name="proficiency" id="update_proficiency">
    </form>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="js/skills.js"></script>
</body>
</html>