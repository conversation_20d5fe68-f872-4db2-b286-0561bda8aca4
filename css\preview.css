/* Resume Preview Styles */
body {
    background-color: #f5f5f5;
}

.preview-controls {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.preview-info h1 {
    margin-bottom: 0;
    font-size: 1.5rem;
}

.preview-actions {
    display: flex;
    gap: 1rem;
}

.resume-preview-container {
    max-width: 800px;
    margin: 100px auto 50px;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.resume-preview {
    padding: 2rem;
}

.preview-footer {
    text-align: center;
    padding: 2rem 0;
}

/* Resume Template Styles */
.resume-template {
    font-family: 'Arial', sans-serif;
    color: #333;
    line-height: 1.6;
}

.resume-template.serif {
    font-family: 'Georgia', serif;
}

.resume-template.monospace {
    font-family: 'Courier New', monospace;
}

.resume-header {
    text-align: center;
    margin-bottom: 2rem;
}

.resume-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

.resume-title {
    font-size: 1.25rem;
    color: var(--gray);
    margin-bottom: 1rem;
}

.resume-contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.9rem;
}

.resume-contact-item {
    display: flex;
    align-items: center;
}

.resume-contact-item i {
    margin-right: 0.5rem;
    color: var(--primary);
}

.resume-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 1.5rem;
    border: 3px solid var(--primary);
}

.resume-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.resume-section {
    margin-bottom: 2rem;
}

.resume-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary);
    border-bottom: 2px solid var(--primary);
    padding-bottom: 0.5rem;
}

.resume-objective {
    margin-bottom: 2rem;
    font-style: italic;
    color: var(--gray);
}

.resume-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.skill-badge {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    border-radius: 3px;
    font-size: 0.9rem;
}

.experience-item,
.education-item,
.project-item,
.certification-item {
    margin-bottom: 1.5rem;
}

.item-header {
    margin-bottom: 0.5rem;
}

.item-title {
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.item-subtitle {
    font-weight: 500;
    color: var(--gray);
    margin-bottom: 0.25rem;
}

.item-period {
    font-size: 0.875rem;
    color: var(--gray);
}

.item-description {
    font-size: 0.9rem;
}

.item-description ul {
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

.item-description li {
    margin-bottom: 0.25rem;
}

/* Color Schemes */
.resume-template.blue .resume-name,
.resume-template.blue .resume-section-title,
.resume-template.blue .resume-contact-item i {
    color: var(--blue);
}

.resume-template.blue .resume-section-title {
    border-color: var(--blue);
}

.resume-template.blue .resume-photo {
    border-color: var(--blue);
}

.resume-template.blue .skill-badge {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--blue);
}

.resume-template.green .resume-name,
.resume-template.green .resume-section-title,
.resume-template.green .resume-contact-item i {
    color: var(--green);
}

.resume-template.green .resume-section-title {
    border-color: var(--green);
}

.resume-template.green .resume-photo {
    border-color: var(--green);
}

.resume-template.green .skill-badge {
    background-color: rgba(56, 176, 0, 0.1);
    color: var(--green);
}

.resume-template.black .resume-name,
.resume-template.black .resume-section-title,
.resume-template.black .resume-contact-item i {
    color: var(--black);
}

.resume-template.black .resume-section-title {
    border-color: var(--black);
}

.resume-template.black .resume-photo {
    border-color: var(--black);
}

.resume-template.black .skill-badge {
    background-color: rgba(33, 37, 41, 0.1);
    color: var(--black);
}

.resume-template.gray .resume-name,
.resume-template.gray .resume-section-title,
.resume-template.gray .resume-contact-item i {
    color: var(--gray);
}

.resume-template.gray .resume-section-title {
    border-color: var(--gray);
}

.resume-template.gray .resume-photo {
    border-color: var(--gray);
}

.resume-template.gray .skill-badge {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--gray);
}

@media print {
    body {
        background-color: white;
    }
    
    .preview-controls,
    .preview-footer {
        display: none;
    }
    
    .resume-preview-container {
        margin: 0;
        box-shadow: none;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .preview-controls {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .resume-preview-container {
        margin-top: 150px;
    }
}