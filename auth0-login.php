<?php
require_once 'includes/functions.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// CORS headers (before session or any output)
$allowed_origins = [
    "http://localhost:5173",
    "https://app.yourdomain.com",
    "https://proyuj.com",
    "https://appttitude.proyuj.com"
];
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: $origin");
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Headers: Authorization, Content-Type");
    header("Access-Control-Allow-Methods: POST, OPTIONS");
}

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(204);
    exit();
}

header('Content-Type: application/json');

// Start session securely (AFTER CORS, BEFORE any output)
secureSessionStart();

// 1. Get JWT from Authorization header
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';
if (!$authHeader || !preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['error' => 'No token provided']);
    exit();
}
$jwt = $matches[1];

// 2. Decode JWT payload manually (NO signature verification!)
function base64url_decode($data) {
    return base64_decode(strtr($data, '-_', '+/'));
}
$parts = explode('.', $jwt);
if (count($parts) !== 3) {
    http_response_code(400);
    echo json_encode(['error' => 'Malformed JWT']);
    exit();
}
$payload = json_decode(base64url_decode($parts[1]), true);

$auth0_id = $payload['sub'] ?? null;
$email    = $payload['email'] ?? null;
$name     = $payload['name'] ?? null;
$aud      = $payload['aud'] ?? null;
$iss      = $payload['iss'] ?? null;

// Optional: Check audience and issuer for a basic sanity check
$expected_audience = 'wxDof22BzF2GegY3QjIGZEV53gK81b89';
$expected_issuer = 'https://dev-ja45ufmibqculccs.us.auth0.com/';
if (
    (is_array($aud) && !in_array($expected_audience, $aud)) ||
    (!is_array($aud) && $aud !== $expected_audience) ||
    $iss !== $expected_issuer
) {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid audience or issuer']);
    exit();
}

// 3. Upsert user in DB
$conn = getDbConnection();
$stmt = $conn->prepare("SELECT id, username, name, is_admin, email FROM users WHERE auth0_id = ?");
$stmt->bind_param("s", $auth0_id);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows == 0) {
    // Try to match legacy user by email
    $findByEmail = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $findByEmail->bind_param("s", $email);
    $findByEmail->execute();
    $findByEmail->store_result();
    if ($findByEmail->num_rows == 1) {
        $findByEmail->bind_result($existing_id);
        $findByEmail->fetch();
        $update = $conn->prepare("UPDATE users SET auth0_id = ? WHERE id = ?");
        $update->bind_param("si", $auth0_id, $existing_id);
        $update->execute();
        $user_id = $existing_id;
    } else {
        $insert = $conn->prepare("INSERT INTO users (auth0_id, email, name) VALUES (?, ?, ?)");
        $insert->bind_param("sss", $auth0_id, $email, $name);
        $insert->execute();
        $user_id = $insert->insert_id;
    }
} else {
    $stmt->bind_result($user_id, $username, $name, $is_admin, $email);
    $stmt->fetch();
}
$stmt->close();

// 4. Login the user: set session variables
$_SESSION['user_id'] = $user_id;
$_SESSION['email'] = $email;
$_SESSION['username'] = $username ?? '';
$_SESSION['name'] = $name ?? '';
$_SESSION['is_admin'] = $is_admin ?? 0;
$_SESSION['created'] = time();

$conn->close();

echo json_encode(['status' => 'ok', 'user_id' => $user_id]);