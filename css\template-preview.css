/* Template Preview Page Styles */
:root {
    --preview-bg: #f5f8fa;
    --preview-card-bg: #ffffff;
    --preview-primary: rgb(25, 65, 75);
    --preview-secondary: rgb(255, 145, 77);
    --preview-text: #333;
    --preview-border: rgba(25, 65, 75, 0.1);
    --preview-shadow: 0 10px 25px rgba(25, 65, 75, 0.1);
    --preview-radius: 12px;
    --preview-transition: all 0.3s ease;
  }
  
  .template-preview-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1.5rem;
  }
  
  /* Preview Header */
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--preview-border);
  }
  
  .preview-title h1 {
    font-size: 1.8rem;
    color: var(--preview-primary);
    margin-bottom: 0.5rem;
    font-weight: 700;
    position: relative;
  }
  
  .preview-title h1::after {
    content: "";
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--preview-secondary);
    border-radius: 3px;
  }
  
  .preview-title p {
    color: #666;
    font-size: 1rem;
  }
  
  .preview-actions {
    display: flex;
    gap: 1rem;
  }
  
  .preview-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.25rem;
    border-radius: var(--preview-radius);
    font-weight: 600;
    transition: var(--preview-transition);
    text-decoration: none;
    gap: 0.5rem;
  }
  
  .back-btn {
    background-color: #f0f0f0;
    color: var(--preview-text);
    border: 1px solid #e0e0e0;
  }
  
  .back-btn:hover {
    background-color: #e0e0e0;
  }
  
  .use-template-btn {
    background-color: var(--preview-primary);
    color: white;
    border: 1px solid var(--preview-primary);
  }
  
  .use-template-btn:hover {
    background-color: rgba(25, 65, 75, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 65, 75, 0.2);
  }
  
  /* Preview Content */
  .preview-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2rem;
  }
  
  /* Main Preview Area */
  .preview-main {
    background-color: var(--preview-bg);
    border-radius: var(--preview-radius);
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  .resume-preview-wrapper {
    background-color: var(--preview-card-bg);
    border-radius: var(--preview-radius);
    box-shadow: var(--preview-shadow);
    overflow: hidden;
    transition: var(--preview-transition);
    position: relative;
  }
  
  .resume-preview-wrapper::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to right, var(--preview-primary), var(--preview-secondary));
  }
  
  .resume-preview {
    padding: 2rem;
    max-height: 800px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--preview-primary) #f0f0f0;
  }
  
  .resume-preview::-webkit-scrollbar {
    width: 8px;
  }
  
  .resume-preview::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
  }
  
  .resume-preview::-webkit-scrollbar-thumb {
    background-color: var(--preview-primary);
    border-radius: 10px;
  }
  
  /* Preview Sidebar */
  .preview-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .sidebar-section {
    background-color: var(--preview-card-bg);
    border-radius: var(--preview-radius);
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--preview-transition);
    border: 1px solid var(--preview-border);
  }
  
  .sidebar-section:hover {
    box-shadow: var(--preview-shadow);
    transform: translateY(-3px);
  }
  
  .sidebar-section h3 {
    font-size: 1.2rem;
    color: var(--preview-primary);
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--preview-border);
    font-weight: 600;
  }
  
  /* Template Details */
  .template-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px dashed var(--preview-border);
  }
  
  .detail-item:last-child {
    border-bottom: none;
  }
  
  .detail-label {
    font-weight: 600;
    color: #555;
  }
  
  .detail-value {
    color: var(--preview-text);
  }
  
  /* Template Features */
  .template-features {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .feature-icon {
    width: 24px;
    height: 24px;
    background-color: rgba(25, 65, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--preview-primary);
  }
  
  .feature-text {
    font-size: 0.95rem;
    color: var(--preview-text);
  }
  
  /* Action Button */
  .btn-block {
    width: 100%;
    padding: 1rem;
    text-align: center;
    font-size: 1rem;
    border-radius: var(--preview-radius);
    transition: var(--preview-transition);
    cursor: pointer;
  }
  
  .btn-primary {
    background-color: var(--preview-primary);
    color: white;
    border: none;
    font-weight: 600;
  }
  
  .btn-primary:hover {
    background-color: rgba(25, 65, 75, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 65, 75, 0.2);
  }
  
  /* Template Styles */
  .template-modern {
    font-family: "Arial", sans-serif;
    color: #333;
    line-height: 1.6;
  }
  
  .template-modern .header {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .template-modern .header h1 {
    color: var(--preview-primary);
    margin-bottom: 0.5rem;
    font-size: 2.2rem;
  }
  
  .template-modern .header p {
    color: #666;
    font-size: 1.1rem;
  }
  
  .template-modern .section {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
    background-color: #f9f9f9;
    border-radius: 8px;
    border-left: 4px solid var(--preview-primary);
  }
  
  .template-modern .section h2 {
    color: var(--preview-primary);
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.4rem;
  }
  
  .template-modern .section p {
    margin: 0.5rem 0;
  }
  
  .template-modern .section ul {
    padding-left: 1.5rem;
  }
  
  .template-modern .section li {
    margin-bottom: 0.5rem;
  }
  
  .template-professional {
    font-family: "Arial", sans-serif;
    color: #333;
  }
  
  .template-professional .header {
    text-align: center;
    padding-bottom: 1.5rem;
    border-bottom: 3px solid var(--preview-primary);
    margin-bottom: 1.5rem;
  }
  
  .template-professional .header h1 {
    color: var(--preview-primary);
    margin-bottom: 0.5rem;
  }
  
  .template-professional .contact-info {
    text-align: center;
    margin-bottom: 1.5rem;
  }
  
  .template-professional .contact-info a {
    color: var(--preview-primary);
    text-decoration: none;
    font-weight: 600;
  }
  
  .template-professional .section {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
    background-color: #f5f5f5;
    border-left: 5px solid var(--preview-primary);
    border-radius: 5px;
  }
  
  .template-professional .section h2 {
    color: var(--preview-primary);
    margin-top: 0;
    margin-bottom: 1rem;
  }
  
  .template-creative {
    font-family: "Arial", sans-serif;
    color: #333;
  }
  
  .template-creative .profile {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .template-creative .profile-info {
    flex-grow: 1;
  }
  
  .template-creative .profile-info h2 {
    color: #333;
    margin-bottom: 0.5rem;
  }
  
  .template-creative .contact a {
    display: block;
    text-decoration: none;
    color: var(--preview-primary);
    margin-top: 0.5rem;
  }
  
  .template-creative .section {
    margin-bottom: 1.5rem;
  }
  
  .template-creative .section h3 {
    border-bottom: 2px solid var(--preview-primary);
    padding-bottom: 0.5rem;
    color: #333;
  }
  
  .template-minimal {
    font-family: "Arial", sans-serif;
    color: #333;
    line-height: 1.6;
  }
  
  .template-minimal h1 {
    color: #333;
    margin-bottom: 1rem;
  }
  
  .template-minimal a {
    color: var(--preview-primary);
    text-decoration: none;
  }
  
  .template-minimal .section {
    margin-bottom: 1.5rem;
  }
  
  .template-minimal .section h2 {
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .template-modern-dark {
    font-family: "Arial", sans-serif;
    color: #333;
  }
  
  .template-modern-dark .container {
    display: flex;
  }
  
  .template-modern-dark .sidebar {
    background: #000;
    color: #fff;
    padding: 2rem;
    width: 30%;
  }
  
  .template-modern-dark .sidebar img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    margin-bottom: 1.5rem;
  }
  
  .template-modern-dark .main-content {
    width: 70%;
    padding: 2rem;
  }
  
  .template-modern-dark .section {
    margin-bottom: 2rem;
  }
  
  .template-modern-dark .timeline-item {
    margin-left: 1.5rem;
    padding: 1rem;
    background: #f9f9f9;
    border-left: 3px solid #333;
    margin-bottom: 1rem;
  }
  
  /* Responsive Styles */
  @media (max-width: 992px) {
    .preview-content {
      grid-template-columns: 1fr;
    }
  
    .preview-sidebar {
      order: -1;
    }
  
    .sidebar-section {
      margin-bottom: 1.5rem;
    }
  }
  
  @media (max-width: 768px) {
    .preview-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  
    .preview-actions {
      width: 100%;
      justify-content: space-between;
    }
  
    .preview-main {
      padding: 1rem;
    }
  
    .resume-preview {
      padding: 1.5rem;
    }
  
    .template-modern-dark .container {
      flex-direction: column;
    }
  
    .template-modern-dark .sidebar,
    .template-modern-dark .main-content {
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    .preview-action-btn {
      padding: 0.6rem 1rem;
      font-size: 0.9rem;
    }
  }
  
  