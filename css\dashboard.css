/* Base Styles Enhancement */
:root {
  --primary: rgb(25, 65, 75);
  --primary-light: rgba(25, 65, 75, 0.1);
  --primary-dark: rgb(15, 45, 55);
  --secondary: #f8a978;
  --secondary-light: #ffeee2;
  --accent: #4ecdc4;
  --light: #f7f9fc;
  --dark: #2d3748;
  --gray: #718096;
  --gray-light: #e2e8f0;
  --gray-lighter: #f7fafc;
  --success: #48bb78;
  --warning: #ecc94b;
  --error: #e53e3e;
  --white: #ffffff;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --radius: 8px;
  --radius-lg: 12px;
  --transition: all 0.3s ease;
  --container-width: 1200px;
}

body {
  background-color: #f8fafc;
  font-family: 'Poppins', sans-serif;
  color: var(--dark);
  line-height: 1.6;
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  max-width: var(--container-width);
  margin: 2rem auto;
  min-height: calc(100vh - 180px);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

/* Sidebar Enhancement */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #194155, #0d2b39);
  color: var(--white);
  padding: 2rem 0;
  transition: var(--transition);
  overflow-y: auto;
  height: 100%;
}

.sidebar.active {
  transform: translateX(0);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1.5rem;
}

.user-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1rem;
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255,255,255,0.2);
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--primary);
  background: var(--white);
}

.user-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.user-info p {
  font-size: 0.9rem;
  opacity: 0.8;
  text-align: center;
}

.sidebar-nav ul {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
}

.sidebar-nav li {
  margin-bottom: 0.25rem;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 0.8rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
}

.sidebar-nav a i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.sidebar-nav a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  transform: translateX(5px);
}

.sidebar-nav li.active a {
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-left: 4px solid var(--accent);
  font-weight: 600;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: var(--white);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-light);
}

.dashboard-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary);
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

/* Stats Cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  border-radius: var(--radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid var(--gray-light);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: var(--primary);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.stat-icon i {
  font-size: 1.5rem;
  color: var(--primary);
}

.stat-info h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.25rem;
  color: var(--primary-dark);
}

.stat-info p {
  margin: 0;
  color: var(--gray);
  font-size: 0.9rem;
}

/* Dashboard Sections */
.dashboard-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.section {
  background: var(--white);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-light);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--gray-light);
  background-color: var(--gray-lighter);
}

.section-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--primary);
}

.section-content {
  padding: 1.5rem;
}

/* Profile Completion */
.progress-bar {
  height: 12px;
  background-color: var(--gray-light);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  border-radius: 6px;
  transition: width 0.5s ease;
}

.completion-tips {
  margin-top: 1.5rem;
  background-color: var(--primary-light);
  padding: 1.25rem;
  border-radius: var(--radius);
  border-left: 4px solid var(--primary);
}

.completion-tips h4 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.completion-tips ul {
  padding-left: 1.5rem;
  margin: 0;
}

.completion-tips li {
  margin-bottom: 0.5rem;
}

.completion-tips a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
  font-weight: 500;
}

.completion-tips a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Resume Cards */
.resume-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.resume-card {
  background: var(--white);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid var(--gray-light);
}

.resume-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.resume-preview {
  height: 180px;
  overflow: hidden;
  border-bottom: 1px solid var(--gray-light);
  position: relative;
}

.resume-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.resume-card:hover .resume-preview img {
  transform: scale(1.05);
}

.resume-info {
  padding: 1.25rem;
}

.resume-info h3 {
  font-size: 1.1rem;
  margin: 0 0 0.5rem;
  color: var(--primary);
  font-weight: 600;
}

.resume-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: var(--gray);
}

.resume-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0 1.25rem 1.25rem;
  flex-wrap: wrap;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1.5rem;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.empty-icon i {
  font-size: 2rem;
  color: var(--primary);
}

.empty-state h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.empty-state p {
  margin-bottom: 1.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  color: var(--gray);
}

/* Buttons Enhancement */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  border: none;
  font-size: 0.95rem;
  gap: 0.5rem;
}

.btn i {
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-dark));
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

/* Sidebar Toggle */
.sidebar-toggle {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary);
  color: var(--white);
  border: none;
  box-shadow: var(--shadow);
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

/* Messages */
.message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: var(--radius);
  background-color: var(--light);
  border-left: 4px solid var(--primary);
  animation: fadeIn 0.5s ease;
}

.message.success {
  background-color: rgba(72, 187, 120, 0.1);
  border-left-color: var(--success);
}

.message.error {
  background-color: rgba(229, 62, 62, 0.1);
  border-left-color: var(--error);
}

.message.warning {
  background-color: rgba(236, 201, 75, 0.1);
  border-left-color: var(--warning);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-container {
    margin: 1rem;
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    max-height: auto;
    position: relative;
    transform: none;
  }
  
  .sidebar-nav ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .sidebar-nav li {
    margin: 0.25rem;
  }
  
  .sidebar-nav a {
    padding: 0.5rem 1rem;
  }
  
  .dashboard-content {
    padding: 1.5rem;
  }
  
  .dashboard-stats,
  .resume-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .action-buttons {
    width: 100%;
  }
  
  .sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    width: 280px;
  }
  
  .sidebar-toggle {
    display: flex;
  }
  
  .sidebar-nav ul {
    flex-direction: column;
  }
  
  .sidebar-nav li {
    margin: 0.25rem 0;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .btn {
    width: 100%;
  }
}