<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../vendor/autoload.php';

// Require login
requireLogin();

// Check if resume ID is provided
if (!isset($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Resume ID is required'
    ]);
    exit;
}

$resume_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Get resume from database
$db->query("SELECT r.*, t.name, t.html_structure, t.css_styles 
            FROM resumes r 
            JOIN templates t ON r.template_id = t.id 
            WHERE r.id = :id AND r.user_id = :user_id");
$db->bind(':id', $resume_id);
$db->bind(':user_id', $user_id);
$resume = $db->single();

if (!$resume) {
    echo json_encode([
        'success' => false,
        'message' => 'Resume not found or access denied'
    ]);
    exit;
}

// Parse content JSON
$content = json_decode($resume['content'], true);

// Create PDF using a library like TCPDF or mPDF
// This is a simplified example - in a real app, you would format the PDF properly
try {
    // Create PDF instance
    $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('ResumeForge');
    $pdf->SetAuthor($user_id);
    $pdf->SetTitle($resume['resume_name']);
    
    // Remove header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Add a page
    $pdf->AddPage();
    
    // Apply CSS styles
    $styles = $resume['css_styles'];
    
    // Generate HTML content from resume data
    $html = generateResumeHTML($content, $resume['html_structure']);
    
    // Write HTML to PDF
    $pdf->writeHTML($styles . $html, true, false, true, false, '');
    
    // Set filename
    $filename = 'resume_' . $resume_id . '.pdf';
    
    // Output PDF
    $pdf->Output($filename, 'D'); // D = download
    
    // Track download
    $db->query("UPDATE resumes SET downloads = downloads + 1 WHERE id = :id");
    $db->bind(':id', $resume_id);
    $db->execute();
    
    exit;
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate PDF: ' . $e->getMessage()
    ]);
    exit;
}

// Helper function to generate HTML from resume content
function generateResumeHTML($content, $template) {
    // In a real app, you would use the template structure and fill it with content
    // This is a simplified example
    $html = '<div class="resume">';
    
    // Personal info
    if (isset($content['personal'])) {
        $personal = $content['personal'];
        $html .= '<div class="personal-info">';
        $html .= '<h1>' . htmlspecialchars($personal['name'] ?? '') . '</h1>';
        $html .= '<h2>' . htmlspecialchars($personal['title'] ?? '') . '</h2>';
        
        $html .= '<div class="contact-details">';
        if (isset($personal['email'])) {
            $html .= '<div class="contact-item"><i class="fas fa-envelope"></i> ' . htmlspecialchars($personal['email']) . '</div>';
        }
        if (isset($personal['phone'])) {
            $html .= '<div class="contact-item"><i class="fas fa-phone"></i> ' . htmlspecialchars($personal['phone']) . '</div>';
        }
        if (isset($personal['location'])) {
            $html .= '<div class="contact-item"><i class="fas fa-map-marker-alt"></i> ' . htmlspecialchars($personal['location']) . '</div>';
        }
        if (isset($personal['linkedin'])) {
            $html .= '<div class="contact-item"><i class="fab fa-linkedin"></i> ' . htmlspecialchars($personal['linkedin']) . '</div>';
        }
        $html .= '</div>'; // contact-details
        $html .= '</div>'; // personal-info
    }
    
    // Summary
    if (isset($content['summary'])) {
        $html .= '<div class="section">';
        $html .= '<h2>Professional Summary</h2>';
        $html .= '<p>' . htmlspecialchars($content['summary']) . '</p>';
        $html .= '</div>';
    }
    
    // Experience
    if (isset($content['experience']) && is_array($content['experience'])) {
        $html .= '<div class="section">';
        $html .= '<h2>Work Experience</h2>';
        
        foreach ($content['experience'] as $job) {
            $html .= '<div class="experience-item">';
            $html .= '<div class="job-header">';
            $html .= '<h3>' . htmlspecialchars($job['title'] ?? '') . '</h3>';
            $html .= '<h4>' . htmlspecialchars($job['company'] ?? '') . '</h4>';
            $html .= '<div class="date">' . htmlspecialchars($job['date'] ?? '') . '</div>';
            $html .= '</div>'; // job-header
            
            if (isset($job['description'])) {
                $html .= '<ul>';
                foreach ($job['description'] as $item) {
                    $html .= '<li>' . htmlspecialchars($item) . '</li>';
                }
                $html .= '</ul>';
            }
            
            $html .= '</div>'; // experience-item
        }
        
        $html .= '</div>'; // section
    }
    
    // Education
    if (isset($content['education']) && is_array($content['education'])) {
        $html .= '<div class="section">';
        $html .= '<h2>Education</h2>';
        
        foreach ($content['education'] as $edu) {
            $html .= '<div class="education-item">';
            $html .= '<div class="edu-header">';
            $html .= '<h3>' . htmlspecialchars($edu['degree'] ?? '') . '</h3>';
            $html .= '<h4>' . htmlspecialchars($edu['school'] ?? '') . '</h4>';
            $html .= '<div class="date">' . htmlspecialchars($edu['date'] ?? '') . '</div>';
            $html .= '</div>'; // edu-header
            
            if (isset($edu['description'])) {
                $html .= '<p>' . htmlspecialchars($edu['description']) . '</p>';
            }
            
            $html .= '</div>'; // education-item
        }
        
        $html .= '</div>'; // section
    }
    
    // Skills
    if (isset($content['skills']) && is_array($content['skills'])) {
        $html .= '<div class="section">';
        $html .= '<h2>Skills</h2>';
        $html .= '<div class="skills-container">';
        
        foreach ($content['skills'] as $skill) {
            $html .= '<div class="skill-item">' . htmlspecialchars($skill) . '</div>';
        }
        
        $html .= '</div>'; // skills-container
        $html .= '</div>'; // section
    }
    
    // Add other sections as needed
    
    $html .= '</div>'; // resume
    
    return $html;
}
?>
