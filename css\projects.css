/* Projects Container Styles */
.projects-container {
    padding: 20px 0;
}

/* Projects Grid Styles */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Project Card Styles */
.project-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Project Header Styles */
.project-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.project-header h2 {
    margin: 0 0 5px;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.project-role {
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 5px;
    color: #444;
}

.project-period {
    color: #666;
    font-size: 0.9rem;
    margin-top: 8px;
}

/* Project Description Styles */
.project-description {
    flex-grow: 1;
    margin-bottom: 15px;
}

.project-description p {
    margin: 0;
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Project Technologies Styles */
.project-technologies {
    margin: 15px 0;
}

.project-technologies h3 {
    font-size: 1rem;
    margin: 0 0 10px;
    color: #333;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tech-tag {
    background-color: var(--primary-color-light);
    color: var(--primary-color-dark);
    font-size: 0.85rem;
    padding: 4px 10px;
    border-radius: 50px;
    display: inline-block;
}

/* Project URL Styles */
.project-url {
    margin: 15px 0;
}

.project-url a {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    text-decoration: none;
}

.project-url a i {
    font-size: 0.9rem;
}

/* Project Actions Styles */
.project-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.delete-project-form {
    margin: 0;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: #333;
}

.empty-state p {
    margin: 0 0 20px;
    color: #666;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.close {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #f0f0f0;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Form Styles */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group input[type="url"],
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-hint {
    font-size: 0.85rem;
    color: #777;
    margin-top: 4px;
    margin-bottom: 0;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.form-check label {
    margin-bottom: 0;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .projects-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .project-actions {
        flex-direction: column;
    }
    
    .project-actions button,
    .project-actions form,
    .project-actions form button {
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}