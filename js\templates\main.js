document.addEventListener("DOMContentLoaded", () => {
  // Global variables
  let currentTemplate = null
  let currentResumeId = null
  let isUnsaved = false

  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search)
  const templateId = urlParams.get("template")
  const resumeId = urlParams.get("resume")

  // Initialize the editor
  initEditor()

  // Main initialization function
  async function initEditor() {
    // Setup UI event listeners
    setupUIEventListeners()

    // Load resume or template
    if (resumeId) {
      // Load existing resume
      await loadResume(resumeId)
    } else if (templateId) {
      // Load template
      await loadTemplate(templateId)
    } else {
      // No template or resume specified, redirect to templates page
      window.location.href = "templates.php"
    }

    // Initialize editor components
    initToolbar()
    setupSectionDragging()
    setupPropertiesPanel()
    initPreviewModal()
    initTemplateModal()
    initTemplateSettingsModal()
    setupAddItemButtons()
    setupSectionButtons()
  }

  // Load template from database
  async function loadTemplate(templateId) {
    try {
      showLoading("Loading template...")

      const response = await fetch(`api/get-template.php?id=${templateId}`)

      const rawText = await response.text()


      let data
      try {
        data = JSON.parse(rawText)
      } catch (parseError) {
        console.error("JSON parse error:", parseError)
        showToast("Invalid response from server. Check console for details.", "error")
        hideLoading()
        return
      }

      hideLoading()

      if (response.ok) {
        currentTemplate = data.template

        // Set document name
        const documentNameEl = document.querySelector(".document-name")
        
        if (documentNameEl) {
          documentNameEl.textContent = `New ${currentTemplate.name} Resume`
        }

        // Render template
        renderTemplate(currentTemplate)

        // Set save status
        updateSaveStatus("unsaved")
        isUnsaved = true
      } else {
        showToast("Failed to load template: " + data.message, "error")
      }
    } catch (error) {
      hideLoading()
      console.error("Error loading template:", error)
      showToast("An error occurred while loading the template", "error")
    }
  }

  // Load resume from database
  async function loadResume(resumeId) {
    try {
      showLoading("Loading resume...");
  
      const response = await fetch(`api/get-resume.php?id=${resumeId}`);
      
      const data = await response.json(); 
      
      console.log("Parsed response:", data); 
  
      hideLoading();
  
      if (data.success) {
        console.log("Success condition met ✅");
  
        const resume = data.resume;
  
        currentResumeId = resumeId;
        currentTemplate = {
          id: resume.template_id,
          template_name: resume.name,
          category: resume.category,
          html_structure: resume.html_structure,
          css_styles: resume.css_styles,
        };
  
        const documentNameEl = document.querySelector(".document-name");
        if (documentNameEl) {
          documentNameEl.textContent = resume.name;
        }

        console.log("cont",resume);
  
        renderTemplate(currentTemplate, resume.content);
        updateSaveStatus("saved");
        isUnsaved = false;
      } else {
        showToast("Failed to load resume: " + data.message, "error");
      }
    } catch (error) {
      hideLoading();
      console.error("Error loading resume:", error);
      showToast("An error occurred while loading the resume", "error");
    }
  }
  

  // Render template in the editor
  function renderTemplate(template, content = null) {
    const resumePage = document.getElementById("resumePage")

    // Clear loading indicator and any existing content
    resumePage.innerHTML = ""

    // Remove any existing template styles
    const existingStyles = document.getElementById("template-styles")
    if (existingStyles) {
      existingStyles.remove()
    }

    // Add template styles
    const styleElement = document.createElement("style")
    styleElement.id = "template-styles"
    styleElement.textContent = template.css_styles
    document.head.appendChild(styleElement)

    // Add template HTML structure
    resumePage.innerHTML = template.html_structure

    // If content is provided, fill the template with it
    if (content) {
      fillTemplateWithContent(content)
    }

    // Make content editable
    makeContentEditable()

    // Update sidebar sections based on template
    updateSidebarSections()
  }

  // Fill template with content
  function fillTemplateWithContent(content) {
    // This function would populate the template with the resume content
    // The implementation depends on the structure of your content and templates

    // Example implementation:
    // Personal information
    if (content.personal) {
      const personal = content.personal
      const nameEl = document.querySelector(".resume-name")
      const titleEl = document.querySelector(".resume-title")
      const emailEl = document.querySelector(".personal-details .detail-item:nth-child(1) span")
      const phoneEl = document.querySelector(".personal-details .detail-item:nth-child(2) span")
      const locationEl = document.querySelector(".personal-details .detail-item:nth-child(3) span")
      const linkedinEl = document.querySelector(".personal-details .detail-item:nth-child(4) span")

      if (nameEl && personal.name) nameEl.textContent = personal.name
      if (titleEl && personal.title) titleEl.textContent = personal.title
      if (emailEl && personal.email) emailEl.textContent = personal.email
      if (phoneEl && personal.phone) phoneEl.textContent = personal.phone
      if (locationEl && personal.location) locationEl.textContent = personal.location
      if (linkedinEl && personal.linkedin) linkedinEl.textContent = personal.linkedin
    }

    // Summary
    if (content.summary) {
      const summaryEl = document.querySelector("#summary-section .summary-text")
      if (summaryEl) summaryEl.textContent = content.summary
    }

    // Experience
    if (content.experience && Array.isArray(content.experience)) {
      const experienceSection = document.querySelector("#experience-section .section-content")
      if (experienceSection) {
        // Clear existing items except the add button
        const addButton = experienceSection.querySelector(".add-item-btn")
        experienceSection.innerHTML = ""

        // Add experience items
        content.experience.forEach((job) => {
          const item = createExperienceItem()

          // Fill with content
          const title = item.querySelector("h3")
          const company = item.querySelector("h4")
          const date = item.querySelector(".experience-date span")
          const description = item.querySelector(".experience-description ul")

          if (title && job.title) title.textContent = job.title
          if (company && job.company) company.textContent = job.company
          if (date && job.date) date.textContent = job.date

          if (description && job.description && Array.isArray(job.description)) {
            description.innerHTML = ""
            job.description.forEach((bullet) => {
              const li = document.createElement("li")
              li.contentEditable = "true"
              li.textContent = bullet
              description.appendChild(li)
            })
          }

          experienceSection.appendChild(item)
        })

        // Add the "Add Experience" button back
        if (addButton) experienceSection.appendChild(addButton)
      }
    }

    // Education
    if (content.education && Array.isArray(content.education)) {
      const educationSection = document.querySelector("#education-section .section-content")
      if (educationSection) {
        // Clear existing items except the add button
        const addButton = educationSection.querySelector(".add-item-btn")
        educationSection.innerHTML = ""

        // Add education items
        content.education.forEach((edu) => {
          const item = createEducationItem()

          // Fill with content
          const degree = item.querySelector("h3")
          const school = item.querySelector("h4")
          const date = item.querySelector(".education-date span")
          const description = item.querySelector(".education-description")

          if (degree && edu.degree) degree.textContent = edu.degree
          if (school && edu.school) school.textContent = edu.school
          if (date && edu.date) date.textContent = edu.date

          if (description && edu.description) {
            description.innerHTML = ""
            const p = document.createElement("p")
            p.contentEditable = "true"
            p.textContent = edu.description
            description.appendChild(p)
          }

          educationSection.appendChild(item)
        })

        // Add the "Add Education" button back
        if (addButton) educationSection.appendChild(addButton)
      }
    }

    // Skills
    if (content.skills && Array.isArray(content.skills)) {
      const skillsContainer = document.querySelector("#skills-section .skills-container")
      if (skillsContainer) {
        // Clear existing skills except the add button
        const addButton = skillsContainer.querySelector(".add-skill-btn")
        skillsContainer.innerHTML = ""

        // Add skills
        content.skills.forEach((skill) => {
          const skillItem = document.createElement("div")
          skillItem.className = "skill-item"
          skillItem.contentEditable = "true"
          skillItem.textContent = skill
          skillsContainer.appendChild(skillItem)
        })

        // Add the "Add Skill" button back
        if (addButton) skillsContainer.appendChild(addButton)
      }
    }

    // Certifications
    if (content.certifications && Array.isArray(content.certifications)) {
      const certificationsSection = document.querySelector("#certifications-section .section-content")
      if (certificationsSection) {
        // Clear existing items except the add button
        const addButton = certificationsSection.querySelector(".add-item-btn")
        certificationsSection.innerHTML = ""

        // Add certification items
        content.certifications.forEach((cert) => {
          const item = createCertificationItem()

          // Fill with content
          const name = item.querySelector("h3")
          const year = item.querySelector("span")

          if (name && cert.name) name.textContent = cert.name
          if (year && cert.year) year.textContent = cert.year

          certificationsSection.appendChild(item)
        })

        // Add the "Add Certification" button back
        if (addButton) certificationsSection.appendChild(addButton)
      }
    }

    // Add other sections as needed
  }

  // Make content editable
  function makeContentEditable() {
    // Find all elements that should be editable
    const editableElements = document.querySelectorAll(
      ".resume-page h1, .resume-page h2, .resume-page h3, .resume-page h4, .resume-page p, .resume-page li, .resume-page .detail-item span, .resume-page .skill-item",
    )

    // Make them editable
    editableElements.forEach((element) => {
      element.contentEditable = "true"

      // Add input event listener to update save status
      element.addEventListener("input", () => {
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    })
  }

  // Update sidebar sections based on template
  function updateSidebarSections() {
    const sectionList = document.getElementById("sectionList")
    const resumeSections = document.querySelectorAll(".resume-section")

    // Clear existing sections
    sectionList.innerHTML = ""

    // Add sections from template
    resumeSections.forEach((section) => {
      const sectionId = section.id.replace("-section", "")
      const sectionTitle = section.querySelector(".section-header h2")?.textContent || "Section"

      const sidebarItem = document.createElement("div")
      sidebarItem.className = "section-item"
      sidebarItem.setAttribute("data-section", sectionId)

      sidebarItem.innerHTML = `
        <div class="section-drag-handle">
          <i class="fas fa-grip-lines"></i>
        </div>
        <div class="section-info">
          <span>${sectionTitle}</span>
        </div>
        <div class="section-actions">
          <button class="btn-icon" title="Edit Section">
            <i class="fas fa-cog"></i>
          </button>
        </div>
      `

      sectionList.appendChild(sidebarItem)

      // Add click event to navigate to section
      sidebarItem.addEventListener("click", function () {
        const sectionId = this.getAttribute("data-section")
        const targetSection = document.getElementById(sectionId + "-section")

        if (targetSection) {
          // Update active section in sidebar
          document.querySelector(".section-item.active")?.classList.remove("active")
          this.classList.add("active")

          // Update active section in resume
          document.querySelector(".resume-section.active")?.classList.remove("active")
          targetSection.classList.add("active")

          // Scroll to section
          targetSection.scrollIntoView({ behavior: "smooth", block: "center" })
        }
      })

      // Add click event to section config button
      const configBtn = sidebarItem.querySelector(".section-actions .btn-icon")
      configBtn.addEventListener("click", (e) => {
        e.stopPropagation()

        const sectionId = sidebarItem.getAttribute("data-section")
        const section = document.getElementById(sectionId + "-section")

        if (section) {
          const propertiesPanel = document.getElementById("propertiesPanel")
          updatePropertiesPanel(section)
          propertiesPanel.classList.add("active")
        }
      })
    })

    // Set first section as active
    const firstSection = sectionList.querySelector(".section-item")
    if (firstSection) {
      firstSection.classList.add("active")

      const sectionId = firstSection.getAttribute("data-section")
      const targetSection = document.getElementById(sectionId + "-section")

      if (targetSection) {
        targetSection.classList.add("active")
      }
    }
  }

  // Setup UI event listeners
  function setupUIEventListeners() {
    // Save button
    const saveBtn = document.getElementById("saveBtn")
    if (saveBtn) {
      saveBtn.addEventListener("click", saveResume)
    }

    // Document name change
    const documentNameEl = document.querySelector(".document-name")
    if (documentNameEl) {
      documentNameEl.addEventListener("input", () => {
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    }

    // Change template button
    const changeTemplateBtn = document.getElementById("changeTemplateBtn")
    if (changeTemplateBtn) {
      changeTemplateBtn.addEventListener("click", () => {
        const templateModal = document.getElementById("templateModal")
        if (templateModal) {
          loadTemplatesForModal()
          templateModal.classList.add("active")
        }
      })
    }

    // Template settings button
    const templateSettingsBtn = document.getElementById("templateSettingsBtn")
    if (templateSettingsBtn) {
      templateSettingsBtn.addEventListener("click", () => {
        const templateSettingsModal = document.getElementById("templateSettingsModal")
        if (templateSettingsModal) {
          templateSettingsModal.classList.add("active")
        }
      })
    }

    // Download buttons
    document.getElementById("downloadPdf")?.addEventListener("click", (e) => {
      e.preventDefault()
      downloadResume("pdf")
    })

    document.getElementById("downloadDocx")?.addEventListener("click", (e) => {
      e.preventDefault()
      downloadResume("docx")
    })

    document.getElementById("downloadTxt")?.addEventListener("click", (e) => {
      e.preventDefault()
      downloadResume("txt")
    })

    // Before unload event to warn about unsaved changes
    window.addEventListener("beforeunload", (e) => {
      if (isUnsaved) {
        e.preventDefault()
        e.returnValue = "You have unsaved changes. Are you sure you want to leave?"
        return e.returnValue
      }
    })
  }

  // Save resume
  async function saveResume() {
    try {
      // Show saving toast
      showLoading("Saving resume...")

      // Get resume data
      const resumeData = {
        name: document.querySelector(".document-name").textContent,
        template_id: currentTemplate.id,
        content: extractResumeContent(),
      }

      // If editing existing resume, add resume_id
      if (currentResumeId) {
        resumeData.resume_id = currentResumeId
      }

      console.log("res",resumeData)

      // Send data to server
      const response = await fetch("api/save-resume.php", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(resumeData),
      })

      const data = await response.json()
      console.log("data",data);


      hideLoading()

      if (data.success) {
        // Update current resume ID if new resume
        if (!currentResumeId) {
          currentResumeId = data.resume_id

          // Update URL without reloading
          const newUrl = new URL(window.location.href)
          newUrl.searchParams.delete("template")
          newUrl.searchParams.set("resume", currentResumeId)
          window.history.pushState({}, "", newUrl)
        }

        // Update save status
        updateSaveStatus("saved")
        isUnsaved = false

        showToast("Resume saved successfully!", "success")
      } else {
        showToast("Failed to save resume: " + data.message, "error")
      }
    } catch (error) {
      hideLoading()
      console.error("Error saving resume:", error)
      showToast("An error occurred while saving the resume", "error")
    }
  }

  // Extract resume content from the editor
  function extractResumeContent() {
    const content = {}

    // Personal information
    const personalSection = document.getElementById("personal-section")
    if (personalSection) {
      content.personal = {
        name: personalSection.querySelector(".resume-name")?.textContent || "",
        title: personalSection.querySelector(".resume-title")?.textContent || "",
        email: personalSection.querySelector(".detail-item:nth-child(1) span")?.textContent || "",
        phone: personalSection.querySelector(".detail-item:nth-child(2) span")?.textContent || "",
        location: personalSection.querySelector(".detail-item:nth-child(3) span")?.textContent || "",
        linkedin: personalSection.querySelector(".detail-item:nth-child(4) span")?.textContent || "",
      }
    }

    // Summary
    const summarySection = document.getElementById("summary-section")
    if (summarySection) {
      content.summary = summarySection.querySelector(".summary-text")?.textContent || ""
    }

    // Experience
    const experienceSection = document.getElementById("experience-section")
    if (experienceSection) {
      content.experience = []

      const experienceItems = experienceSection.querySelectorAll(".experience-item")
      experienceItems.forEach((item) => {
        const job = {
          title: item.querySelector("h3")?.textContent || "",
          company: item.querySelector("h4")?.textContent || "",
          date: item.querySelector(".experience-date span")?.textContent || "",
          description: [],
        }

        // Get bullet points
        const bullets = item.querySelectorAll(".experience-description ul li")
        bullets.forEach((bullet) => {
          job.description.push(bullet.textContent)
        })

        content.experience.push(job)
      })
    }

    // Education
    const educationSection = document.getElementById("education-section")
    if (educationSection) {
      content.education = []

      const educationItems = educationSection.querySelectorAll(".education-item")
      educationItems.forEach((item) => {
        const edu = {
          degree: item.querySelector("h3")?.textContent || "",
          school: item.querySelector("h4")?.textContent || "",
          date: item.querySelector(".education-date span")?.textContent || "",
          description: item.querySelector(".education-description p")?.textContent || "",
        }

        content.education.push(edu)
      })
    }

    // Skills
    const skillsSection = document.getElementById("skills-section")
    if (skillsSection) {
      content.skills = []

      const skillItems = skillsSection.querySelectorAll(".skill-item")
      skillItems.forEach((item) => {
        content.skills.push(item.textContent)
      })
    }

    // Certifications
    const certificationsSection = document.getElementById("certifications-section")
    if (certificationsSection) {
      content.certifications = []

      const certItems = certificationsSection.querySelectorAll(".certification-item")
      certItems.forEach((item) => {
        const cert = {
          name: item.querySelector("h3")?.textContent || "",
          year: item.querySelector("span")?.textContent || "",
        }

        content.certifications.push(cert)
      })
    }

    // Add other sections as needed

    return content
  }

  // Download resume
  function downloadResume(format) {
    // Check if resume is saved
    if (!currentResumeId) {
      showToast("Please save your resume before downloading", "error")
      return
    }

    // Show download toast
    showToast(`Preparing ${format.toUpperCase()} download...`)

    // Redirect to download endpoint
    window.location.href = `api/generate-pdf.php?id=${currentResumeId}&format=${format}`
  }

  // Load templates for modal
  async function loadTemplatesForModal() {
    const templatesGrid = document.getElementById("templatesGrid")

    if (!templatesGrid) return

    // Show loading indicator
    templatesGrid.innerHTML = `
      <div class="templates-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading templates...</p>
      </div>
    `

    try {
      // Fetch templates from API
      const response = await fetch("api/templates.php")
      const data = await response.json()

      if (data.success) {
        // Clear loading indicator
        templatesGrid.innerHTML = ""

        // Render templates
        data.templates.forEach((template) => {
          const templateCard = document.createElement("div")
          templateCard.className = "template-card"
          templateCard.setAttribute("data-id", template.id)
          templateCard.setAttribute("data-category", template.category.toLowerCase())

          templateCard.innerHTML = `
            <div class="template-preview">
              <img src="${template.thumbnail}" alt="${template.template_name}">
              <div class="template-overlay">
                <button class="btn btn-sm btn-primary use-template-btn">
                  <i class="fas fa-check"></i> Use Template
                </button>
              </div>
            </div>
            <div class="template-info">
              <h3>${template.name}</h3>
              <p>${template.description}</p>
            </div>
          `

          // Add click event to use template button
          const useBtn = templateCard.querySelector(".use-template-btn")
          useBtn.addEventListener("click", () => {
            changeTemplate(template.id)
          })

          templatesGrid.appendChild(templateCard)
        })

        // Add filter functionality
        setupTemplateFilters()
      } else {
        templatesGrid.innerHTML = `
          <div class="templates-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>Failed to load templates: ${data.message}</p>
            <button class="btn btn-primary retry-btn">Retry</button>
          </div>
        `

        const retryBtn = templatesGrid.querySelector(".retry-btn")
        if (retryBtn) {
          retryBtn.addEventListener("click", loadTemplatesForModal)
        }
      }
    } catch (error) {
      console.error("Error loading templates:", error)

      templatesGrid.innerHTML = `
        <div class="templates-error">
          <i class="fas fa-exclamation-circle"></i>
          <p>An error occurred while loading templates</p>
          <button class="btn btn-primary retry-btn">Retry</button>
        </div>
      `

      const retryBtn = templatesGrid.querySelector(".retry-btn")
      if (retryBtn) {
        retryBtn.addEventListener("click", loadTemplatesForModal)
      }
    }
  }

  // Setup template filters
  function setupTemplateFilters() {
    const filterButtons = document.querySelectorAll("#templateModal .filter-btn")
    const searchInput = document.getElementById("templateSearchModal")
    const templateCards = document.querySelectorAll("#templatesGrid .template-card")

    // Filter by category
    filterButtons.forEach((button) => {
      button.addEventListener("click", function () {
        // Update active button
        document.querySelector("#templateModal .filter-btn.active").classList.remove("active")
        this.classList.add("active")

        const category = this.getAttribute("data-category")
        const searchTerm = searchInput.value.toLowerCase().trim()

        filterTemplates(category, searchTerm)
      })
    })

    // Filter by search term
    if (searchInput) {
      searchInput.addEventListener("input", function () {
        const category = document.querySelector("#templateModal .filter-btn.active").getAttribute("data-category")
        const searchTerm = this.value.toLowerCase().trim()

        filterTemplates(category, searchTerm)
      })
    }

    // Filter templates function
    function filterTemplates(category, searchTerm) {
      templateCards.forEach((card) => {
        const templateCategory = card.getAttribute("data-category")
        const templateName = card.querySelector("h3").textContent.toLowerCase()
        const templateDesc = card.querySelector("p").textContent.toLowerCase()

        const matchesCategory = category === "all" || templateCategory === category
        const matchesSearch = templateName.includes(searchTerm) || templateDesc.includes(searchTerm)

        if (matchesCategory && matchesSearch) {
          card.style.display = ""
        } else {
          card.style.display = "none"
        }
      })
    }
  }

  // Change template
  async function changeTemplate(templateId) {
    try {
      // Show loading toast
      showLoading("Loading template...")

      // Close template modal
      document.getElementById("templateModal").classList.remove("active")

      // Get template data
      const response = await fetch(`api/get-template.php?id=${templateId}`)
      const data = await response.json()

      hideLoading()

      if (data.success) {
        // Extract current content
        const currentContent = extractResumeContent()

        // Update current template
        currentTemplate = data.template

        // Render new template with current content
        renderTemplate(currentTemplate, currentContent)

        // Update save status
        updateSaveStatus("unsaved")
        isUnsaved = true

        showToast("Template changed successfully!", "success")
      } else {
        showToast("Failed to change template: " + data.message, "error")
      }
    } catch (error) {
      hideLoading()
      console.error("Error changing template:", error)
      showToast("An error occurred while changing the template", "error")
    }
  }

  // Initialize template settings modal
  function initTemplateSettingsModal() {
    const modal = document.getElementById("templateSettingsModal")
    const closeBtn = modal.querySelector(".close-modal")
    const applyBtn = document.getElementById("applyTemplateSettingsBtn")
    const resetBtn = document.getElementById("resetTemplateBtn")

    // Close button
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        modal.classList.remove("active")
      })
    }

    // Apply button
    if (applyBtn) {
      applyBtn.addEventListener("click", () => {
        applyTemplateSettings()
        modal.classList.remove("active")
      })
    }

    // Reset button
    if (resetBtn) {
      resetBtn.addEventListener("click", () => {
        resetTemplateSettings()
      })
    }

    // Range slider value display
    const rangeSliders = modal.querySelectorAll(".range-slider input[type='range']")
    rangeSliders.forEach((slider) => {
      slider.addEventListener("input", function () {
        this.nextElementSibling.textContent = this.value
      })
    })
  }

  // Apply template settings
  function applyTemplateSettings() {
    // Get settings values
    const primaryColor = document.getElementById("primaryColor").value
    const accentColor = document.getElementById("accentColor").value
    const textColor = document.getElementById("textColor").value
    const headingFont = document.getElementById("headingFont").value
    const bodyFont = document.getElementById("bodyFont").value
    const fontSize = document.getElementById("fontSize").value
    const sectionSpacing = document.getElementById("templateSectionSpacing").value
    const pageMargins = document.getElementById("pageMargins").value
    const lineHeight = document.getElementById("lineHeight").value

    // Get style element
    const styleElement = document.getElementById("template-styles")

    if (styleElement) {
      // Create CSS variables
      const cssVars = `
        :root {
          --primary-color: ${primaryColor};
          --accent-color: ${accentColor};
          --text-color: ${textColor};
          --section-spacing: ${sectionSpacing * 10}px;
          --page-margin: ${pageMargins * 10}px;
          --line-height: ${lineHeight};
        }
        
        .resume-page {
          font-family: ${bodyFont === "default" ? "inherit" : bodyFont}, sans-serif;
          color: var(--text-color);
          line-height: var(--line-height);
          padding: var(--page-margin);
        }
        
        .resume-page h1, .resume-page h2, .resume-page h3, .resume-page h4 {
          font-family: ${headingFont === "default" ? "inherit" : headingFont}, sans-serif;
          color: var(--primary-color);
        }
        
        .resume-section {
          margin-bottom: var(--section-spacing);
        }
        
        .section-header h2 {
          color: var(--primary-color);
          border-bottom: 2px solid var(--accent-color);
        }
        
        .skill-item {
          background-color: var(--accent-color);
          color: white;
        }
      `

      // Append to existing styles
      styleElement.textContent += cssVars

      // Update save status
      updateSaveStatus("unsaved")
      isUnsaved = true

      showToast("Template settings applied", "success")
    }
  }

  // Reset template settings
  function resetTemplateSettings() {
    // Reset color inputs
    document.getElementById("primaryColor").value = "#194B5B"
    document.getElementById("accentColor").value = "#2A9D8F"
    document.getElementById("textColor").value = "#333333"

    // Reset font selects
    document.getElementById("headingFont").value = "default"
    document.getElementById("bodyFont").value = "default"
    document.getElementById("fontSize").value = "medium"

    // Reset range sliders
    document.getElementById("templateSectionSpacing").value = "2"
    document.getElementById("templateSectionSpacing").nextElementSibling.textContent = "2"

    document.getElementById("pageMargins").value = "2"
    document.getElementById("pageMargins").nextElementSibling.textContent = "2"

    document.getElementById("lineHeight").value = "1.5"
    document.getElementById("lineHeight").nextElementSibling.textContent = "1.5"

    // Get style element
    const styleElement = document.getElementById("template-styles")

    if (styleElement && currentTemplate) {
      // Reset to original template styles
      styleElement.textContent = currentTemplate.css_styles

      showToast("Template settings reset to default", "success")
    }
  }

  // Initialize template modal
  function initTemplateModal() {
    const modal = document.getElementById("templateModal")
    const closeBtn = modal.querySelector(".close-modal")
    const cancelBtn = document.getElementById("closeTemplateModalBtn")

    // Close button
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        modal.classList.remove("active")
      })
    }

    // Cancel button
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => {
        modal.classList.remove("active")
      })
    }
  }

  // Initialize preview modal
  function initPreviewModal() {
    const previewBtn = document.getElementById("previewBtn")
    const previewModal = document.getElementById("previewModal")
    const closePreviewBtn = document.getElementById("closePreviewBtn")
    const closeModalBtn = previewModal.querySelector(".close-modal")
    const downloadPreviewBtn = document.getElementById("downloadPreviewBtn")

    if (previewBtn && previewModal) {
      previewBtn.addEventListener("click", () => {
        // Update preview frame with current content
        const previewFrame = document.getElementById("previewFrame")
        if (previewFrame) {
          const resumeContent = document.querySelector(".resume-page").cloneNode(true)

          // Remove edit buttons from preview
          resumeContent.querySelectorAll(".item-actions, .add-item-btn, .upload-btn").forEach((el) => {
            el.remove()
          })

          // Wait for iframe to load
          previewFrame.onload = () => {
            const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document

            // Add template styles to iframe
            const styleElement = document.getElementById("template-styles")
            if (styleElement) {
              const iframeStyle = frameDoc.createElement("style")
              iframeStyle.textContent = styleElement.textContent
              frameDoc.head.appendChild(iframeStyle)
            }

            // Add content to iframe
            const previewContainer = frameDoc.querySelector(".preview-container")
            if (previewContainer) {
              previewContainer.innerHTML = ""
              previewContainer.appendChild(resumeContent)
            }
          }

          // Reload iframe to trigger onload
          previewFrame.src = previewFrame.src
        }

        previewModal.classList.add("active")
      })
    }

    // Close modal buttons
    if (closePreviewBtn) {
      closePreviewBtn.addEventListener("click", () => {
        previewModal.classList.remove("active")
      })
    }

    if (closeModalBtn) {
      closeModalBtn.addEventListener("click", () => {
        previewModal.classList.remove("active")
      })
    }

    // Download button in preview
    if (downloadPreviewBtn) {
      downloadPreviewBtn.addEventListener("click", () => {
        downloadResume("pdf")
      })
    }
  }

  // Initialize toolbar
  function initToolbar() {
    const toolbarButtons = document.querySelectorAll(".toolbar-btn")

    toolbarButtons.forEach((button) => {
      button.addEventListener("click", function () {
        const command = getCommandFromButton(this);

        if (command) {
          document.execCommand(command, false, null);
          this.classList.toggle("active");
          updateSaveStatus("unsaved");
          isUnsaved = true;
        }

      })
    }
  )

    // Handle font selector
    const fontSelector = document.querySelector(".font-selector")
    if (fontSelector) {
      fontSelector.addEventListener("change", function () {
        const font = this.value
        if (font !== "default") {
          document.execCommand("fontName", false, font)
          updateSaveStatus("unsaved")
          isUnsaved = true
        }
      });
    }

    // Handle font size selector
    const fontSizeSelector = document.querySelector(".font-size-selector")
    if (fontSizeSelector) {
      fontSizeSelector.addEventListener("change", function () {
        const size = this.value
        let fontSize

        switch (size) {
          case "small":
            fontSize = "2"
            break
          case "medium":
            fontSize = "3"
            break
          case "large":
            fontSize = "4"
            break
          default:
            fontSize = "3"
        }

        if (size !== "default") {
          document.execCommand("fontSize", false, fontSize)
          updateSaveStatus("unsaved")
          isUnsaved = true
        }
      })
    }
  }

  // Get command from toolbar button
  function getCommandFromButton(button) {
    // Get the command based on the button's icon or title
    const icon = button.querySelector("i")

    if (!icon) return null

    const iconClass = icon.className

    if (iconClass.includes("fa-bold")) return "bold"
    if (iconClass.includes("fa-italic")) return "italic"
    if (iconClass.includes("fa-underline")) return "underline"
    if (iconClass.includes("fa-align-left")) return "justifyLeft"
    if (iconClass.includes("fa-align-center")) return "justifyCenter"
    if (iconClass.includes("fa-align-right")) return "justifyRight"
    if (iconClass.includes("fa-list-ul")) return "insertUnorderedList"
    if (iconClass.includes("fa-list-ol")) return "insertOrderedList"

    // For color buttons, open a color picker
    if (iconClass.includes("fa-palette")) {
      openColorPicker("foreColor")
      return null
    }
    if (iconClass.includes("fa-fill-drip")) {
      openColorPicker("hiliteColor")
      return null
    }

    return null
  }

  // Open color picker
  function openColorPicker(command) {
    // Create a temporary input element
    const colorInput = document.createElement("input")
    colorInput.type = "color"

    colorInput.addEventListener("change", function () {
      document.execCommand(command, false, this.value)
      updateSaveStatus("unsaved")
      isUnsaved = true
    })

    colorInput.click()
  }

  // Setup section dragging
  function setupSectionDragging() {
    const sectionList = document.getElementById("sectionList")
    let draggedItem = null

    // Set up drag events for each section item
    document.querySelectorAll(".section-item").forEach((item) => {
      const dragHandle = item.querySelector(".section-drag-handle")

      dragHandle.addEventListener("mousedown", (e) => {
        // Prevent default to avoid text selection during drag
        e.preventDefault()

        draggedItem = item
        item.classList.add("dragging")

        // Store original position for reference
        const rect = item.getBoundingClientRect()
        const offsetY = e.clientY - rect.top

        function moveItem(e) {
          if (!draggedItem) return

          const containerRect = sectionList.getBoundingClientRect()
          const y = e.clientY - containerRect.top - offsetY

          // Set position with transform for smooth movement
          draggedItem.style.transform = `translateY(${y}px)`

          // Determine new position in list
          const siblings = [...sectionList.querySelectorAll(".section-item:not(.dragging)")]

          const nextSibling = siblings.find((sibling) => {
            const siblingRect = sibling.getBoundingClientRect()
            const siblingMiddle = siblingRect.top + siblingRect.height / 2
            return e.clientY < siblingMiddle
          })

          if (nextSibling) {
            sectionList.insertBefore(draggedItem, nextSibling)
          } else {
            sectionList.appendChild(draggedItem)
          }

          // Update the order of sections in the resume
          updateSectionOrder()
        }

        function dropItem() {
          if (!draggedItem) return

          draggedItem.classList.remove("dragging")
          draggedItem.style.transform = ""
          draggedItem = null

          // Update status to unsaved
          updateSaveStatus("unsaved")
          isUnsaved = true

          document.removeEventListener("mousemove", moveItem)
          document.removeEventListener("mouseup", dropItem)
        }

        document.addEventListener("mousemove", moveItem)
        document.addEventListener("mouseup", dropItem)
      })
    })
  }

  // Update section order
  function updateSectionOrder() {
    // Get all sections in their new order
    const sectionItems = document.querySelectorAll(".section-item")
    const resumePage = document.querySelector(".resume-page")

    // Rearrange sections in the resume according to sidebar order
    sectionItems.forEach((item) => {
      const sectionId = item.getAttribute("data-section") + "-section"
      const section = document.getElementById(sectionId)
      if (section) {
        resumePage.appendChild(section)
      }
    })
  }

  // Setup properties panel
  function setupPropertiesPanel() {
    const propertiesPanel = document.getElementById("propertiesPanel")
    const closePropertiesBtn = document.getElementById("closePropertiesBtn")
    const applyChangesBtn = document.getElementById("applyChangesBtn")
    const deleteSectionBtn = document.getElementById("deleteSectionBtn")

    // Close properties panel
    if (closePropertiesBtn) {
      closePropertiesBtn.addEventListener("click", () => {
        propertiesPanel.classList.remove("active")
      })
    }

    // Apply changes button
    if (applyChangesBtn) {
      applyChangesBtn.addEventListener("click", () => {
        applyPropertyChanges()
        propertiesPanel.classList.remove("active")
      })
    }

    // Delete section button
    if (deleteSectionBtn) {
      deleteSectionBtn.addEventListener("click", () => {
        const sectionId = propertiesPanel.getAttribute("data-editing-section")
        if (sectionId && confirm("Are you sure you want to delete this section?")) {
          deleteSection(sectionId)
          propertiesPanel.classList.remove("active")
        }
      })
    }

    // Range slider value display
    const rangeSliders = propertiesPanel.querySelectorAll(".range-slider input[type='range']")
    rangeSliders.forEach((slider) => {
      slider.addEventListener("input", function () {
        this.nextElementSibling.textContent = this.value
      })
    })

    // Number input buttons
    const numberInputs = propertiesPanel.querySelectorAll(".number-input")
    numberInputs.forEach((container) => {
      const input = container.querySelector("input[type='number']")
      const downBtn = container.querySelector(".number-down")
      const upBtn = container.querySelector(".number-up")

      if (input && downBtn && upBtn) {
        downBtn.addEventListener("click", () => {
          if (input.value > input.min) {
            input.value = Number.parseInt(input.value) - 1
          }
        })

        upBtn.addEventListener("click", () => {
          input.value = Number.parseInt(input.value) + 1
        })
      }
    })
  }

  // Update properties panel with section info
  function updatePropertiesPanel(section) {
    const propertiesPanel = document.getElementById("propertiesPanel")
    const sectionTitle = document.getElementById("sectionTitle")
    const sectionVisibility = document.getElementById("sectionVisibility")
    const sectionSpacing = document.getElementById("sectionSpacing")
    const titleStyle = document.getElementById("titleStyle")
    const sectionBorder = document.getElementById("sectionBorder")
    const sectionOrder = document.getElementById("sectionOrder")
    const columnLayout = document.getElementById("columnLayout")

    // Set section ID for reference
    propertiesPanel.setAttribute("data-editing-section", section.id)

    // Set section title
    if (sectionTitle) {
      const headerTitle = section.querySelector(".section-header h2")
      sectionTitle.value = headerTitle ? headerTitle.textContent : ""
    }

    // Set visibility
    if (sectionVisibility) {
      sectionVisibility.checked = !section.classList.contains("hidden")
    }

    // Set spacing
    if (sectionSpacing) {
      // Get spacing from section's margin-bottom or default to 2
      const computedStyle = window.getComputedStyle(section)
      const currentSpacing = Number.parseInt(computedStyle.marginBottom) / 10 || 2
      sectionSpacing.value = currentSpacing
      sectionSpacing.nextElementSibling.textContent = currentSpacing
    }

    // Set title style
    if (titleStyle) {
      const headerTitle = section.querySelector(".section-header h2")
      if (headerTitle) {
        if (headerTitle.style.fontWeight === "700") {
          titleStyle.value = "bold"
        } else if (headerTitle.style.borderBottom) {
          titleStyle.value = "underlined"
        } else if (headerTitle.style.textAlign === "center") {
          titleStyle.value = "centered"
        } else {
          titleStyle.value = "default"
        }
      }
    }

    // Set border style
    if (sectionBorder) {
      if (
        section.style.border === "1px solid var(--border-color)" ||
        section.style.border === "1px solid rgb(224, 224, 224)"
      ) {
        sectionBorder.value = "full"
      } else if (
        section.style.borderBottom === "1px solid var(--border-color)" ||
        section.style.borderBottom === "1px solid rgb(224, 224, 224)"
      ) {
        sectionBorder.value = "bottom"
      } else {
        sectionBorder.value = "none"
      }
    }

    // Set order
    if (sectionOrder) {
      // Get section's position in the resume
      const allSections = document.querySelectorAll(".resume-section")
      let index = 0
      allSections.forEach((s, i) => {
        if (s === section) index = i + 1
      })
      sectionOrder.value = index
    }

    // Set column layout
    if (columnLayout) {
      if (section.classList.contains("two-columns")) {
        columnLayout.value = "double"
      } else {
        columnLayout.value = "single"
      }
    }
  }

  // Apply property changes
  function applyPropertyChanges() {
    const propertiesPanel = document.getElementById("propertiesPanel")
    const sectionId = propertiesPanel.getAttribute("data-editing-section")
    const section = document.getElementById(sectionId)

    if (!section) return

    const sectionTitle = document.getElementById("sectionTitle")
    const sectionVisibility = document.getElementById("sectionVisibility")
    const sectionSpacing = document.getElementById("sectionSpacing")
    const titleStyle = document.getElementById("titleStyle")
    const sectionBorder = document.getElementById("sectionBorder")
    const sectionOrder = document.getElementById("sectionOrder")
    const columnLayout = document.getElementById("columnLayout")

    // Update section title
    if (sectionTitle) {
      const headerTitle = section.querySelector(".section-header h2")
      if (headerTitle) {
        headerTitle.textContent = sectionTitle.value

        // Also update sidebar item text
        const sidebarItem = document.querySelector(
          `.section-item[data-section="${sectionId.replace("-section", "")}"] .section-info span`,
        )
        if (sidebarItem) {
          sidebarItem.textContent = sectionTitle.value
        }
      }
    }

    // Update visibility
    if (sectionVisibility) {
      if (sectionVisibility.checked) {
        section.classList.remove("hidden")
      } else {
        section.classList.add("hidden")
      }
    }

    // Update spacing
    if (sectionSpacing) {
      section.style.marginBottom = `${sectionSpacing.value * 10}px`
    }

    // Update title style
    if (titleStyle) {
      const headerTitle = section.querySelector(".section-header h2")
      if (headerTitle) {
        // Reset styles
        headerTitle.style.fontWeight = ""
        headerTitle.style.borderBottom = ""
        headerTitle.style.paddingBottom = ""
        headerTitle.style.textAlign = ""

        // Apply selected style
        switch (titleStyle.value) {
          case "bold":
            headerTitle.style.fontWeight = "700"
            break
          case "underlined":
            headerTitle.style.borderBottom = "2px solid var(--primary-color)"
            headerTitle.style.paddingBottom = "0.5rem"
            break
          case "centered":
            headerTitle.style.textAlign = "center"
            break
        }
      }
    }

    // Update border
    if (sectionBorder) {
      // Reset borders
      section.style.border = "none"
      section.style.borderBottom = "none"
      section.style.padding = ""
      section.style.paddingBottom = ""
      section.style.borderRadius = ""

      // Apply selected border
      switch (sectionBorder.value) {
        case "bottom":
          section.style.borderBottom = "1px solid var(--border-color)"
          section.style.paddingBottom = "1.5rem"
          break
        case "full":
          section.style.border = "1px solid var(--border-color)"
          section.style.padding = "1rem"
          section.style.borderRadius = "var(--border-radius)"
          break
      }
    }

    // Update order
    if (sectionOrder) {
      const resumePage = document.querySelector(".resume-page")
      const allSections = document.querySelectorAll(".resume-section")
      const newIndex = Number.parseInt(sectionOrder.value) - 1

      if (newIndex >= 0 && newIndex < allSections.length) {
        if (newIndex === allSections.length - 1) {
          resumePage.appendChild(section)
        } else {
          const referenceNode = [...allSections][newIndex]
          resumePage.insertBefore(section, referenceNode)
        }

        // Also update the order in the sidebar
        updateSidebarOrder()
      }
    }

    // Update column layout
    if (columnLayout) {
      section.classList.remove("two-columns")

      if (columnLayout.value === "double") {
        section.classList.add("two-columns")
      }
    }

    // Update save status
    updateSaveStatus("unsaved")
    isUnsaved = true
  }

  // Delete section
  function deleteSection(sectionId) {
    const section = document.getElementById(sectionId)
    const sidebarItem = document.querySelector(`.section-item[data-section="${sectionId.replace("-section", "")}"]`)

    if (section) {
      section.remove()
    }

    if (sidebarItem) {
      sidebarItem.remove()
    }

    // Update save status
    updateSaveStatus("unsaved")
    isUnsaved = true
  }

  // Update sidebar order
  function updateSidebarOrder() {
    const resumeSections = document.querySelectorAll(".resume-section")
    const sectionList = document.getElementById("sectionList")

    // Create a temporary array to hold sections in new order
    const orderedItems = []

    resumeSections.forEach((section) => {
      const sectionId = section.id.replace("-section", "")
      const sidebarItem = document.querySelector(`.section-item[data-section="${sectionId}"]`)
      if (sidebarItem) {
        orderedItems.push(sidebarItem)
      }
    })

    // Clear and repopulate the section list
    orderedItems.forEach((item) => {
      sectionList.appendChild(item)
    })
  }

  // Setup add item buttons
  function setupAddItemButtons() {
    // Add experience item
    const addExperienceBtn = document.querySelector("#experience-section .add-item-btn")
    if (addExperienceBtn) {
      addExperienceBtn.addEventListener("click", function () {
        const experienceList = this.parentElement
        const newItem = createExperienceItem()
        experienceList.insertBefore(newItem, this)
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    }

    // Add education item
    const addEducationBtn = document.querySelector("#education-section .add-item-btn")
    if (addEducationBtn) {
      addEducationBtn.addEventListener("click", function () {
        const educationList = this.parentElement
        const newItem = createEducationItem()
        educationList.insertBefore(newItem, this)
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    }

    // Add certification item
    const addCertificationBtn = document.querySelector("#certifications-section .add-item-btn")
    if (addCertificationBtn) {
      addCertificationBtn.addEventListener("click", function () {
        const certificationList = this.parentElement
        const newItem = createCertificationItem()
        certificationList.insertBefore(newItem, this)
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    }

    // Add skill item
    const addSkillBtn = document.querySelector(".add-skill-btn")
    if (addSkillBtn) {
      addSkillBtn.addEventListener("click", function () {
        const skillsContainer = this.parentElement
        const newSkill = document.createElement("div")
        newSkill.className = "skill-item"
        newSkill.contentEditable = "true"
        newSkill.textContent = "New Skill"
        skillsContainer.insertBefore(newSkill, this)
        newSkill.focus()
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    }

    // Add new section
    const addSectionBtn = document.getElementById("addSectionBtn")
    if (addSectionBtn) {
      addSectionBtn.addEventListener("click", () => {
        // Create a prompt for section name
        const sectionName = prompt("Enter name for new section:")
        if (sectionName) {
          addNewSection(sectionName)
          updateSaveStatus("unsaved")
          isUnsaved = true
        }
      })
    }
  }

  // Create experience item
  function createExperienceItem() {
    const item = document.createElement("div")
    item.className = "experience-item"

    item.innerHTML = `
      <div class="experience-header">
        <div>
          <h3 contenteditable="true">Job Title</h3>
          <h4 contenteditable="true">Company Name</h4>
        </div>
        <div class="experience-date">
          <span contenteditable="true">Start Date - End Date</span>
        </div>
      </div>
      <div class="experience-description">
        <ul>
          <li contenteditable="true">Responsibility or achievement</li>
          <li contenteditable="true">Responsibility or achievement</li>
        </ul>
      </div>
      <div class="item-actions">
        <button class="btn-icon" title="Add Item">
          <i class="fas fa-plus"></i>
        </button>
        <button class="btn-icon" title="Remove Item">
          <i class="fas fa-trash-alt"></i>
        </button>
      </div>
    `

    setupItemButtons(item)
    return item
  }

  // Create education item
  function createEducationItem() {
    const item = document.createElement("div")
    item.className = "education-item"

    item.innerHTML = `
      <div class="education-header">
        <div>
          <h3 contenteditable="true">Degree</h3>
          <h4 contenteditable="true">Institution</h4>
        </div>
        <div class="education-date">
          <span contenteditable="true">Year - Year</span>
        </div>
      </div>
      <div class="education-description">
        <p contenteditable="true">Description</p>
      </div>
      <div class="item-actions">
        <button class="btn-icon" title="Add Item">
          <i class="fas fa-plus"></i>
        </button>
        <button class="btn-icon" title="Remove Item">
          <i class="fas fa-trash-alt"></i>
        </button>
      </div>
    `

    setupItemButtons(item)
    return item
  }

  // Create certification item
  function createCertificationItem() {
    const item = document.createElement("div")
    item.className = "certification-item"

    item.innerHTML = `
      <div class="certification-header">
        <h3 contenteditable="true">Certification Name</h3>
        <span contenteditable="true">Year</span>
      </div>
      <div class="item-actions">
        <button class="btn-icon" title="Add Item">
          <i class="fas fa-plus"></i>
        </button>
        <button class="btn-icon" title="Remove Item">
          <i class="fas fa-trash-alt"></i>
        </button>
      </div>
    `

    setupItemButtons(item)
    return item
  }

  // Setup item buttons
  function setupItemButtons(item) {
    // Add button functionality
    const addBtn = item.querySelector(".item-actions .btn-icon[title='Add Item']")
    const removeBtn = item.querySelector(".item-actions .btn-icon[title='Remove Item']")

    if (addBtn) {
      addBtn.addEventListener("click", () => {
        const itemType = item.className.split(" ")[0] // experience-item, education-item, etc.
        let newItem

        switch (itemType) {
          case "experience-item":
            newItem = createExperienceItem()
            break
          case "education-item":
            newItem = createEducationItem()
            break
          case "certification-item":
            newItem = createCertificationItem()
            break
        }

        if (newItem) {
          item.parentNode.insertBefore(newItem, item.nextSibling)
          updateSaveStatus("unsaved")
          isUnsaved = true
        }
      })
    }

    if (removeBtn) {
      removeBtn.addEventListener("click", () => {
        if (confirm("Are you sure you want to remove this item?")) {
          item.remove()
          updateSaveStatus("unsaved")
          isUnsaved = true
        }
      })
    }

    // Make content editable elements update save status
    item.querySelectorAll("[contenteditable='true']").forEach((element) => {
      element.addEventListener("input", () => {
        updateSaveStatus("unsaved")
        isUnsaved = true
      })
    })
  }

  // Add new section
  function addNewSection(sectionName) {
    // Create section ID from name
    const sectionId = sectionName.toLowerCase().replace(/\s+/g, "-")

    // Create sidebar item
    const sectionList = document.getElementById("sectionList")
    const sidebarItem = document.createElement("div")
    sidebarItem.className = "section-item"
    sidebarItem.setAttribute("data-section", sectionId)

    sidebarItem.innerHTML = `
      <div class="section-drag-handle">
        <i class="fas fa-grip-lines"></i>
      </div>
      <div class="section-info">
        <span>${sectionName}</span>
      </div>
      <div class="section-actions">
        <button class="btn-icon" title="Edit Section">
          <i class="fas fa-cog"></i>
        </button>
      </div>
    `

    sectionList.appendChild(sidebarItem)

    // Create resume section
    const resumePage = document.querySelector(".resume-page")
    const section = document.createElement("div")
    section.className = "resume-section"
    section.id = sectionId + "-section"

    section.innerHTML = `
      <div class="section-header">
        <h2 contenteditable="true">${sectionName}</h2>
      </div>
      <div class="section-content">
        <p contenteditable="true">Click to add content...</p>
      </div>
    `

    resumePage.appendChild(section)

    // Add event listener to the new sidebar item
    sidebarItem.addEventListener("click", function () {
      const sectionId = this.getAttribute("data-section")
      const targetSection = document.getElementById(sectionId + "-section")

      if (targetSection) {
        // Update active section in sidebar
        document.querySelector(".section-item.active")?.classList.remove("active")
        this.classList.add("active")

        // Update active section in resume
        document.querySelector(".resume-section.active")?.classList.remove("active")
        targetSection.classList.add("active")

        // Scroll to section
        targetSection.scrollIntoView({ behavior: "smooth", block: "center" })
      }
    })

    // Add event listener to the section config button
    const configBtn = sidebarItem.querySelector(".section-actions .btn-icon")
    configBtn.addEventListener("click", (e) => {
      e.stopPropagation()

      const sectionId = sidebarItem.getAttribute("data-section")
      const section = document.getElementById(sectionId + "-section")

      if (section) {
        const propertiesPanel = document.getElementById("propertiesPanel")
        updatePropertiesPanel(section)
        propertiesPanel.classList.add("active")
      }
    })

    // Activate the new section
    sidebarItem.click()
  }

  // Setup section buttons
  function setupSectionButtons() {
    // Setup all existing item action buttons
    document.querySelectorAll(".experience-item, .education-item, .certification-item").forEach((item) => {
      setupItemButtons(item)
    })
  }

  // Update save status
  function updateSaveStatus(status) {
    const statusIndicator = document.querySelector(".status-indicator")

    if (statusIndicator) {
      statusIndicator.className = "status-indicator " + status

      const icon = statusIndicator.querySelector("i")
      const text = statusIndicator.querySelector("span") || statusIndicator

      if (status === "saved") {
        icon.className = "fas fa-check-circle"
        text.textContent = " Saved"
      } else {
        icon.className = "fas fa-exclamation-circle"
        text.textContent = " Unsaved changes"
      }
    }
  }

  // Show loading indicator
  function showLoading(message = "Loading...") {
    let loadingEl = document.getElementById("loading-indicator")

    if (!loadingEl) {
      loadingEl = document.createElement("div")
      loadingEl.id = "loading-indicator"
      loadingEl.innerHTML = `
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div class="loading-message"></div>
      `

      // Add styles
      const style = document.createElement("style")
      style.textContent = `
        #loading-indicator {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 9999;
          color: white;
        }
        .loading-spinner {
          font-size: 3rem;
          margin-bottom: 1rem;
        }
        .loading-message {
          font-size: 1.2rem;
        }
      `
      document.head.appendChild(style)

      document.body.appendChild(loadingEl)
    }

    loadingEl.querySelector(".loading-message").textContent = message
    loadingEl.style.display = "flex"
  }

  // Hide loading indicator
  function hideLoading() {
    const loadingEl = document.getElementById("loading-indicator")
    if (loadingEl) {
      loadingEl.style.display = "none"
    }
  }

  // Show toast notification
  function showToast(message, type = "info") {
    // Create toast element if it doesn't exist
    let toast = document.getElementById("toast-notification")
    if (!toast) {
      toast = document.createElement("div")
      toast.id = "toast-notification"
      document.body.appendChild(toast)

      // Add CSS for toast
      const style = document.createElement("style")
      style.textContent = `
        #toast-notification {
          position: fixed;
          bottom: 20px;
          right: 20px;
          padding: 12px 20px;
          background-color: var(--primary-color);
          color: white;
          border-radius: var(--border-radius);
          box-shadow: 0 3px 6px rgba(0,0,0,0.16);
          z-index: 1000;
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.3s ease;
        }
        #toast-notification.show {
          opacity: 1;
          transform: translateY(0);
        }
        #toast-notification.info {
          background-color: var(--primary-color);
        }
        #toast-notification.success {
          background-color: #2a9d8f;
        }
        #toast-notification.error {
          background-color: #e63946;
        }
      `
      document.head.appendChild(style)
    }

    // Set message and show
    toast.textContent = message
    toast.className = type + " show"

    // Hide after 3 seconds
    setTimeout(() => {
      toast.classList.remove("show")
    }, 3000)
  }
})
