/* Create Resume Styles */
.create-resume-container {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.template-preview, .resume-options {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.template-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.template-image {
    height: 300px;
    overflow: hidden;
    border-bottom: 1px solid #e0e0e0;
}

.template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-info {
    padding: 1rem;
}

.template-info h3 {
    margin-top: 0;
    color: #333;
}

.template-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.resume-options-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #333;
}

.color-schemes, .font-families {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.color-option, .font-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-option.active, .font-option.active {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.1);
}

.color-option input, .font-option input {
    display: none;
}

.color-swatch {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-bottom: 5px;
    border: 1px solid #ddd;
}

.color-name, .font-name {
    font-size: 0.8rem;
    color: #666;
}

.font-preview {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: #333;
}

.section-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.section-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.section-option:hover {
    background-color: #f9f9f9;
}

.section-option input {
    margin: 0;
}

.form-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .create-resume-container {
        flex-direction: column;
    }
}

/* CSS Variables */
:root {
    --blue: #3498db;
    --gray: #7f8c8d;
    --black: #34495e;
    --green: #2ecc71;
    --primary-color: #3498db;
    --primary-rgb: 52, 152, 219;
}