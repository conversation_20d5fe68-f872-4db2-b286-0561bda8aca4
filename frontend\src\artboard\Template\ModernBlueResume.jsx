import React from "react";
import Header from "./components/Header";
import Summary from "./components/Summary";
import Skills from "./components/Skills";
import Education from "./components/Education";
import Experience from "./components/Experience";
import Volunteering from "./components/Volunteering";
import Certifications from "./components/Certifications";
import Awards from "./components/Awards";
import Projects from "./components/Projects";
import Publications from "./components/Publications";
import Languages from "./components/Languages";
import Interests from "./components/Interests";
import References from "./components/References";
import Profiles from "./components/Profiles";

const resumeData = {
  basics: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    headline: "Full Stack Developer",
    phone: "+91 9749038945",
    email: "<EMAIL>",
    address: "West Bengal, India",
    website: "https://ganesh.dev",
    linkedin: "https://linkedin.com/in/ganeshshit",
    twitter: "https://twitter.com/ganeshshit"
  },
  summary:
    "Aspiring Full-Stack Developer skilled in JavaScript, React, Node.js, and MongoDB. Experienced in building responsive applications and working in agile teams.",
  skills: [
    "JavaScript", "React", "Node.js", "MongoDB", "Tailwind CSS", "Git"
  ],
  education: [
    {
      degree: "B.Tech in Computer Science",
      institution: "NIST, Odisha",
      gpa: "9.2 GPA",
      graduation: "2024"
    }
  ],
  experience: [
    {
      company: "Zetpeak",
      location: "Bangalore",
      title: "Frontend Intern",
      date: "June 2023 - Sep 2023",
      bullets: [
        "Developed a responsive company website using React.js and Tailwind.",
        "Improved page speed by optimizing images and reducing component size."
      ]
    },
    {
      company: "TechVritti",
      location: "Remote",
      title: "Web Developer Intern",
      date: "Mar 2023 - Present",
      bullets: [
        "Worked on dashboard components and integrated REST APIs.",
        "Collaborated in GitHub using pull requests and version control best practices."
      ]
    }
  ],
  volunteering: [
    {
      organization: "NIST Cloud Club",
      position: "Operations Lead",
      date: "Feb 2023 - Present",
      summary: "Led club operations, organized cloud bootcamps and sessions for 100+ students."
    }
  ],
  certifications: [
    {
      title: "Meta Frontend Developer",
      issuer: "Coursera",
      date: "Aug 2023",
      summary: "Completed 6-course certification on frontend development with React.js"
    }
  ],
  awards: [
    {
      title: "Best Project - DevFest",
      awarder: "Google DevFest 2023",
      date: "Nov 2023",
      summary: "Awarded for building an AI-powered resume builder in 24-hour hackathon."
    }
  ],
  projects: [
    {
      title: "Resume Builder App",
      date: "2023",
      summary: "Built a dynamic resume generator using MERN stack with live preview and export."
    }
  ],
  publications: [
    {
      title: "Optimizing Frontend Load Time",
      publisher: "Medium",
      date: "Jan 2024",
      summary: "Published best practices for improving React.js application performance."
    }
  ],
  languages: ["English", "Hindi", "Bengali"],
  interests: ["UI/UX Design", "Open Source", "Hackathons"],
  references: [
    {
      name: "Ravi Kumar",
      position: "Team Lead at Zetpeak",
      contact: "<EMAIL>"
    }
  ],
  profiles: [
    { platform: "GitHub", username: "coderGanesh", url: "https://github.com/coderGanesh" },
    { platform: "LinkedIn", username: "ganeshshit", url: "https://linkedin.com/in/ganeshshit" }
  ]
};

const ModernBlueResume = () => {
  const {
    basics,
    summary,
    skills,
    education,
    experience,
    volunteering,
    certifications,
    awards,
    projects,
    publications,
    languages,
    interests,
    references,
    profiles
  } = resumeData;

  return (
    <div className="w-[794px] mx-auto bg-white text-gray-800 shadow-md border border-gray-200 font-sans">

      {/* Header Section */}
      <div className="bg-blue-800 text-white text-center py-6">
        <h1 className="text-3xl font-bold uppercase">{basics.name}</h1>
        <p className="text-sm">{basics.headline}</p>
      </div>

      {/* Summary Section */}
      <div className="p-6 border-b border-gray-300">
        <Summary content={summary} />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-2">

        {/* Left Sidebar */}
        <aside className="bg-blue-100 p-6 space-y-6 text-sm">
          <div>
            <h2 className="text-blue-800 font-semibold mb-1">Contact</h2>
            <p>{basics.phone}</p>
            <p>{basics.email}</p>
            <p>{basics.address}</p>
            <p>{basics.website}</p>
          </div>
          <Profiles profiles={profiles} />
          <div>
            <h2 className="text-blue-800 font-semibold mb-1">Skills</h2>
            <Skills skills={skills} listStyle="list-disc pl-4" />
          </div>
          <div>
            <h2 className="text-blue-800 font-semibold mb-1">Languages</h2>
            <Languages languages={languages.map(lang => ({ name: lang }))} />
          </div>
          <Certifications certifications={certifications} />
          <References references={references} />
        </aside>

        {/* Right Main Content */}
        <main className="p-6 space-y-6 text-sm border-l border-gray-300">
          <Experience experience={experience} />
          <Projects projects={projects} />
          <Education education={education} />
          <Volunteering volunteering={volunteering} />
          <Awards awards={awards} />
          <Publications publications={publications} />
        </main>
      </div>
    </div>
  );
};

export default ModernBlueResume;
