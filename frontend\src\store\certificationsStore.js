import { create } from "zustand";
import axios from "axios";
import {
    GETINFO_ENDPOINT,
    ADDINFO_ENDPOINTS,
    UPDATEINFO_ENDPOINTS,
} from "../lib/constants";

export const useCertificationsStore = create((set, get) => ({
    certifications: [],
    isLoading: false,
    error: null,

    fetchCertifications: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, { withCredentials: true });
            const user = res.data.user;
            set({
                certifications: user?.Certifications || [],
                isLoading: false,
            });
        } catch (err) {
            set({
                error: err.response?.data?.error || "Failed to load certifications",
                isLoading: false,
            });
        }
    },

    addCertification: async (data) => {
        try {
            const res = await axios.post(ADDINFO_ENDPOINTS.CERTIFICATIONS, data, {
                withCredentials: true,
            });
            set((state) => ({
                certifications: [...state.certifications, res.data],
            }));
        } catch (err) {
            console.error("Error adding certification:", err.response?.data || err.message);
        }
    },

    updateCertification: async (id, data) => {
        try {
            const res = await axios.put(
                `${UPDATEINFO_ENDPOINTS.CERTIFICATIONS}/${id}`,
                data,
                { withCredentials: true }
            );
            set((state) => ({
                certifications: state.certifications.map((item) =>
                    item._id === id ? res.data : item
                ),
            }));
        } catch (err) {
            console.error("Error updating certification:", err.response?.data || err.message);
        }
    },

    deleteCertification: async (cert) => {
        try {
            await axios.delete(`${API_BASE_URL}/deleteinfo/certifications`, {
                data: cert,
                withCredentials: true,
            });

            set((state) => ({
                certifications: state.certifications.filter((c) => c._id !== cert._id),
            }));
        } catch (error) {
            console.error("Failed to delete certification:", error);
        }
    }
      
}));
