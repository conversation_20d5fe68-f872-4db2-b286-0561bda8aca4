<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];

// Get resume ID from URL
$resume_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Check if resume exists and belongs to user
$db->query("SELECT r.*, t.template_name, t.template_file 
            FROM user_resumes r 
            JOIN resume_templates t ON r.template_id = t.id 
            WHERE r.id = :id AND r.user_id = :user_id");
$db->bind(':id', $resume_id);
$db->bind(':user_id', $user_id);
$resume = $db->single();

if (!$resume) {
    setMessage('Resume not found.', 'error');
    redirect('my-resumes.php');
}

// Parse resume data
$resume_data = json_decode($resume['resume_data'], true);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $resume['resume_name']; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/resume-viewer.css">
    <link rel="stylesheet" href="templates/<?php echo $resume['template_file']; ?>/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="viewer-container">
        <div class="viewer-header">
            <div class="viewer-title">
                <h1><?php echo $resume['resume_name']; ?></h1>
                <p>Template: <?php echo $resume['template_name']; ?></p>
            </div>
            
            <div class="viewer-actions">
                <a href="edit-resume.php?resume_id=<?php echo $resume['id']; ?>&template_id=<?php echo $resume['template_id']; ?>" class="btn btn-outline" style="background: rgb(25,65,75);color:white;border:white">
                    <i class="fas fa-edit"></i> Edit Resume
                </a>
                <a href="download-resume.php?id=<?php echo $resume['id']; ?>" class="btn " style="background-color: rgb(25,65,75);color: white">
                    <i class="fas fa-download"></i> Download PDF
                </a>
            </div>
        </div>
        
        <div class="resume-viewer">
            <div class="resume-container">
                <?php include 'templates/' . $resume['template_file'] . '/view.php'; ?>
            </div>
        </div>
        
        <div class="viewer-footer">
            <a href="my-resumes.php" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to My Resumes
            </a>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize with resume data
            const resumeData = <?php echo json_encode($resume_data); ?>;
            
            // Populate resume template with data
            populateResumeTemplate(resumeData);
            
            function populateResumeTemplate(data) {
                // Personal information
                if (data.personal) {
                    Object.keys(data.personal).forEach(key => {
                        const elements = document.querySelectorAll(`[data-resume-field="${key}"]`);
                        elements.forEach(el => {
                            if (el) {
                                el.textContent = data.personal[key];
                            }
                        });
                    });
                }
                
                // Other sections are populated by the template's view.php file
            }
        });
    </script>
</body>
</html>

