document.addEventListener('DOMContentLoaded', function() {
    // Get modal elements
    const addModal = document.getElementById('add-project-modal');
    const editModal = document.getElementById('edit-project-modal');
    const addBtn = document.getElementById('add-project-btn');
    const emptyAddBtn = document.getElementById('empty-add-project-btn');
    const closeButtons = document.querySelectorAll('.close');
    
    // Add click event for add project button
    if (addBtn) {
        addBtn.addEventListener('click', function() {
            addModal.style.display = 'block';
        });
    }
    
    // Add click event for empty state add button
    if (emptyAddBtn) {
        emptyAddBtn.addEventListener('click', function() {
            addModal.style.display = 'block';
        });
    }
    
    // Add click events for all close buttons
    closeButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            addModal.style.display = 'none';
            editModal.style.display = 'none';
        });
    });
    
    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target === addModal) {
            addModal.style.display = 'none';
        }
        if (event.target === editModal) {
            editModal.style.display = 'none';
        }
    });
    
    // Handle current project checkbox
    const currentProjectCheckbox = document.getElementById('current_project');
    const endDateInput = document.getElementById('end_date');
    const endDateGroup = document.querySelector('.end-date-group');
    
    if (currentProjectCheckbox && endDateInput) {
        currentProjectCheckbox.addEventListener('change', function() {
            if (this.checked) {
                endDateInput.value = '';
                endDateGroup.style.opacity = '0.5';
                endDateInput.disabled = true;
            } else {
                endDateGroup.style.opacity = '1';
                endDateInput.disabled = false;
            }
        });
    }
    
    // Handle edit current project checkbox
    const editCurrentProjectCheckbox = document.getElementById('edit_current_project');
    const editEndDateInput = document.getElementById('edit_end_date');
    const editEndDateGroup = document.querySelector('.edit-end-date-group');
    
    if (editCurrentProjectCheckbox && editEndDateInput) {
        editCurrentProjectCheckbox.addEventListener('change', function() {
            if (this.checked) {
                editEndDateInput.value = '';
                editEndDateGroup.style.opacity = '0.5';
                editEndDateInput.disabled = true;
            } else {
                editEndDateGroup.style.opacity = '1';
                editEndDateInput.disabled = false;
            }
        });
    }
    
    // Handle edit buttons
    const editButtons = document.querySelectorAll('.edit-project-btn');
    
    editButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const projectId = this.getAttribute('data-id');
            const projectCard = document.querySelector(`.project-card[data-id="${projectId}"]`);
            
            // Get project details
            const projectName = projectCard.querySelector('h2').textContent;
            
            // Get role if it exists
            const roleElement = projectCard.querySelector('.project-role');
            const role = roleElement ? roleElement.textContent : '';
            
            // Get dates if they exist
            const periodElement = projectCard.querySelector('.project-period');
            let startDate = '';
            let endDate = '';
            let isCurrentProject = false;
            
            if (periodElement) {
                const periodText = periodElement.textContent;
                const dates = periodText.split(' - ');
                const startDateText = dates[0].trim();
                const endDateText = dates[1].trim();
                
                // Convert dates to YYYY-MM-DD format for input
                // This is a simple conversion assuming dates are in MM/YYYY format
                const startDateParts = startDateText.split('/');
                startDate = startDateParts.length === 2 ? 
                    `${startDateParts[1]}-${startDateParts[0]}-01` : startDateText;
                
                isCurrentProject = endDateText === 'Present';
                
                if (!isCurrentProject) {
                    const endDateParts = endDateText.split('/');
                    endDate = endDateParts.length === 2 ? 
                        `${endDateParts[1]}-${endDateParts[0]}-01` : endDateText;
                }
            }
            
            // Get description if it exists
            const descriptionElement = projectCard.querySelector('.project-description p');
            const description = descriptionElement ? descriptionElement.innerHTML.replace(/<br>/g, '\n') : '';
            
            // Get technologies if they exist
            const techTags = projectCard.querySelectorAll('.tech-tag');
            const technologies = Array.from(techTags).map(tag => tag.textContent).join(', ');
            
            // Get project URL if it exists
            const urlElement = projectCard.querySelector('.project-url a');
            const projectUrl = urlElement ? urlElement.href : '';
            
            // Fill form fields
            document.getElementById('edit_project_id').value = projectId;
            document.getElementById('edit_project_name').value = projectName;
            document.getElementById('edit_role').value = role;
            document.getElementById('edit_start_date').value = startDate;
            document.getElementById('edit_end_date').value = endDate;
            document.getElementById('edit_current_project').checked = isCurrentProject;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_technologies_used').value = technologies;
            document.getElementById('edit_project_url').value = projectUrl;
            
            // Handle end date display based on current project status
            if (isCurrentProject) {
                editEndDateInput.disabled = true;
                editEndDateGroup.style.opacity = '0.5';
            } else {
                editEndDateInput.disabled = false;
                editEndDateGroup.style.opacity = '1';
            }
            
            // Show modal
            editModal.style.display = 'block';
        });
    });
    
    // Technologies validation
    const validateTechInput = function(input) {
        // Remove any double commas or spaces around commas
        input.value = input.value.replace(/,\s*,/g, ',').replace(/\s*,\s*/g, ', ').replace(/^,\s*|\s*,$/g, '');
    };
    
    const techInputs = [
        document.getElementById('technologies_used'),
        document.getElementById('edit_technologies_used')
    ];
    
    techInputs.forEach(function(input) {
        if (input) {
            input.addEventListener('blur', function() {
                validateTechInput(this);
            });
        }
    });
    
    // URL validation
    const validateUrl = function(input) {
        if (input.value && !input.value.match(/^https?:\/\//)) {
            input.value = 'https://' + input.value;
        }
    };
    
    const urlInputs = [
        document.getElementById('project_url'),
        document.getElementById('edit_project_url')
    ];
    
    urlInputs.forEach(function(input) {
        if (input) {
            input.addEventListener('blur', function() {
                validateUrl(this);
            });
        }
    });
});