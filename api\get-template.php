<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Get template ID from request
$template_id = isset($_GET['id']) ? (int)$_GET['id'] : null;

if (!$template_id) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Template ID is required'
    ]);
    exit;
}

try {
    // Connect to database
    $db = new Database();
    
    // Get template details
    $db->query("SELECT * FROM templates WHERE id = :id");
    $db->bind(':id', $template_id);
    $template = $db->single();
    
    if (!$template) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Template not found'
        ]);
        exit;
    }
    
    $category = strtolower($template['category']);
    // $html_structure_file = "../templates/{$category}/{$template['name']}/structure.html";
    // $css_styles_file = "../templates/{$category}/{$template['name']}/styles.css";
    
    // // Fallback to default if files don't exist
    // if (!file_exists($html_structure_file)) {
    //     $html_structure_file = "../templates/default/structure.html";
    // }
    
    // if (!file_exists($css_styles_file)) {
    //     $css_styles_file = "../templates/default/styles.css";
    // }
    
    // // Read template files
    // $html_structure = file_get_contents($html_structure_file);
    // $css_styles = file_get_contents($css_styles_file);
    
    // Return template data
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'template' => [
            'id' => $template['id'],
            'template_name' => $template['name'],
            'category' => $template['category'],
            'description' => $template['description'],
            'preview_image' => $template['thumbnail'],
            'html_structure' => $template['html_structure'],
            'css_styles' => $template['css_styles']
        ]
    ]);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
