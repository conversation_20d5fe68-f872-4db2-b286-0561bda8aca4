const axios = require('axios');

async function testDebugEndpoint() {
    try {
        console.log('Testing debug endpoint...');
        
        const response = await axios.get('https://resumebuilder-m27v.onrender.com/api/pdf/debug');
        
        console.log('Debug information:');
        console.log(JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ Debug endpoint failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
}

testDebugEndpoint();
