import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, GETINFO_ENDPOINT, DELETE_INFO } from "../lib/constants";

export const useEducationStore = create((set) => ({
    education: [],
    isLoading: false,
    error: null,

    // Fetch all education data
    fetchEducation: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, { withCredentials: true });
            const user = res.data.user;
            set({ education: user?.Education || [], isLoading: false });
        } catch (err) {
            set({
                error: err.response?.data?.error || "Failed to load education",
                isLoading: false,
            });
        }
    },

    // Add new education entry and refresh state
    addEducation: async (data) => {
        try {
            await axios.post(ADDINFO_ENDPOINTS.EDUCATION, data, {
                withCredentials: true,
            });

            // Re-fetch the full list to stay in sync
            await useEducationStore.getState().fetchEducation();
        } catch (err) {
            console.error("Error adding education:", err.response?.data || err.message);
        }
    },

    // Delete education entry
    deleteEducation: async (education) => {
        try {
            await axios.delete(DELETE_INFO.EDUCATION(education._id), {
                withCredentials: true,
            });

            // Update local state after deletion
            set((state) => ({
                education: state.education.filter((e) => e._id !== education._id),
            }));
        } catch (error) {
            console.error("Failed to delete education:", error.response?.data || error.message);
        }
    },

    // Update existing education entry
    updateEducation: async (id, data) => {
        try {
            const res = await axios.put(`${ADDINFO_ENDPOINTS.EDUCATION}/${id}`, data, {
                withCredentials: true,
            });

            set((state) => ({
                education: state.education.map((edu) =>
                    edu._id === id ? res.data : edu
                ),
            }));
        } catch (err) {
            console.error("Error updating education:", err.response?.data || err.message);
        }
    },
}));
