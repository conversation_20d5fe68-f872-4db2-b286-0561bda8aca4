<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php'; // Require Composer autoloader for TCPDF

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];

// Check if resume ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setMessage('Resume not found.', 'error');
    redirect('dashboard.php');
}

$resume_id = (int)$_GET['id'];

// Get resume
$db->query("SELECT * FROM saved_resumes WHERE id = :id AND user_id = :user_id");
$db->bind(':id', $resume_id);
$db->bind(':user_id', $user_id);
$resume = $db->single();

if (!$resume) {
    setMessage('Resume not found or you do not have permission to download it.', 'error');
    redirect('dashboard.php');
}

// Get template
$db->query("SELECT * FROM resume_templates WHERE id = :id");
$db->bind(':id', $resume['template_id']);
$template = $db->single();

if (!$template) {
    setMessage('Template not found.', 'error');
    redirect('dashboard.php');
}

// Get resume data
$resume_data = json_decode($resume['resume_data'], true);

// Create PDF using TCPDF
class MYPDF extends TCPDF {
    public function Header() {
        // Empty header
    }
    
    public function Footer() {
        // Empty footer
    }
}

// Create new PDF document
$pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Set document information
$pdf->SetCreator(SITE_NAME);
$pdf->SetAuthor($resume_data['profile']['first_name'] . ' ' . $resume_data['profile']['last_name']);
$pdf->SetTitle($resume['resume_name']);
$pdf->SetSubject('Resume');
$pdf->SetKeywords('Resume, CV, ' . $resume_data['profile']['first_name'] . ' ' . $resume_data['profile']['last_name']);

// Set default header and footer data
$pdf->setHeaderData('', 0, '', '');

// Set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

// Set margins
$pdf->SetMargins(10, 10, 10);
$pdf->SetHeaderMargin(0);
$pdf->SetFooterMargin(0);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 10);

// Set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Add a page
$pdf->AddPage();

// Start output buffering to capture HTML content
ob_start();

// Include template file
include $template['template_file'];

// Get HTML content
$html = ob_get_clean();

// Convert HTML to PDF
$pdf->writeHTML($html, true, false, true, false, '');

// Close and output PDF document
$pdf->Output($resume['resume_name'] . '.pdf', 'D');
exit;
?>