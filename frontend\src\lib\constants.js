// Base API URL
// export const API_BASE_URL = "http://localhost:5000/api";
// export const API_BASE_URL = "https://resumebuilder-m27v.onrender.com/api";    
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
// export const API_BASE_URL = process.env.VITE_API_BASE_URL; 
// Auth Endpoints
export const AUTH_ENDPOINTS = {
    LOGIN: `${API_BASE_URL}/auth/login`,
    REGISTER: `${API_BASE_URL}/auth/register`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
    CURRENT_USER: `${API_BASE_URL}/auth/me`,
    UPDATE_USER: `${API_BASE_URL}/auth/update`,
    VERIFY_OTP: `${API_BASE_URL}/auth/verify-otp`,
    RESEND_OTP: `${API_BASE_URL}/auth/resend-otp`,
    // RESEND_OTP: `${import.meta.env.VITE_API_BASE}/auth/resend-otp`
};

// AddInfo Endpoints
export const ADDINFO_ENDPOINTS = {
    BASE: `${API_BASE_URL}/addinfo`,
    IMAGE: `${API_BASE_URL}/addinfo/upload-image`,
    DELETE_IMAGE: `${API_BASE_URL}/addinfo/delete-image`,
    WEBSITE: `${API_BASE_URL}/addinfo/website`,
    PHONE: `${API_BASE_URL}/addinfo/phone`,
    LOCATION: `${API_BASE_URL}/addinfo/location`,
    PROFILES: `${API_BASE_URL}/addinfo/profiles`,
    EXPERIENCE: `${API_BASE_URL}/addinfo/experience`,
    EDUCATION: `${API_BASE_URL}/addinfo/education`,
    SKILLS: `${API_BASE_URL}/addinfo/skills`,
    LANGUAGES: `${API_BASE_URL}/addinfo/languages`,
    AWARDS: `${API_BASE_URL}/addinfo/awards`,
    CERTIFICATIONS: `${API_BASE_URL}/addinfo/certifications`,
    INTERESTS: `${API_BASE_URL}/addinfo/interests`,
    PUBLICATIONS: `${API_BASE_URL}/addinfo/publications`,
    VOLUNTEERING: `${API_BASE_URL}/addinfo/volunteering`,
    PROJECT: `${API_BASE_URL}/addinfo/addprojects`,
    SUMMERY: `${API_BASE_URL}/addinfo/summery`,
};

export const DELETE_INFO = {
    WEBSITE: `${API_BASE_URL}/deleteinfo/website`,
    PHONE: `${API_BASE_URL}/deleteinfo/phone`,
    LOCATION: `${API_BASE_URL}/deleteinfo/location`,
    PROFILE: (id) => `${API_BASE_URL}/deleteinfo/deleteprofile/${id}`,
    EXPERIENCE: (id) => `${API_BASE_URL}/deleteinfo/deleteexperience/${id}`,
    EDUCATION: (id) => `${API_BASE_URL}/deleteinfo/deleteeducation/${id}`,
    SKILLS: (id) => `${API_BASE_URL}/deleteinfo/deleteskills/${id}`,
    LANGUAGES: (id) => `${API_BASE_URL}/deleteinfo/deletelanguages/${id}`,
    AWARDS: (id) => `${API_BASE_URL}/deleteinfo/deleteawards/${id}`,
    CERTIFICATIONS: (id) => `${API_BASE_URL}/deleteinfo/deletecertifications/${id}`,
    INTERESTS: (id) => `${API_BASE_URL}/deleteinfo/deleteinterests/${id}`,
    PUBLICATIONS: (id) => `${API_BASE_URL}/deleteinfo/deletepublications/${id}`,
    VOLUNTEERING: (id) => `${API_BASE_URL}/deleteinfo/deletevolunteering/${id}`,
    REFERENCES: (id) => `${API_BASE_URL}/deleteinfo/deletereferences/${id}`,
    PROJECT: (id) => `${API_BASE_URL}/deleteinfo/deleteproject/${id}`,
    Summery: (id) => `${API_BASE_URL}/deleteinfo/deleteproject/${id}`,
};
export const RESUME_ENDPOINTS = {
    BASE: `${API_BASE_URL}/resume`, // this matches your router base path
    CREATE: `${API_BASE_URL}/resume`, // POST
    GET_ALL: `${API_BASE_URL}/resume`, // GET
    GET_ONE: (id) => `${API_BASE_URL}/resume/${id}`, // GET by ID
    UPDATE: (id) => `${API_BASE_URL}/resume/${id}`, // PUT
    DELETE: (id) => `${API_BASE_URL}/resume/${id}`, // DELETE
    DUPLICATE: (id) => `${API_BASE_URL}/resume/${id}/duplicate`, // POST
    DOWNLOAD_PDF: (id) => `${API_BASE_URL}/resume/${id}/download-pdf`, // optional if implemented
};
export const GETINFO_ENDPOINT = `${API_BASE_URL}/getinfo`;

// DeleteInfo Endpoints


// UpdateInfo Endpoints
export const UPDATEINFO_ENDPOINTS = {
    CERTIFICATIONS: `${API_BASE_URL}/updateinfo/certifications`,
    PUBLICATIONS: `${API_BASE_URL}/updateinfo/publications`,
};