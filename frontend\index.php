<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


// session_start();
// Get templates for showcase
$db->query("SELECT * FROM templates ORDER BY id LIMIT 6");
$templates = $db->resultSet();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medini - Create Professional Resumes in Minutes</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(25,65,75,0);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--white);
            overflow-x: hidden;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        /* Header Styles */
        header {
            background-color: var(--white);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            height: 70px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            text-decoration: none;
        }
        
        .logo i {
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--spacing-lg);
        }
        
        nav a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: var(--spacing-xs) 0;
            position: relative;
            font-size: 0.95rem;
        }
        
        nav a:hover {
            color: var(--primary);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary);
            transition: var(--transition);
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        .auth-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.6rem 1.25rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.95rem;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }
        
        .btn-light {
            background-color: var(--white);
            color: var(--primary);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-light:hover {
            background-color: var(--light);
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.45rem 1rem;
            font-size: 0.85rem;
        }
        
        .btn-lg {
            padding: 0.8rem 1.75rem;
            font-size: 1rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--dark);
            font-size: 1.5rem;
            cursor: pointer;
        }

        section {
            margin-bottom: 20px;
        }
        
        /* Hero Section Styles */
        .hero {
            position: relative;
            background-color: var(--white);
            color: var(--dark);
            padding: var(--spacing-xl) 0;
            overflow: hidden;
        }
        
        .hero-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .hero-content {
            max-width: 600px;
        }
        
        .hero-badge {
            display: inline-flex;
            align-items: center;
            background: var(--secondary-light);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            margin-bottom: var(--spacing-md);
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 500;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .hero-badge i {
            color: var(--secondary);
            margin-right: 0.5rem;
        }
        
        .hero-title {
            font-size: 2.75rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: var(--spacing-md);
            color: var(--dark);
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }
        
        .hero-title .highlight {
            color: var(--primary);
            position: relative;
        }
        
        .hero-title .highlight::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 8px;
            bottom: 5px;
            left: 0;
            background-color: var(--primary-light);
            z-index: -1;
        }
        
        .hero-subtitle {
            font-size: 1.1rem;
            margin-bottom: var(--spacing-lg);
            color: var(--gray);
            line-height: 1.5;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }
        
        .text-accent {
            color: var(--accent);
            font-weight: 600;
        }
        
        .hero-actions {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
            margin-bottom: var(--spacing-lg);
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }
        
        .hero-stats {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-md);
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }
        
        .stat-item {
            position: relative;
            padding-right: var(--spacing-md);
        }
        
        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 20%;
            height: 60%;
            width: 1px;
            background-color: var(--light-gray);
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            color: var(--primary);
            display: flex;
            align-items: center;
        }
        
        .stat-label {
            font-size: 0.85rem;
            color: var(--gray);
        }

        /* Resume Preview Styles */
        .hero-visual {
            position: relative;
            animation: fadeIn 1s ease-out 0.5s both;
        }
        
        .resume-preview {
            position: relative;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
        }
        
        .resume-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            height: 500px;
            padding: 2rem;
            border: 1px solid var(--light-gray);
            transition: var(--transition);
        }
        
        .resume-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .resume-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .profile-circle {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary-light) 0%, var(--primary-light) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .profile-circle::after {
            content: '\f007';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .header-lines {
            flex: 1;
        }
        
        .line-long {
            height: 15px;
            width: 70%;
            background: var(--gray);
            opacity: 0.1;
            border-radius: 4px;
            margin-bottom: 0.75rem;
        }
        
        .line-short {
            height: 10px;
            width: 40%;
            background: var(--gray);
            opacity: 0.1;
            border-radius: 4px;
        }
        
        .resume-body {
            padding: 0 0.5rem;
        }
        
        .section-title-line {
            height: 12px;
            width: 30%;
            background: var(--primary);
            opacity: 0.1;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .content-lines {
            margin-bottom: 1.5rem;
        }
        
        .line {
            height: 8px;
            width: 100%;
            background: var(--gray);
            opacity: 0.1;
            border-radius: 4px;
            margin-bottom: 0.75rem;
        }
        
        .editing-elements {
            position: absolute;
            z-index: 2;
        }
        
        .element {
            position: absolute;
            background: var(--white);
            border-radius: var(--radius);
            padding: 0.6rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            font-weight: 500;
            color: var(--dark);
            font-size: 0.85rem;
            border: 1px solid var(--light-gray);
        }
        
        .element i {
            color: var(--primary);
        }
        
        .element-1 {
            top: 20px;
            right: -15px;
            animation: floatElement 4s ease-in-out infinite;
        }
        
        .element-2 {
            bottom: 120px;
            left: -20px;
            animation: floatElement 4s ease-in-out infinite 1s;
        }
        
        .element-3 {
            bottom: 30px;
            right: 30px;
            animation: floatElement 4s ease-in-out infinite 2s;
        }
        
        @keyframes floatElement {
            0% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
            100% { transform: translateY(0); }
        }
        
        .notification {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--white);
            border-radius: 50px;
            padding: 0.6rem 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 10;
            animation: popIn 0.5s ease-out 1.5s both;
        }
        
        .success-notification {
            color: var(--success);
            border-left: 3px solid var(--success);
        }
        
        .success-notification i {
            font-size: 1rem;
        }
        
        /* Features Section */
        .features {
            background-color: var(--light);
            padding: var(--spacing-xl) 0;
        }
        
        .section-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: var(--spacing-xl);
            text-align: center;
            position: relative;
            color: var(--dark);
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background-color: var(--primary);
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .feature-card {
            background-color: var(--white);
            border-radius: var(--radius);
            padding: var(--spacing-lg);
            transition: var(--transition);
            text-align: center;
            border: 1px solid var(--light-gray);
            opacity: 0;
            transform: translateY(20px);
        }
        
        .feature-card.animate {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .feature-icon {
            background: var(--secondary-light);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
        }
        
        .feature-icon i {
            font-size: 1.4rem;
            color: var(--primary);
        }
        
        .feature-card h3 {
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--dark);
            font-size: 1.15rem;
        }
        
        .feature-card p {
            color: var(--gray);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        /* Templates Section */
        .templates {
            background-color: var(--white);
            padding: var(--spacing-xl) 0;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .slider-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 20px;
        }
        
        .slider-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--white);
            border: 1px solid var(--light-gray);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
        }
        
        .slider-btn:hover {
            background-color: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }
        
        .slider-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .slider-wrapper {
            width: 100%;
            overflow: hidden;
            position: relative;
        }
        
        .template-slider {
            display: flex;
            gap: var(--spacing-lg);
            transition: transform 0.5s ease;
        }
        
        .template-card {
            flex: 0 0 calc((100% - 4rem) / 3);
            min-width: 0;
            background-color: var(--white);
            border-radius: var(--radius);
            overflow: hidden;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .template-image {
            position: relative;
            overflow: hidden;
            height: 300px;
            background-color: #f0f4f8;
        }
        
        .template-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }
        
        .template-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(45, 95, 139, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: var(--transition);
        }
        
        .template-card:hover .template-overlay {
            opacity: 1;
        }
        
        .template-info {
            padding: var(--spacing-md);
            background-color: var(--white);
        }
        
        .template-info h3 {
            margin-bottom: 0.5rem;
            color: var(--dark);
            font-size: 1.1rem;
        }
        
        .template-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: var(--spacing-md);
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--light-gray);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .dot.active {
            width: 20px;
            border-radius: 10px;
            background-color: var(--primary);
        }
        
        /* How It Works Section */
        .how-it-works {
            background-color: var(--light);
            padding: var(--spacing-xl) 0;
        }
        
        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .step {
            position: relative;
            text-align: center;
            padding: var(--spacing-xl) var(--spacing-md) var(--spacing-lg);
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--light-gray);
            opacity: 0;
            transform: translateY(20px);
        }
        
        .step.animate {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .step-number {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background-color: var(--primary);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: 0 4px 12px rgba(45, 95, 139, 0.2);
        }
        
        .step-icon {
            width: 60px;
            height: 60px;
            background-color: var(--secondary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
        }
        
        .step-icon i {
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .step h3 {
            margin-bottom: var(--spacing-sm);
            color: var(--dark);
            font-size: 1.1rem;
        }
        
        .step p {
            color: var(--gray);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        /* Testimonials Section */
        .testimonials {
            background-color: var(--white);
            padding: var(--spacing-xl) 0;
        }
        
        .testimonial-slider {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .testimonial-card {
            background-color: var(--white);
            border-radius: var(--radius);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            position: relative;
            border: 1px solid var(--light-gray);
            opacity: 0;
            transform: translateY(20px);
        }
        
        .testimonial-card.animate {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 3rem;
            color: var(--primary-light);
            font-family: serif;
            line-height: 1;
            opacity: 0.7;
        }
        
        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .testimonial-content {
            margin-bottom: var(--spacing-md);
            padding-left: 1rem;
            border-left: 3px solid var(--primary);
            font-size: 0.95rem;
            color: var(--gray);
            line-height: 1.6;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .testimonial-author img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--light-gray);
            background-color: #f0f4f8;
        }
        
        .author-info h4 {
            color: var(--dark);
            margin-bottom: 0.25rem;
            font-size: 1rem;
        }
        
        .author-info p {
            color: var(--gray);
            font-size: 0.85rem;
        }
        
        /* CTA Section */
        .cta {
            background-color: var(--primary);
            color: var(--white);
            padding: var(--spacing-xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cta h2 {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }
        
        .cta p {
            max-width: 600px;
            margin: 0 auto var(--spacing-lg);
            opacity: 0.9;
            font-size: 1rem;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }
        
        .cta .btn {
            position: relative;
            z-index: 1;
        }
        
        .cta-shape {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
        }
        
        .cta-shape-1 {
            width: 300px;
            height: 300px;
            top: -150px;
            left: -100px;
        }
        
        .cta-shape-2 {
            width: 200px;
            height: 200px;
            bottom: -100px;
            right: -50px;
        }
        
        /* Footer Styles */
        footer {
            background-color: var(--white);
            color: var(--dark);
            padding: var(--spacing-xl) 0 var(--spacing-lg);
            border-top: 1px solid var(--light-gray);
        }
        
        footer .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
            text-decoration: none;
            margin-bottom: 1rem;
        }
        
        footer .logo i {
            font-size: 1.5rem;
        }
        
        .footer-tagline {
            color: var(--gray);
            margin-bottom: var(--spacing-md);
            line-height: 1.6;
            font-size: 0.95rem;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: 2fr repeat(4, 1fr);
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border-bottom: 1px solid var(--light-gray);
            padding-bottom: var(--spacing-lg);
        }
        
        .footer-column h3 {
            color: var(--dark);
            margin-bottom: var(--spacing-md);
            font-size: 1rem;
            position: relative;
            padding-bottom: 0.75rem;
            font-weight: 600;
        }
        
        .footer-column h3::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 25px;
            height: 2px;
            background-color: var(--primary);
        }
        
        .footer-column ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .footer-column ul li {
            margin-bottom: 0.65rem;
        }
        
        .footer-column a {
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            font-size: 0.9rem;
        }
        
        .footer-column a:hover {
            color: var(--primary);
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: var(--spacing-md);
        }
        
        .social-link {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            transition: var(--transition);
            text-decoration: none;
            border: 1px solid var(--light-gray);
        }
        
        .social-link:hover {
            background-color: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }
        
        .footer-bottom {
            text-align: center;
        }
        
        .footer-bottom p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes popIn {
            0% {
                opacity: 0;
                transform: translateX(-50%) scale(0.8);
            }
            70% {
                transform: translateX(-50%) scale(1.1);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: var(--spacing-xl);
            }
            
            .hero-content {
                max-width: 100%;
                padding-right: 0;
            }
            
            .hero-title {
                font-size: 2.25rem;
            }
            
            .hero-actions {
                justify-content: center;
            }
            
            .hero-stats {
                justify-content: center;
            }
            
            .stat-item {
                padding-left: var(--spacing-md);
                padding-right: var(--spacing-md);
            }
            
            .hero-visual {
                order: -1;
            }
            
            .footer-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .footer-column:first-child {
                grid-column: span 3;
                margin-bottom: var(--spacing-lg);
            }
            
            .template-card {
                flex: 0 0 calc((100% - 2rem) / 2);
            }
        }
        
        @media (max-width: 768px) {
            .hero {
                padding: var(--spacing-lg) 0;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-stats {
                flex-direction: column;
                gap: var(--spacing-md);
                align-items: center;
            }
            
            .stat-item:not(:last-child)::after {
                display: none;
            }
            
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .footer-column:first-child {
                grid-column: span 2;
            }
            
            nav ul {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .template-card {
                flex: 0 0 100%;
            }
        }
        
        @media (max-width: 576px) {
            .hero-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .btn {
                width: 100%;
            }
            
            .footer-grid {
                grid-template-columns: 1fr;
            }
            
            .footer-column:first-child {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <?php include "includes/header.php" ?>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span><i class="fas fa-star"></i> Trusted by 10,000+ professionals</span>
                </div>
                <h1 class="hero-title">
                    <span class="highlight">Transform</span> Your Career Journey With ATS Friendly Resumes
                </h1>
                <p class="hero-subtitle">Our intelligent platform analyzes hiring trends to create personalized, ATS-friendly resumes that are <span class="text-accent">3x more likely</span> to land interviews</p>

                <div class="hero-actions">
                    <a href="register.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket"></i> Create Your Resume
                    </a>
                    <a href="templates.php" class="btn btn-outline btn-lg">
                        View Templates
                    </a>
                </div>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number count-up" data-target="94">0</span><span>%</span>
                        <span class="stat-label">Pass ATS Screening</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number count-up" data-target="3121">0</span><span>+</span>
                        <span class="stat-label">Resumes Created</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number count-up" data-target="4.9" data-decimal="true">0</span>
                        <span class="stat-label"><i class="fas fa-star"></i> Rating</span>
                    </div>
                </div>
            </div>

            <div class="hero-visual">
                <div class="resume-preview">
                    <div class="resume-card">
                        <div class="resume-header">
                            <div class="profile-circle"></div>
                            <div class="header-lines">
                                <div class="line-long"></div>
                                <div class="line-short"></div>
                            </div>
                        </div>
                        <div class="resume-body">
                            <div class="section-title-line"></div>
                            <div class="content-lines">
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line-short"></div>
                            </div>
                            <div class="section-title-line"></div>
                            <div class="content-lines">
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line-short"></div>
                            </div>
                        </div>
                    </div>
                    <div class="editing-elements">
                        <div class="element element-1">
                            <i class="fas fa-briefcase"></i>
                            <span>Experience</span>
                        </div>
                        <div class="element element-2">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Education</span>
                        </div>
                        <div class="element element-3">
                            <i class="fas fa-cogs"></i>
                            <span>Skills</span>
                        </div>
                    </div>
                    <div class="notification success-notification">
                        <i class="fas fa-check-circle"></i>
                        <span>ATS-Optimized!</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section id="features" class="section features">
        <div class="container">
            <h2 class="section-title text-center">Smart Features for Job-Winning Resumes</h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-download"></i>
                    </div>
                    <h3>Free Resume Download</h3>
                    <p>Create and download your resume for free in high-quality formats. No paywall, no tricks—just great resumes.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>ATS-Optimized Templates</h3>
                    <p>All designs are tested with leading Applicant Tracking Systems to ensure your resume passes automated screenings.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-copy"></i>
                    </div>
                    <h3>One-Click Duplication</h3>
                    <p>Easily duplicate your resume to target different roles without starting from scratch. Save time and stay organized.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Real-Time Editor</h3>
                    <p>Create and iterate on your resume with our intuitive drag-and-drop interface and see changes instantly.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3>Multi-Format Export</h3>
                    <p>Download your resume in PDF, DOCX, or plain text formats to match any application requirement.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Privacy Protection</h3>
                    <p>Your data is encrypted and never shared with third parties. We prioritize the security of your personal information.</p>
                </div>
            </div>
        </div>
    </section>

    
    <!-- Templates Section -->
    <section id="templates" class="section templates">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Professional Resume Templates</h2>
                <a href="templates.php" class="btn btn-outline">View All Templates</a>
            </div>
            
            <div class="slider-wrapper">
                <div class="template-slider">
                    <?php foreach ($templates as $template): ?>
                    <div class="template-card">
                        <div class="template-image">
                            <img src="<?php echo $template['thumbnail']; ?>" alt="Executive Template">
                            <div class="template-overlay">
                                <a href="editor.html?template=<?php echo $template['id'] ?>" class="btn btn-sm btn-light">Use Template</a>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3><?php echo $template['name'] ?></h3>
                            <p><?php echo $template['description'] ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="slider-controls">
                <button class="slider-btn prev-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="slider-btn next-btn"><i class="fas fa-chevron-right"></i></button>
            </div>
        </div>
    </section>
    
    <!-- How It Works Section -->
    <section class="section how-it-works">
        <div class="container">
            <h2 class="section-title text-center">Build Your Resume in 4 Simple Steps</h2>
            
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3>Create Your Account</h3>
                    <p>Sign up in seconds and access our full suite of resume-building tools.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>Select a Template</h3>
                    <p>Choose from our collection of professional, ATS-optimized resume designs.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h3>Add Your Content</h3>
                    <p>Input your details or let our AI suggest professional content for each section.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3>Download & Apply</h3>
                    <p>Export your polished resume in multiple formats and start applying with confidence.</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Testimonials Section -->
    <section class="section testimonials">
        <div class="container">
            <h2 class="section-title text-center">Success Stories from Our Users</h2>
            
            <div class="testimonial-slider">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"After using Medini Resume Builder, I received interview requests from 4 out of 6 companies within the first week. The AI suggestions helped me highlight achievements I wouldn't have thought to include!"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/60x60/f0f4f8/2d5f8b?text=JW" alt="James Wilson">
                        <div class="author-info">
                            <h4>James Wilson</h4>
                            <p>Software Engineer</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"As a career changer, I struggled to present my transferable skills effectively. The template and content suggestions made all the difference—I landed my dream role in marketing after just one month!"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/60x60/f0f4f8/2d5f8b?text=SJ" alt="Sarah Johnson">
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <p>Marketing Specialist</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The ATS optimization feature is a game-changer. My previous resume was getting rejected by automated systems, but my new resume from Medini got me through to human recruiters and ultimately to my first job!"</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://placehold.co/60x60/f0f4f8/2d5f8b?text=MC" alt="Michael Chen">
                        <div class="author-info">
                            <h4>Michael Chen</h4>
                            <p>Recent Graduate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-shape cta-shape-1"></div>
        <div class="cta-shape cta-shape-2"></div>
        <div class="container">
            <h2>Ready to Elevate Your Career?</h2>
            <p>Join thousands of professionals who have accelerated their job search with our intelligent resume platform.</p>
            <a href="#register" class="btn btn-light btn-lg">Create Your Resume Now</a>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <a href="index.html" class="logo">
                        <i class="fas fa-file-alt"></i>
                        <span>Medini</span>
                    </a>
                    <p class="footer-tagline">Create professional, ATS-optimized resumes that get you noticed by employers.</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="footer-column">
                    <h3>Product</h3>
                    <ul>
                        <li><a href="templates.php">Resume Templates</a></li>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#testimonials">Testimonials</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#blog">Career Blog</a></li>
                        <li><a href="#guides">Resume Guides</a></li>
                        <li><a href="#examples">Resume Examples</a></li>
                        <li><a href="#career-advice">Career Advice</a></li>
                        <li><a href="#job-search-tips">Job Search Tips</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#contact">Contact</a></li>
                        <li><a href="#careers">Careers</a></li>
                        <li><a href="#press">Press</a></li>
                        <li><a href="#affiliates">Affiliate Program</a></li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#help-center">Help Center</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                        <li><a href="#security">Security</a></li>
                        <li><a href="#accessibility">Accessibility</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom text-center">
                <p>© <span id="current-year">2023</span> Medini. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update current year
            document.getElementById('current-year').textContent = new Date().getFullYear();
            
            // Mobile menu toggle
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const nav = document.querySelector('nav ul');
            
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    nav.style.display = nav.style.display === 'flex' ? 'none' : 'flex';
                });
            }
            
            // Slider functionality
            const templateSlider = document.querySelector('.template-slider');
            const templateCards = document.querySelectorAll('.template-card');
            const prevBtn = document.querySelector('.prev-btn');
            const nextBtn = document.querySelector('.next-btn');
            const dots = document.querySelectorAll('.dot');
            
            // Calculate visible items based on screen width
            function getVisibleItems() {
                if (window.innerWidth < 768) return 1;
                if (window.innerWidth < 992) return 2;
                return 3;
            }
            
            let currentIndex = 0;
            let visibleItems = getVisibleItems();
            
            // Update slider position
            function updateSlider() {
                const cardWidth = templateCards[0].offsetWidth;
                const gap = 32; // 2rem gap from CSS
                templateSlider.style.transform = `translateX(-${currentIndex * (cardWidth + gap)}px)`;
                
                // Update button states
                prevBtn.disabled = currentIndex === 0;
                nextBtn.disabled = currentIndex >= templateCards.length - visibleItems;
                
                // Visual indicator for disabled buttons
                prevBtn.style.opacity = prevBtn.disabled ? '0.5' : '1';
                nextBtn.style.opacity = nextBtn.disabled ? '0.5' : '1';
                
                // Update dots
                dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === Math.floor(currentIndex / visibleItems));
                });
            }
            
            // Initialize slider
            updateSlider();
            
            // Event listeners for buttons
            prevBtn.addEventListener('click', function() {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateSlider();
                }
            });
            
            nextBtn.addEventListener('click', function() {
                if (currentIndex < templateCards.length - visibleItems) {
                    currentIndex++;
                    updateSlider();
                }
            });
            
            // Dot navigation
            dots.forEach(dot => {
                dot.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    currentIndex = index * visibleItems;
                    updateSlider();
                });
            });
            
            // Update on window resize
            window.addEventListener('resize', function() {
                visibleItems = getVisibleItems();
                // Reset position if we're showing more items than remaining
                if (currentIndex > templateCards.length - visibleItems) {
                    currentIndex = Math.max(0, templateCards.length - visibleItems);
                }
                updateSlider();
            });
            
            // Number counter animation
            function animateCountUp() {
                const counters = document.querySelectorAll('.count-up');
                const speed = 200; // The lower the faster
                
                counters.forEach(counter => {
                    const target = parseFloat(counter.getAttribute('data-target'));
                    const isDecimal = counter.getAttribute('data-decimal') === 'true';
                    const increment = target / speed;
                    let count = 0;
                    
                    const updateCount = () => {
                        if (count < target) {
                            count += increment;
                            if (count > target) count = target;
                            
                            if (isDecimal) {
                                counter.textContent = count.toFixed(1);
                            } else {
                                counter.textContent = Math.floor(count);
                            }
                            
                            requestAnimationFrame(updateCount);
                        }
                    };
                    
                    updateCount();
                });
            }
            
            // Animate elements when they come into view
            function animateOnScroll() {
                const featureCards = document.querySelectorAll('.feature-card');
                const steps = document.querySelectorAll('.step');
                const testimonialCards = document.querySelectorAll('.testimonial-card');
                
                const elements = [...featureCards, ...steps, ...testimonialCards];
                
                const isInViewport = (element) => {
                    const rect = element.getBoundingClientRect();
                    return (
                        rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8
                    );
                };
                
                const animateElements = () => {
                    elements.forEach((element, index) => {
                        if (isInViewport(element) && !element.classList.contains('animate')) {
                            // Add delay based on index for staggered animation
                            setTimeout(() => {
                                element.classList.add('animate');
                            }, index % 3 * 150); // Stagger by 150ms within each row
                        }
                    });
                };
                
                // Initial check
                animateElements();
                
                // Check on scroll
                window.addEventListener('scroll', animateElements);
            }
            
            // Start animations
            animateCountUp();
            animateOnScroll();
        });
    </script>
</body>
</html>