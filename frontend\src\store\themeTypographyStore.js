// store/themeTypographyStore.js
import { create } from "zustand";

export const useTypographyStore = create((set) => ({
    fontFamily: "Roboto",
    fontVariant: "regular",
    fontSize: 18,
    lineHeight: 1.5,
    hideIcons: false,
    underlineLinks: true,

    setFontFamily: (value) => set({ fontFamily: value }),
    setFontVariant: (value) => set({ fontVariant: value }),
    setFontSize: (value) => set({ fontSize: value }),
    setLineHeight: (value) => set({ lineHeight: value }),
    setHideIcons: (value) => set({ hideIcons: value }),
    setUnderlineLinks: (value) => set({ underlineLinks: value }),

    // ✅ Reset function
    resetTypography: () =>
        set({
            fontFamily: "Roboto",
            fontVariant: "regular",
            fontSize: 18,
            lineHeight: 1.5,
            hideIcons: false,
            underlineLinks: true,
        }),
}));
