import React, { useEffect } from "react";
import { useAdminStore } from "../store/useAdminStore";
import ResumeCard from "../components/admin/ResumeCard";

const AdminResumes = () => {
    const {
        resumes,
        paginatedResumes,
        currentPage,
        paginateResumes,
        deleteResume,
        loading,
        error,
        fetchAllResumes,
    } = useAdminStore();

    useEffect(() => {
        fetchAllResumes();
    }, []);

    const handlePreview = (resume) => {
        // Open modal or redirect to resume detail page
        console.log("Preview resume:", resume);
    };

    return (
        <div className="p-4">
            <h1 className="text-2xl font-bold mb-6 text-[#29354d]">All Resumes</h1>

            {loading && <p className="text-gray-600">Loading resumes...</p>}
            {error && <p className="text-red-500">{error}</p>}
            {!loading && paginatedResumes.length === 0 && (
                <p className="text-gray-500">No resumes available.</p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedResumes.map((resume) => (
                    <ResumeCard
                        key={resume._id}
                        resume={resume}
                        onDelete={deleteResume}
                        onPreview={handlePreview}
                    />
                ))}
            </div>

            {/* Pagination */}
            {resumes.length > 5 && (
                <div className="flex justify-center mt-6 gap-2">
                    {Array.from({ length: Math.ceil(resumes.length / 5) }).map((_, i) => (
                        <button
                            key={i}
                            onClick={() => paginateResumes(i + 1)}
                            className={`px-3 py-1 rounded-md text-sm font-medium ${currentPage === i + 1
                                    ? "bg-yellow-400 text-black"
                                    : "bg-gray-200 hover:bg-gray-300"
                                }`}
                        >
                            {i + 1}
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

export default AdminResumes;
