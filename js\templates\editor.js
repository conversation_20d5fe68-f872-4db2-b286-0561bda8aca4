/**
 * Resume Editor JavaScript
 * Handles all the functionality for the resume editor
 */

// Global variables
let resumeData = {
    personal: {},
    summary: '',
    experience: [],
    education: [],
    skills: [],
    projects: [],
    certifications: []
};

/**
 * Initialize the resume editor
 * @param {Object} data - Existing resume data (if editing)
 */
function initResumeEditor(data = null) {
    // If we have existing data, load it
    if (data) {
        resumeData = data;
        populateFormFields();
    } else {
        // Otherwise, initialize with data from the form
        collectFormData();
    }

    // Set up event listeners
    setupEventListeners();
    
    // Initial update of the resume preview
    updateResumePreview();
}

/**
 * Populate form fields with existing data
 */
function populateFormFields() {
    // Personal information
    for (const field in resumeData.personal) {
        const input = document.querySelector(`[data-field="${field}"]`);
        if (input) {
            input.value = resumeData.personal[field];
        }
    }
    
    // Summary
    const summaryField = document.querySelector('#summary');
    if (summaryField && resumeData.summary) {
        summaryField.value = resumeData.summary;
    }
    
    // Experience
    const experienceContainer = document.getElementById('experience-items');
    experienceContainer.innerHTML = '';
    
    resumeData.experience.forEach((exp, index) => {
        const expItem = createExperienceItem(exp, index);
        experienceContainer.appendChild(expItem);
    });
    
    // Education
    const educationContainer = document.getElementById('education-items');
    educationContainer.innerHTML = '';
    
    resumeData.education.forEach((edu, index) => {
        const eduItem = createEducationItem(edu, index);
        educationContainer.appendChild(eduItem);
    });
    
    // Skills
    const skillsContainer = document.getElementById('skills-container');
    skillsContainer.innerHTML = '';
    
    resumeData.skills.forEach(skill => {
        const skillTag = createSkillTag(skill);
        skillsContainer.appendChild(skillTag);
    });
}

/**
 * Collect data from form fields
 */
function collectFormData() {
    // Personal information
    const personalFields = document.querySelectorAll('[data-field]');
    personalFields.forEach(field => {
        const fieldName = field.getAttribute('data-field');
        if (fieldName === 'summary') {
            resumeData.summary = field.value;
        } else {
            resumeData.personal[fieldName] = field.value;
        }
    });
    
    // Experience
    resumeData.experience = [];
    const experienceItems = document.querySelectorAll('.experience-item');
    experienceItems.forEach(item => {
        const jobTitle = item.querySelector('.exp-job-title').value;
        const company = item.querySelector('.exp-company').value;
        const startDate = item.querySelector('.exp-start-date').value;
        const endDate = item.querySelector('.exp-end-date').value;
        const description = item.querySelector('.exp-description').value;
        
        resumeData.experience.push({
            jobTitle,
            company,
            startDate,
            endDate,
            description
        });
    });
    
    // Education
    resumeData.education = [];
    const educationItems = document.querySelectorAll('.education-item');
    educationItems.forEach(item => {
        const degree = item.querySelector('.edu-degree').value;
        const institution = item.querySelector('.edu-institution').value;
        const startDate = item.querySelector('.edu-start-date').value;
        const endDate = item.querySelector('.edu-end-date').value;
        const description = item.querySelector('.edu-description').value;
        
        resumeData.education.push({
            degree,
            institution,
            startDate,
            endDate,
            description
        });
    });
    
    // Skills
    resumeData.skills = [];
    const skillTags = document.querySelectorAll('.skill-tag span');
    skillTags.forEach(tag => {
        resumeData.skills.push(tag.textContent);
    });
}

/**
 * Set up event listeners for the editor
 */
function setupEventListeners() {
    // Section toggles
    const sectionToggles = document.querySelectorAll('.section-toggle');
    sectionToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const section = this.closest('.editor-section');
            section.classList.toggle('active');
            
            // Update icon
            const icon = this.querySelector('i');
            if (section.classList.contains('active')) {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        });
    });
    
    // Form field changes
    const formFields = document.querySelectorAll('input[data-field], textarea[data-field]');
    formFields.forEach(field => {
        field.addEventListener('input', function() {
            const fieldName = this.getAttribute('data-field');
            if (fieldName === 'summary') {
                resumeData.summary = this.value;
            } else {
                resumeData.personal[fieldName] = this.value;
            }
            updateResumePreview();
        });
    });
    
    // Add experience button
    const addExperienceBtn = document.getElementById('add-experience');
    addExperienceBtn.addEventListener('click', function() {
        const experienceContainer = document.getElementById('experience-items');
        const newIndex = experienceContainer.children.length;
        const newExperience = {
            jobTitle: '',
            company: '',
            startDate: '',
            endDate: '',
            description: ''
        };
        
        const expItem = createExperienceItem(newExperience, newIndex);
        experienceContainer.appendChild(expItem);
        
        // Open the form for editing
        const itemForm = expItem.querySelector('.item-form');
        const itemHeader = expItem.querySelector('.item-header');
        itemForm.style.display = 'block';
        itemHeader.style.display = 'none';
        
        // Focus on the first input
        expItem.querySelector('.exp-job-title').focus();
    });
    
    // Add education button
    const addEducationBtn = document.getElementById('add-education');
    addEducationBtn.addEventListener('click', function() {
        const educationContainer = document.getElementById('education-items');
        const newIndex = educationContainer.children.length;
        const newEducation = {
            degree: '',
            institution: '',
            startDate: '',
            endDate: '',
            description: ''
        };
        
        const eduItem = createEducationItem(newEducation, newIndex);
        educationContainer.appendChild(eduItem);
        
        // Open the form for editing
        const itemForm = eduItem.querySelector('.item-form');
        const itemHeader = eduItem.querySelector('.item-header');
        itemForm.style.display = 'block';
        itemHeader.style.display = 'none';
        
        // Focus on the first input
        eduItem.querySelector('.edu-degree').focus();
    });
    
    // Add skill button
    const addSkillBtn = document.getElementById('add-skill-btn');
    const addSkillInput = document.getElementById('add-skill-input');
    
    addSkillBtn.addEventListener('click', function() {
        const skillName = addSkillInput.value.trim();
        if (skillName) {
            const skillsContainer = document.getElementById('skills-container');
            const skillTag = createSkillTag(skillName);
            skillsContainer.appendChild(skillTag);
            
            // Add to resume data
            resumeData.skills.push(skillName);
            
            // Clear input
            addSkillInput.value = '';
            addSkillInput.focus();
            
            // Update preview
            updateResumePreview();
        }
    });
    
    // Allow adding skills with Enter key
    addSkillInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            addSkillBtn.click();
        }
    });
    
    // Form submission
    const resumeForm = document.getElementById('resume-form');
    resumeForm.addEventListener('submit', function(e) {
        // Update resume data before submission
        collectFormData();
        
        // Set the hidden input value
        document.getElementById('resume_data').value = JSON.stringify(resumeData);
    });
    
    // Preview button
    const previewBtn = document.getElementById('preview-btn');
    previewBtn.addEventListener('click', function() {
        collectFormData();
        updateResumePreview();
        
        // Scroll to preview on mobile
        if (window.innerWidth < 992) {
            document.querySelector('.resume-preview').scrollIntoView({ behavior: 'smooth' });
        }
    });
    
    // Download PDF button
    const downloadPdfBtn = document.getElementById('download-pdf');
    downloadPdfBtn.addEventListener('click', function() {
        const resumeElement = document.getElementById('resume-template');
        const resumeName = document.getElementById('resume_name').value || 'resume';
        
        // Configure html2pdf options
        const opt = {
            margin: 10,
            filename: `${resumeName}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2, useCORS: true },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        };
        
        // Generate PDF
        html2pdf().set(opt).from(resumeElement).save();
    });
}

/**
 * Create an experience item element
 * @param {Object} experience - Experience data
 * @param {number} index - Index of the experience
 * @returns {HTMLElement} - Experience item element
 */
function createExperienceItem(experience, index) {
    const expItem = document.createElement('div');
    expItem.className = 'experience-item';
    expItem.dataset.index = index;
    
    expItem.innerHTML = `
        <div class="item-header">
            <h4>${experience.jobTitle || 'New Position'} at ${experience.company || 'Company'}</h4>
            <div class="item-actions">
                <button type="button" class="btn-icon item-edit"><i class="fas fa-edit"></i></button>
                <button type="button" class="btn-icon item-remove"><i class="fas fa-trash"></i></button>
            </div>
        </div>
        <div class="item-form" style="display: none;">
            <div class="form-group">
                <label>Job Title</label>
                <input type="text" class="exp-job-title" value="${experience.jobTitle || ''}">
            </div>
            <div class="form-group">
                <label>Company</label>
                <input type="text" class="exp-company" value="${experience.company || ''}">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Start Date</label>
                    <input type="text" class="exp-start-date" value="${experience.startDate || ''}">
                </div>
                <div class="form-group">
                    <label>End Date</label>
                    <input type="text" class="exp-end-date" value="${experience.endDate || ''}">
                </div>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="exp-description" rows="4">${experience.description || ''}</textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-sm btn-outline item-cancel">Cancel</button>
                <button type="button" class="btn btn-sm btn-primary item-save">Save</button>
            </div>
        </div>
    `;
    
    // Add event listeners
    const editBtn = expItem.querySelector('.item-edit');
    const removeBtn = expItem.querySelector('.item-remove');
    const saveBtn = expItem.querySelector('.item-save');
    const cancelBtn = expItem.querySelector('.item-cancel');
    
    editBtn.addEventListener('click', function() {
        const itemForm = expItem.querySelector('.item-form');
        const itemHeader = expItem.querySelector('.item-header');
        itemForm.style.display = 'block';
        itemHeader.style.display = 'none';
    });
    
    removeBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to remove this experience?')) {
            expItem.remove();
            
            // Update resume data
            const index = parseInt(expItem.dataset.index);
            resumeData.experience.splice(index, 1);
            
            // Update indices of remaining items
            const remainingItems = document.querySelectorAll('.experience-item');
            remainingItems.forEach((item, i) => {
                item.dataset.index = i;
            });
            
            // Update preview
            updateResumePreview();
        }
    });
    
    saveBtn.addEventListener('click', function() {
        const jobTitle = expItem.querySelector('.exp-job-title').value;
        const company = expItem.querySelector('.exp-company').value;
        const startDate = expItem.querySelector('.exp-start-date').value;
        const endDate = expItem.querySelector('.exp-end-date').value;
        const description = expItem.querySelector('.exp-description').value;
        
        // Update header
        expItem.querySelector('h4').textContent = `${jobTitle} at ${company}`;
        
        // Update resume data
        const index = parseInt(expItem.dataset.index);
        resumeData.experience[index] = {
            jobTitle,
            company,
            startDate,
            endDate,
            description
        };
        
        // Hide form, show header
        expItem.querySelector('.item-form').style.display = 'none';
        expItem.querySelector('.item-header').style.display = 'flex';
        
        // Update preview
        updateResumePreview();
    });
    
    cancelBtn.addEventListener('click', function() {
        const itemForm = expItem.querySelector('.item-form');
        const itemHeader = expItem.querySelector('.item-header');
        
        // If this is a new item with no data, remove it
        const index = parseInt(expItem.dataset.index);
        if (!resumeData.experience[index] || 
            (!resumeData.experience[index].jobTitle && !resumeData.experience[index].company)) {
            expItem.remove();
        } else {
            // Otherwise, just hide the form
            itemForm.style.display = 'none';
            itemHeader.style.display = 'flex';
        }
    });
    
    return expItem;
}

/**
 * Create an education item element
 * @param {Object} education - Education data
 * @param {number} index - Index of the education
 * @returns {HTMLElement} - Education item element
 */
function createEducationItem(education, index) {
    const eduItem = document.createElement('div');
    eduItem.className = 'education-item';
    eduItem.dataset.index = index;
    
    eduItem.innerHTML = `
        <div class="item-header">
            <h4>${education.degree || 'Degree'} - ${education.institution || 'Institution'}</h4>
            <div class="item-actions">
                <button type="button" class="btn-icon item-edit"><i class="fas fa-edit"></i></button>
                <button type="button" class="btn-icon item-remove"><i class="fas fa-trash"></i></button>
            </div>
        </div>
        <div class="item-form" style="display: none;">
            <div class="form-group">
                <label>Degree</label>
                <input type="text" class="edu-degree" value="${education.degree || ''}">
            </div>
            <div class="form-group">
                <label>Institution</label>
                <input type="text" class="edu-institution" value="${education.institution || ''}">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Start Date</label>
                    <input type="text" class="edu-start-date" value="${education.startDate || ''}">
                </div>
                <div class="form-group">
                    <label>End Date</label>
                    <input type="text" class="edu-end-date" value="${education.endDate || ''}">
                </div>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="edu-description" rows="3">${education.description || ''}</textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-sm btn-outline item-cancel">Cancel</button>
                <button type="button" class="btn btn-sm btn-primary item-save">Save</button>
            </div>
        </div>
    `;
    
    // Add event listeners
    const editBtn = eduItem.querySelector('.item-edit');
    const removeBtn = eduItem.querySelector('.item-remove');
    const saveBtn = eduItem.querySelector('.item-save');
    const cancelBtn = eduItem.querySelector('.item-cancel');
    
    editBtn.addEventListener('click', function() {
        const itemForm = eduItem.querySelector('.item-form');
        const itemHeader = eduItem.querySelector('.item-header');
        itemForm.style.display = 'block';
        itemHeader.style.display = 'none';
    });
    
    removeBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to remove this education?')) {
            eduItem.remove();
            
            // Update resume data
            const index = parseInt(eduItem.dataset.index);
            resumeData.education.splice(index, 1);
            
            // Update indices of remaining items
            const remainingItems = document.querySelectorAll('.education-item');
            remainingItems.forEach((item, i) => {
                item.dataset.index = i;
            });
            
            // Update preview
            updateResumePreview();
        }
    });
    
    saveBtn.addEventListener('click', function() {
        const degree = eduItem.querySelector('.edu-degree').value;
        const institution = eduItem.querySelector('.edu-institution').value;
        const startDate = eduItem.querySelector('.edu-start-date').value;
        const endDate = eduItem.querySelector('.edu-end-date').value;
        const description = eduItem.querySelector('.edu-description').value;
        
        // Update header
        eduItem.querySelector('h4').textContent = `${degree} - ${institution}`;
        
        // Update resume data
        const index = parseInt(eduItem.dataset.index);
        resumeData.education[index] = {
            degree,
            institution,
            startDate,
            endDate,
            description
        };
        
        // Hide form, show header
        eduItem.querySelector('.item-form').style.display = 'none';
        eduItem.querySelector('.item-header').style.display = 'flex';
        
        // Update preview
        updateResumePreview();
    });
    
    cancelBtn.addEventListener('click', function() {
        const itemForm = eduItem.querySelector('.item-form');
        const itemHeader = eduItem.querySelector('.item-header');
        
        // If this is a new item with no data, remove it
        const index = parseInt(eduItem.dataset.index);
        if (!resumeData.education[index] || 
            (!resumeData.education[index].degree && !resumeData.education[index].institution)) {
            eduItem.remove();
        } else {
            // Otherwise, just hide the form
            itemForm.style.display = 'none';
            itemHeader.style.display = 'flex';
        }
    });
    
    return eduItem;
}

/**
 * Create a skill tag element
 * @param {string} skill - Skill name
 * @returns {HTMLElement} - Skill tag element
 */
function createSkillTag(skill) {
    const skillTag = document.createElement('div');
    skillTag.className = 'skill-tag';
    
    skillTag.innerHTML = `
        <span>${skill}</span>
        <button type="button" class="skill-remove"><i class="fas fa-times"></i></button>
    `;
    
    // Add event listener for remove button
    const removeBtn = skillTag.querySelector('.skill-remove');
    removeBtn.addEventListener('click', function() {
        // Remove from DOM
        skillTag.remove();
        
        // Remove from resume data
        const skillIndex = resumeData.skills.indexOf(skill);
        if (skillIndex !== -1) {
            resumeData.skills.splice(skillIndex, 1);
        }
        
        // Update preview
        updateResumePreview();
    });
    
    return skillTag;
}

/**
 * Update the resume preview
 */
function updateResumePreview() {
    // Update personal information
    for (const field in resumeData.personal) {
        const elements = document.querySelectorAll(`.resume-${field}`);
        elements.forEach(element => {
            element.textContent = resumeData.personal[field] || '';
        });
    }
    
    // Update summary
    const summaryElements = document.querySelectorAll('.resume-summary');
    summaryElements.forEach(element => {
        element.textContent = resumeData.summary || '';
    });
    
    // Update experience
    const experienceContainer = document.querySelector('.resume-experience-list');
    if (experienceContainer) {
        experienceContainer.innerHTML = '';
        
        resumeData.experience.forEach(exp => {
            const expItem = document.createElement('div');
            expItem.className = 'resume-experience-item';
            
            expItem.innerHTML = `
                <div class="resume-item-header">
                    <h3>${exp.jobTitle || ''}</h3>
                    <div class="resume-item-subheader">
                        <span class="resume-company">${exp.company || ''}</span>
                        <span class="resume-date">${exp.startDate || ''} - ${exp.endDate || 'Present'}</span>
                    </div>
                </div>
                <div class="resume-item-content">
                    <p>${exp.description || ''}</p>
                </div>
            `;
            
            experienceContainer.appendChild(expItem);
        });
    }
    
    // Update education
    const educationContainer = document.querySelector('.resume-education-list');
    if (educationContainer) {
        educationContainer.innerHTML = '';
        
        resumeData.education.forEach(edu => {
            const eduItem = document.createElement('div');
            eduItem.className = 'resume-education-item';
            
            eduItem.innerHTML = `
                <div class="resume-item-header">
                    <h3>${edu.degree || ''}</h3>
                    <div class="resume-item-subheader">
                        <span class="resume-institution">${edu.institution || ''}</span>
                        <span class="resume-date">${edu.startDate || ''} - ${edu.endDate || 'Present'}</span>
                    </div>
                </div>
                <div class="resume-item-content">
                    <p>${edu.description || ''}</p>
                </div>
            `;
            
            educationContainer.appendChild(eduItem);
        });
    }
    
    // Update skills
    const skillsContainer = document.querySelector('.resume-skills-list');
    if (skillsContainer) {
        skillsContainer.innerHTML = '';
        
        resumeData.skills.forEach(skill => {
            const skillItem = document.createElement('div');
            skillItem.className = 'resume-skill-item';
            skillItem.textContent = skill;
            
            skillsContainer.appendChild(skillItem);
        });
    }
}