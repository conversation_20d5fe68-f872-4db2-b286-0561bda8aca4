import React from "react";

const Volunteering = ({
  volunteering = [],
  title = "Volunteering",
  sectionStyle = "",
  titleStyle = "",
  itemTitleStyle = "",
  dateStyle = "",
  positionStyle = "",
  locationStyle = "",
  summaryStyle = "",
}) => {
  if (!volunteering.length) return null;

  return (
    <section className={sectionStyle}>
      <h3 className={`text-lg font-bold mb-2 ${titleStyle}`}>{title}</h3>
      {volunteering.map((item, index) => (
        <div key={index} className="mb-4">
          <div className="flex justify-between">
            <strong className={`text-base ${itemTitleStyle}`}>
              {item.organization}
            </strong>
            <span className={`text-sm italic ${dateStyle}`}>{item.date}</span>
          </div>
          <div className={`text-sm italic ${positionStyle}`}>
            {item.position}
          </div>
          {item.location && (
            <div className={`text-sm ${locationStyle}`}>{item.location}</div>
          )}
          {item.summary && (
            <div
              className={`text-sm mt-1 ${summaryStyle}`}
              dangerouslySetInnerHTML={{ __html: item.summary }}
            />
          )}
        </div>
      ))}
    </section>
  );
};

export default Volunteering;
