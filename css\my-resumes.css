/* My Resumes Page Styles */
.resumes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .resume-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
  }
  
  .resume-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .resume-preview {
    position: relative;
    height: 320px;
    overflow: hidden;
  }
  
  .resume-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
  }
  
  .resume-card:hover .resume-preview img {
    transform: scale(1.05);
  }
  
  .resume-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(25, 65, 75, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .resume-card:hover .resume-overlay {
    opacity: 1;
  }
  
  .resume-info {
    padding: 1.25rem;
  }
  
  .resume-info h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--primary);
  }
  
  .resume-info p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
  }
  
  .resume-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #777;
  }
  
  .resume-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .resume-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #555;
    transition: all 0.2s;
  }
  
  .btn-icon:hover {
    background-color: var(--primary);
    color: #fff;
  }
  
  .btn-icon.delete-resume:hover {
    background-color: #f44336;
  }
  
  .empty-state {
    text-align: center;
    padding: 3rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .empty-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1.5rem;
  }
  
  .empty-state h2 {
    font-size: 1.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
  }
  
  .empty-state p {
    color: #666;
    margin-bottom: 1.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* Responsive styles */
  @media (max-width: 768px) {
    .resumes-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }
  
  @media (max-width: 576px) {
    .resumes-grid {
      grid-template-columns: 1fr;
    }
  
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }
  
  