import React, { useEffect } from "react";
import { useAuthStore } from "../../store/authStore";
import { useResumeStore } from "../../store/useResumeDetailStore";
import { useThemeStore } from "../../store/themeStore";
import { useTypographyStore } from "../../store/themeTypographyStore";

const A4CompactTemplate = ({ exportMode = false, themeOverrides = {} }) => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { resume, fetchResume } = useResumeStore();

  const {
    primaryColor: storePrimary,
    backgroundColor: storeBackground,
    textColor: storeText,
  } = useThemeStore();

  const {
    fontFamily,
    fontVariant,
    fontSize,
    lineHeight,
    hideIcons,
    underlineLinks,
  } = useTypographyStore();

  const {
    primaryColor = storePrimary,
    backgroundColor = exportMode ? "#ffffff" : storeBackground,
    textColor = exportMode ? "#29354d" : storeText,
  } = themeOverrides;

  const variantStyle = {};
  if (fontVariant === "italic") variantStyle.fontStyle = "italic";
  else if (!isNaN(fontVariant)) variantStyle.fontWeight = fontVariant;

  useEffect(() => {
    const linkId = "dynamic-google-font";
    let link = document.getElementById(linkId);
    if (link) link.remove();
    link = document.createElement("link");
    link.id = linkId;
    link.rel = "stylesheet";
    link.href = `https://fonts.googleapis.com/css?family=${fontFamily.replace(
      / /g,
      "+"
    )}:${fontVariant}`;
    document.head.appendChild(link);
  }, [fontFamily, fontVariant]);

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user?.resumeId && !resume) fetchResume(user.resumeId);
  }, [user]);

  if (!resume || !user)
    return exportMode ? null : (
      <p className="text-center mt-20">Loading resume...</p>
    );

  const {
    Title,
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resume;

  const { name } = user;

  return (
    <div
      className="a4-container print-a4-page px-8 py-6"
      style={{
        backgroundColor,
        color: textColor,
        fontFamily,
        fontSize,
        lineHeight,
        ...variantStyle,
      }}
    >
      <div
        className="text-center border-b pb-4 mb-4"
        style={{ borderColor: primaryColor }}
      >
        {ProfilePic && (
          <img
            src={ProfilePic}
            alt="Profile"
            className="w-24 h-24 mx-auto mb-2 rounded-full object-cover"
          />
        )}
        <h1 className="text-2xl font-bold" style={{ color: primaryColor }}>
          {!hideIcons && "👤 "}
          {name}
        </h1>
        {Headline && (
          <p className="italic text-sm" style={variantStyle}>
            {Headline}
          </p>
        )}
        <div className="flex flex-wrap justify-center gap-2 mt-1 text-xs">
          {Email && (
            <span>
              {!hideIcons && "📧 "}
              {Email}
            </span>
          )}
          {Phone && (
            <span>
              | {!hideIcons && "📞 "}
              {Phone}
            </span>
          )}
          {Location && (
            <span>
              | {!hideIcons && "📍 "}
              {Location}
            </span>
          )}
          {Website && (
            <span>
              |{" "}
              <a
                href={Website}
                style={{
                  textDecoration: underlineLinks ? "underline" : "none",
                  color: primaryColor,
                }}
              >
                {Website}
              </a>
            </span>
          )}
        </div>
      </div>

      {summery && (
        <Section title="Summary" primaryColor={primaryColor}>
          <p>{summery}</p>
        </Section>
      )}

      {Experience.length > 0 && (
        <Section title="Experience" primaryColor={primaryColor}>
          {Experience.map((exp) => (
            <Item
              key={exp._id}
              title={`${exp.Position} at ${exp.Company}`}
              subtitle={`${formatDate(exp.StartDate)} – ${formatDate(
                exp.EndDate
              )} | ${exp.Location}`}
              description={exp.Description}
            />
          ))}
        </Section>
      )}

      {Projects.length > 0 && (
        <Section title="Projects" primaryColor={primaryColor}>
          {Projects.map((proj) => (
            <Item
              key={proj._id}
              title={proj.Title}
              subtitle={proj.Link}
              description={proj.Description}
              extra={
                proj.Technologies?.length > 0
                  ? `Tech: ${proj.Technologies.join(", ")}`
                  : ""
              }
            />
          ))}
        </Section>
      )}

      {Education.length > 0 && (
        <Section title="Education" primaryColor={primaryColor}>
          {Education.map((edu) => (
            <Item
              key={edu._id}
              title={`${edu.Degree} at ${edu.Institution}`}
              subtitle={`${formatDate(edu.StartDate)} – ${formatDate(
                edu.EndDate
              )} | ${edu.Location}`}
            />
          ))}
        </Section>
      )}

      {Skills.length > 0 && (
        <Section title="Skills" primaryColor={primaryColor}>
          <ul className="list-disc pl-5 columns-2">
            {Skills.map((s) => (
              <li key={s._id}>{s.Skill}</li>
            ))}
          </ul>
        </Section>
      )}

      {Languages.length > 0 && (
        <Section title="Languages" primaryColor={primaryColor}>
          <ul className="list-disc pl-5">
            {Languages.map((lang) => (
              <li key={lang._id}>
                {lang.Name} - {lang.Proficiency}
              </li>
            ))}
          </ul>
        </Section>
      )}

      {Certifications.length > 0 && (
        <Section title="Certifications" primaryColor={primaryColor}>
          {Certifications.map((cert) => (
            <Item
              key={cert._id}
              title={cert.Title}
              subtitle={`${cert.Issuer} - ${formatDate(cert.Date)}`}
              description={cert.Description}
            />
          ))}
        </Section>
      )}

      {Awards.length > 0 && (
        <Section title="Awards" primaryColor={primaryColor}>
          {Awards.map((award) => (
            <Item
              key={award._id}
              title={award.Title}
              subtitle={`${award.Issuer} - ${formatDate(award.Date)}`}
            />
          ))}
        </Section>
      )}

      {Publications.length > 0 && (
        <Section title="Publications" primaryColor={primaryColor}>
          {Publications.map((pub) => (
            <Item
              key={pub._id}
              title={pub.Title}
              subtitle={`${pub.Publisher} - ${formatDate(pub.Date)}`}
              extra={pub.Website}
            />
          ))}
        </Section>
      )}

      {Volunteering.length > 0 && (
        <Section title="Volunteering" primaryColor={primaryColor}>
          {Volunteering.map((v) => (
            <Item
              key={v._id}
              title={`${v.Position} at ${v.Organization}`}
              subtitle={`${formatDate(v.StartDate)} – ${formatDate(
                v.EndDate
              )} | ${v.Location}`}
              description={v.Description}
            />
          ))}
        </Section>
      )}

      {References.length > 0 && (
        <Section title="References" primaryColor={primaryColor}>
          {References.map((r) => (
            <Item
              key={r._id}
              title={r.Name}
              subtitle={`${r.Position} at ${r.Company}`}
              extra={`${r.Email} | ${r.Phone}`}
            />
          ))}
        </Section>
      )}

      {Interests.length > 0 && (
        <Section title="Interests" primaryColor={primaryColor}>
          <ul className="list-disc pl-5 columns-2">
            {Interests.map((i, idx) => (
              <li key={idx}>{i.Interest || i}</li>
            ))}
          </ul>
        </Section>
      )}

      {Profiles.length > 0 && (
        <Section title="Profiles" primaryColor={primaryColor}>
          <ul className="list-disc pl-5 text-xs">
            {Profiles.map((p, idx) => (
              <li key={p._id || idx}>
                {p.Network}:{" "}
                <a
                  href={p.ProfileLink}
                  style={{
                    textDecoration: underlineLinks ? "underline" : "none",
                    color: primaryColor,
                  }}
                  target="_blank"
                  rel="noreferrer"
                >
                  {p.Username}
                </a>
              </li>
            ))}
          </ul>
        </Section>
      )}
    </div>
  );
};

const Section = ({ title, children, primaryColor }) => (
  <div className="mb-4 break-inside-avoid-page">
    <h2
      className="font-bold uppercase tracking-wider text-sm border-b pb-1 mb-1"
      style={{ borderColor: primaryColor }}
    >
      {title}
    </h2>
    {children}
  </div>
);

const Item = ({ title, subtitle, description, extra }) => (
  <div className="mb-1 break-inside-avoid">
    <p className="font-medium text-sm">{title}</p>
    {subtitle && <p className="text-xs italic">{subtitle}</p>}
    {description && <p className="text-xs">{description}</p>}
    {extra && <p className="text-xs text-gray-600">{extra}</p>}
  </div>
);

const formatDate = (date) => {
  if (!date) return "Present";
  try {
    return new Date(date).toLocaleDateString("en-IN", {
      month: "short",
      year: "numeric",
    });
  } catch {
    return "N/A";
  }
};

export default A4CompactTemplate;
