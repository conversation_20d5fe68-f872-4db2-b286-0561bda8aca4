import React, { useEffect, useState } from "react";
import { useAdminStore } from "../store/useAdminStore";
import { FaChevronDown, FaChevronUp, FaFileAlt } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

const AdminUsers = () => {
    const {
        users,
        fetchAllUsers,
        fetchUserResumes,
        getUserResumes,
        loading,
        error,
    } = useAdminStore();

    const [openUserId, setOpenUserId] = useState(null);
    const navigate = useNavigate();

    const toggleDropdown = async (userId) => {
        const isOpen = openUserId === userId;
        setOpenUserId(isOpen ? null : userId);
        if (!isOpen) await fetchUserResumes(userId);
    };

    useEffect(() => {
        fetchAllUsers();
    }, []);

    return (
        <div className="p-6  min-h-screen">
            <h1 className="text-3xl font-bold text-[#29354d] mb-8 text-center">All Users</h1>

            {loading && <p className="text-gray-500">Loading users...</p>}
            {error && <p className="text-red-500">{error}</p>}
            {!loading && users.length === 0 && <p className="text-gray-400">No users found.</p>}

            <div className="flex flex-wrap gap-6 justify-center">
                {users.map((user) => {
                    const userResumes = getUserResumes(user._id) || [];
                    const isOpen = openUserId === user._id;
                    const avatarUrl = user.ProfilePic || `https://avatar.iran.liara.run/username?username=${encodeURIComponent(user.name || "user")}`;
                    const profileIcons = userResumes?.[0]?.Profiles?.slice(0, 3) || [];

                    return (
                        <div
                            key={user._id}
                            className="bg-white w-[320px] rounded-2xl shadow-md p-6 text-center transition hover:shadow-lg"
                        >
                            <div className="w-24 h-24 rounded-full mx-auto border-4 border-[#ccc] overflow-hidden mb-4">
                                <img src={avatarUrl} alt="avatar" className="w-full h-full object-cover" />
                            </div>

                            <h2 className="text-xl font-semibold text-[#222]">{user.name}</h2>
                            <p className="text-sm text-gray-500 mb-4">{user.role?.toUpperCase() || "USER"}</p>

                            <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                                A developer who created {userResumes.length} resume{userResumes.length !== 1 && "s"}.
                            </p>

                            {/* Email */}
                            <a
                                href={`mailto:${user.email}`}
                                className="block text-sm font-medium text-indigo-600 hover:text-indigo-700 mb-4"
                            >
                                {user.email}
                            </a>

                            {/* Social Icons */}
                            <div className="flex justify-center gap-3 mt-2 mb-4">
                                {profileIcons.map((profile) => (
                                    <a
                                        key={profile._id}
                                        href={profile.ProfileLink}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        title={profile.Network}
                                        className="bg-gray-200 p-2 rounded-full hover:scale-105 transition"
                                    >
                                        <img src={profile.ProfileImage} alt={profile.Network} className="w-5 h-5" />
                                    </a>
                                ))}
                            </div>

                            {/* Toggle Resumes */}
                            <button
                                onClick={() => toggleDropdown(user._id)}
                                className="text-sm text-indigo-500 font-medium hover:underline flex items-center justify-center gap-1 mb-2"
                            >
                                {isOpen ? (
                                    <>
                                        <FaChevronUp className="text-indigo-500" />
                                        Hide Resumes
                                    </>
                                ) : (
                                    <>
                                        <FaChevronDown className="text-indigo-500" />
                                        View Resumes
                                    </>
                                )}
                            </button>

                            {/* Resume List */}
                            {isOpen && (
                                <div className="mt-2 text-left max-h-40 overflow-y-auto border-t pt-2">
                                    {userResumes.map((resume) => (
                                        <div
                                            key={resume._id}
                                            onClick={() => navigate(`/resume/${resume._id}`)}
                                            className="flex items-center gap-2 text-sm text-gray-700 hover:text-indigo-600 cursor-pointer py-1"
                                        >
                                            <FaFileAlt className="text-yellow-500" />
                                            <span>{resume.Title || "Untitled Resume"}</span>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default AdminUsers;
