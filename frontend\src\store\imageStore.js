import { create } from "zustand";
import axios from "axios";
import { useUserInfoStore } from "./userInfoStore";
import { useResumeStore } from "./useResumeDetailStore";

import {API_BASE_URL} from '../lib/constants'
// const API_BASE = "https://resumebuilder-m27v.onrender.com/api/resume/addinfo";
// const API_BASE = "https://resumebuilder-m27v.onrender.com/api/resume/addinfo";
const API_BASE = `${API_BASE_URL}/resume/addinfo`;
export const useImageStore = create((set) => ({
  uploading: false,
  error: null,
  uploadImage: async (file) => {
    const { resume } = useResumeStore.getState();

    if (!resume?._id) {
      set({ error: "Resume ID not found." });
      return null;
    }

    const formData = new FormData();
    formData.append("image", file);

    try {
      set({ uploading: true });

      const res = await axios.post(
        `${API_BASE}/upload-image/${resume._id}`,
        formData,
        {
          withCredentials: true, // ⬅️ Use cookies for auth
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      set({ uploading: false });

      if (res.status === 200) {
        return res.data.imageUrl;
      } else {
        console.error("Upload failed:", res.data?.error || "Unknown error");
        set({ error: res.data?.error || "Upload failed" });
        return null;
      }
    } catch (err) {
      console.error("Upload error:", err);
      set({
        uploading: false,
        error: err.response?.data?.error || "Image upload failed",
      });
      return null;
    }
  },

  deleteImage: async () => {
    const { fetchUserInfo, userInfo } = useUserInfoStore.getState();

    if (!userInfo?.resumeId) {
      set({ error: "Resume ID not found." });
      return;
    }

    try {
      await axios.delete(`${API_BASE}/delete-image/${userInfo.resumeId}`, {
        withCredentials: true,
      });
      await fetchUserInfo();
    } catch (err) {
      console.error("Delete failed", err);
      set({
        error: err.response?.data?.error || "Delete failed",
      });
    }
  },
}));
