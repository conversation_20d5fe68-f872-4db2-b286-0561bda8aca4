const DataCard = ({ title, subtitle, description, onDelete, onEdit, data }) => {
  return (
    <div className="bg-white text-black border border-gray-300 rounded-lg p-4 shadow-sm w-full overflow-hidden">
      <div className="flex flex-col md:flex-row justify-between gap-2 items-start md:items-center mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold break-words">{title}</h3>
          <p className="text-sm text-gray-500 break-words">{subtitle}</p>
        </div>
        <div className="flex gap-4 shrink-0">
          {onEdit && (
            <button
              onClick={() => onEdit(data)}
              className="text-blue-500 hover:text-blue-700 font-medium"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(data)}
              className="text-red-500 hover:text-red-700 font-medium"
            >
              Delete
            </button>
          )}
        </div>
      </div>
      <p className="text-gray-800 text-sm break-words whitespace-pre-line">
        {description}
      </p>
    </div>
  );
};

export default DataCard;
