document.addEventListener("DOMContentLoaded", () => {
    // Sidebar toggle functionality
    const sidebarToggleBtn = document.getElementById("sidebar-toggle-btn")
    const sidebar = document.querySelector(".sidebar")
    const dashboardContainer = document.querySelector(".dashboard-container")
    const mobileSidebarToggle = document.querySelector(".mobile-sidebar-toggle")
  
    // Check if sidebar state is saved in localStorage
    const sidebarCollapsed = localStorage.getItem("sidebarCollapsed") === "true"
  
    // Initialize sidebar state
    if (sidebarCollapsed) {
      sidebar.classList.add("collapsed")
      dashboardContainer.classList.add("collapsed")
    }
  
    // Desktop sidebar toggle
    if (sidebarToggleBtn) {
      sidebarToggleBtn.addEventListener("click", () => {
        sidebar.classList.toggle("collapsed")
        dashboardContainer.classList.toggle("collapsed")
  
        // Save sidebar state to localStorage
        localStorage.setItem("sidebarCollapsed", sidebar.classList.contains("collapsed"))
      })
    }
  
    // Mobile sidebar toggle
    if (mobileSidebarToggle) {
      mobileSidebarToggle.addEventListener("click", () => {
        sidebar.classList.toggle("mobile-active")
      })
  
      // Close sidebar when clicking outside on mobile
      document.addEventListener("click", (event) => {
        if (
          window.innerWidth <= 768 &&
          sidebar.classList.contains("mobile-active") &&
          !sidebar.contains(event.target) &&
          event.target !== mobileSidebarToggle
        ) {
          sidebar.classList.remove("mobile-active")
        }
      })
    }
  
    // Add tooltips to sidebar items when collapsed
    const sidebarNavItems = document.querySelectorAll(".sidebar-nav li a")
  
    sidebarNavItems.forEach((item) => {
      const text = item.querySelector("span").textContent
      const tooltip = document.createElement("div")
      tooltip.className = "nav-tooltip"
      tooltip.textContent = text
      item.parentNode.appendChild(tooltip)
    })
  
    // Charts and other dashboard functionality can be added here
  })
  
  