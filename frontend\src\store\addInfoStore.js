import { create } from "zustand";
import axios from "axios";
import { useResumeStore } from "./useResumeDetailStore";

// const API_BASE = "http://localhost:5000/api/resume/addinfo";
const API_BASE = 'https://resumebuilder-m27v.onrender.com/api/resume/addinfo';

export const useAddInfoStore = create((set) => ({
    loading: false,
    error: null,
    success: null,
    addInfo: async ({ resumeId, section, newData }) => {
        if (!resumeId) {
            set({ error: "Missing resumeId" });
            return false;
        }
        set({ loading: true, error: null, success: null });
        try {
            const res = await axios.post(
                `${API_BASE}/add${section.toLowerCase()}/${resumeId}`,
                newData,
                { withCredentials: true }
            );

            await useResumeStore.getState().fetchResume(resumeId, true);
            set({
                loading: false,
                success: res.data.message || `${section} added successfully`,
            });
            return true;
        } catch (err) {
            set({
                loading: false,
                error:
                    err?.response?.data?.error ||
                    `Failed to add ${section.toLowerCase()}`,
            });
            return false;
        }
    },

    resetStatus: () =>
        set({
            loading: false,
            error: null,
            success: null,
        }),
}));
