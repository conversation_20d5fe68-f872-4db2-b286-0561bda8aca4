<?php
include 'includes/config.php';
include 'includes/db.php';
include 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get template ID from URL
$template_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// If no template ID provided, redirect to templates page
if ($template_id === 0) {
    header('Location: templates.php');
    exit;
}

// Get template data
$template = getTemplateById($template_id);

// Check if template exists
if (!$template) {
    header('Location: templates.php');
    exit;
}

// Get user data for sample content
$user = getUserById($_SESSION['user_id']);

// Create sample resume data for preview
$resume = [
    'title' => 'Sample Resume',
    'professional_title' => 'Professional Title',
    'summary' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
    'template_id' => $template_id
];

// Sample education data
$education = [
    [
        'degree' => 'Bachelor of Science in Computer Science',
        'institution' => 'University of Technology',
        'start_date' => '2015-09-01',
        'end_date' => '2019-05-31',
        'current' => false,
        'description' => 'Studied computer science with a focus on software engineering and artificial intelligence. Graduated with honors.'
    ],
    [
        'degree' => 'Master of Science in Data Science',
        'institution' => 'Tech Institute',
        'start_date' => '2019-09-01',
        'end_date' => '2021-05-31',
        'current' => false,
        'description' => 'Specialized in machine learning and big data analytics. Completed thesis on predictive modeling for healthcare outcomes.'
    ]
];

// Sample experience data
$experience = [
    [
        'position' => 'Senior Software Engineer',
        'company' => 'Tech Solutions Inc.',
        'start_date' => '2021-06-01',
        'end_date' => '2023-12-31',
        'current_job' => true,
        'description' => 'Lead developer for enterprise web applications. Managed a team of 5 developers. Implemented CI/CD pipelines and improved code quality standards.'
    ],
    [
        'position' => 'Software Developer',
        'company' => 'Digital Innovations',
        'start_date' => '2019-06-01',
        'end_date' => '2021-05-31',
        'current_job' => false,
        'description' => 'Developed and maintained web applications using React and Node.js. Collaborated with UX designers to implement responsive interfaces.'
    ]
];

// Sample skills data
$skills = [
    ['name' => 'JavaScript'],
    ['name' => 'React'],
    ['name' => 'Node.js'],
    ['name' => 'Python'],
    ['name' => 'SQL'],
    ['name' => 'Git'],
    ['name' => 'Docker'],
    ['name' => 'AWS'],
    ['name' => 'Agile Methodologies']
];

// Sample projects data
$projects = [
    [
        'title' => 'E-commerce Platform',
        'url' => 'https://example.com/project1',
        'date' => '2022-03-15',
        'description' => 'Developed a full-stack e-commerce platform with React, Node.js, and MongoDB. Implemented payment processing, inventory management, and user authentication.'
    ],
    [
        'title' => 'Data Visualization Dashboard',
        'url' => 'https://example.com/project2',
        'date' => '2021-08-10',
        'description' => 'Created an interactive dashboard for visualizing complex datasets using D3.js and React. Implemented real-time data updates and filtering capabilities.'
    ]
];

// Sample certifications data
$certifications = [
    [
        'name' => 'AWS Certified Solutions Architect',
        'issuer' => 'Amazon Web Services',
        'date' => '2022-05-20',
        'description' => 'Professional certification for designing distributed systems on AWS.'
    ],
    [
        'name' => 'Certified Scrum Master',
        'issuer' => 'Scrum Alliance',
        'date' => '2021-11-15',
        'description' => 'Professional certification for Agile project management using Scrum methodology.'
    ]
];

$page_title = "Preview Template: " . $template['template_name'];
include 'includes/header.php';
?>

<link rel="stylesheet" href="css/template-preview.css">

<div class="template-preview-container">
    <div class="preview-header">
        <div class="preview-title">
        <h1><?php echo $template['template_name']; ?> Template</h1>
        <p>Preview how your resume will look with this template</p>
        </div>
        
        <div class="preview-actions">
            <a href="templates.php" class="preview-action-btn back-btn">
                <i class="fas fa-arrow-left"></i> Back to Templates
            </a>
            <a href="create-resume.php?template_id=<?php echo $template_id; ?>" class="preview-action-btn use-template-btn">
                <i class="fas fa-plus"></i> Use This Template
            </a>
        </div>
    </div>
    
    <div class="preview-content">
        <div class="preview-main">
            <div class="resume-preview-wrapper">
                <div class="resume-preview">
                    <?php 
                    // Determine template class based on template style
                    $templateClass = '';
                    switch($template['style']) {
                        case 'modern':
                            $templateClass = 'template-modern';
                            break;
                        case 'professional':
                            $templateClass = 'template-professional';
                            break;
                        case 'creative':
                            $templateClass = 'template-creative';
                            break;
                        case 'minimal':
                            $templateClass = 'template-minimal';
                            break;
                        case 'modern-dark':
                            $templateClass = 'template-modern-dark';
                            break;
                        default:
                            $templateClass = 'template-modern';
                    }
                    ?>
                    <div class="<?php echo $templateClass; ?>">
                        <?php include "templates/{$template['template_file']}/template.php"; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="preview-sidebar">
            <div class="sidebar-section">
                <h3>Template Details</h3>
                
                <div class="template-details">
                    <div class="detail-item">
                        <span class="detail-label">Name:</span>
                        <span class="detail-value"><?php echo $template['name']; ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Category:</span>
                        <span class="detail-value"><?php echo ucfirst($template['category']); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Style:</span>
                        <span class="detail-value"><?php echo ucfirst($template['style']); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Created:</span>
                        <span class="detail-value"><?php echo date('M d, Y', strtotime($template['created_at'])); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3>Template Features</h3>
                
                <ul class="template-features">
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">Professional design</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">ATS-friendly format</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">Customizable colors</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">Print-ready layout</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">PDF export support</div>
                    </li>
                    <?php if ($template['has_photo']): ?>
                    <li class="feature-item">
                        <div class="feature-icon"><i class="fas fa-check"></i></div>
                        <div class="feature-text">Photo support</div>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <a href="create-resume.php?template_id=<?php echo $template_id; ?>" class="btn btn-primary btn-block">
                    <i class="fas fa-plus"></i> Create Resume with This Template
                </a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

