import React, { useState, useEffect } from "react";

const commonNetworks = [
  "LinkedIn",
  "GitHub",
  "HackerRank",
  "Twitter",
  "Facebook",
  "Instagram",
  "Dribbble",
  "<PERSON>hance",
  "Medium",
  "StackOverflow",
  "Dev.to",
  "LeetCode",
];

const getIconUrl = (network) => {
  if (!network) return null;
  const slug = encodeURIComponent(
    network.trim().toLowerCase().replace(/\s+/g, "")
  );
  return `https://cdn.simpleicons.org/${slug}`;
};

const AddProfileModal = ({ open, onClose, onSave, initial = null }) => {
  const [profile, setProfile] = useState({
    Network: "",
    Username: "",
    ProfileLink: "",
    ProfileImage: "",
  });

  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    if (initial) {
      setProfile({
        Network: initial.Network || "",
        Username: initial.Username || "",
        ProfileLink: initial.ProfileLink || "",
        ProfileImage: initial.ProfileImage || "",
      });
    }
  }, [initial]);

  if (!open) return null;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfile((prev) => ({ ...prev, [name]: value }));

    if (name === "Network") {
      const filtered = commonNetworks.filter((n) =>
        n.toLowerCase().startsWith(value.toLowerCase())
      );
      setSuggestions(filtered);
    }
  };

  const handleSuggestionClick = (network) => {
    setProfile((prev) => ({ ...prev, Network: network.trim() }));
    setSuggestions([]);
  };

  const handleAutoComplete = () => {
    if (
      suggestions.length === 1 &&
      profile.Network.toLowerCase() !== suggestions[0].toLowerCase()
    ) {
      setProfile((prev) => ({ ...prev, Network: suggestions[0].trim() }));
      setSuggestions([]);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") handleAutoComplete();
  };

  const handleBlur = () => handleAutoComplete();

  const handleSubmit = (e) => {
    e.preventDefault();
    const { Network, Username, ProfileLink } = profile;

    if (!Network || !Username || !ProfileLink) {
      alert("Network, Username, and Profile Link are required.");
      return;
    }

    const finalData = {
      ...profile,
      ProfileImage: getIconUrl(Network),
    };

    onSave(finalData, initial?._id || null);
    onClose();

    setProfile({
      Network: "",
      Username: "",
      ProfileLink: "",
      ProfileImage: "",
    });
    setSuggestions([]);
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50">
      <div className="bg-white text-black rounded-lg p-6 w-full max-w-md shadow-lg relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-4 text-xl font-bold text-gray-500 hover:text-black"
        >
          ×
        </button>
        <h2 className="text-xl font-semibold mb-5 text-center">
          {initial ? "Update Profile" : "Add New Profile"}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4 relative">
          <div className="relative">
            <input
              type="text"
              name="Network"
              placeholder="Network (e.g., LinkedIn)"
              value={profile.Network}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              className="w-full border border-gray-300 rounded px-3 py-2"
              autoComplete="off"
            />
            {suggestions.length > 0 && (
              <ul className="absolute z-10 left-0 right-0 mt-1 bg-white border border-gray-200 rounded shadow max-h-40 overflow-y-auto">
                {suggestions.map((item) => (
                  <li
                    key={item}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onMouseDown={() => handleSuggestionClick(item)}
                  >
                    {item}
                  </li>
                ))}
              </ul>
            )}
          </div>

          {profile.Network.trim() && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Preview:</span>
              <img
                src={getIconUrl(profile.Network)}
                alt={`${profile.Network} icon`}
                className="w-6 h-6"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                }}
              />
            </div>
          )}

          <input
            type="text"
            name="Username"
            placeholder="Username"
            value={profile.Username}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2"
          />

          <input
            type="url"
            name="ProfileLink"
            placeholder="Profile Link"
            value={profile.ProfileLink}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-3 py-2"
          />

          <button
            type="submit"
            className="w-full bg-black text-white font-medium py-2 rounded hover:bg-gray-900 transition"
          >
            {initial ? "Update Profile" : "Create Profile"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AddProfileModal;
