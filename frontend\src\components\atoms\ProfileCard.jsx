import React from "react";

const ProfileCard = ({
  name = "<PERSON>",
  email = "<EMAIL>",
  avatarColor = "#29354d",
  accentColor = "#fcc250",
  size = 72,
}) => {
  return (
    <div className="flex items-center bg-white rounded-xl shadow p-4">
      <div
        className="flex items-center justify-center rounded-full font-bold"
        style={{
          width: size,
          height: size,
          background: avatarColor,
          color: accentColor,
          fontSize: size * 0.45,
        }}
      >
        {name?.charAt(0).toUpperCase()}
      </div>
      <div className="ml-4">
        <div className="text-lg font-semibold text-gray-800">{name}</div>
        <div className="text-sm text-gray-500">{email}</div>
      </div>
    </div>
  );
};

export default ProfileCard;
