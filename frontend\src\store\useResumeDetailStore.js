import { create } from 'zustand';
import axios from 'axios';

// const API_BASE = 'http://localhost:5000/api/resume';
const API_BASE= import.meta.env.VITE_API_BASE_URL;

export const useResumeStore = create((set, get) => ({
    resume: null,
    resumes: [], // ✅ added default value
    selectedResume: null,
    selectedResumeId: null,
    loading: false,
    error: null,
    hasFetched: false,

    currentPage: 1,
    itemsPerPage: 5,
    paginatedResumes: [],

    // ✅ Fetch a single resume
    fetchResume: async (resumeId, force = false) => {
        const { hasFetched } = get();
        if (hasFetched && !force) return;

        set({ loading: true, error: null });

        try {
            const token = localStorage.getItem("token");
            const res = await axios.get(`${API_BASE}/resume/${resumeId}`, {
                headers: { Authorization: `Bearer ${token}` },
                withCredentials: true,
            });

            set({
                resume: res.data.resume,
                loading: false,
                hasFetched: true,
            });
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resume",
            });
        }
    },

    // ✅ Fetch resumes for a specific user (used by AdminDashboard)
    fetchUserResumes: async (userId) => {
        set({ loading: true, error: null });
        try {
            const token = localStorage.getItem("token");
            const res = await axios.get(`${API_BASE}/resume/user/${userId}`, {
                headers: { Authorization: `Bearer ${token}` },
                withCredentials: true,
            });
            set({ resumes: res.data.resumes || [], loading: false });
            get().paginateResumes(1); // initialize pagination
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resumes",
            });
        }
    },
    fetchResumeById: async (resumeId) => {
        set({ loading: true, error: null });
        try {
            const token = localStorage.getItem("token");
            const res = await axios.get(`${API_BASE}/resume/${resumeId}`, {
                headers: { Authorization: `Bearer ${token}` },
                withCredentials: true,
            });
            set({ selectedResume: res.data.resume, loading: false });
        } catch (err) {
            set({
                loading: false,
                error: err?.response?.data?.error || "Failed to fetch resume detail",
            });
        }
    },

    deleteResume: async (resumeId) => {
        try {
            const token = localStorage.getItem("token");
            await axios.delete(`${API_BASE}/${resumeId}`, {
                headers: { Authorization: `Bearer ${token}` },
                withCredentials: true,
            });
            // Remove deleted resume from list
            const updated = get().resumes.filter((r) => r._id !== resumeId);
            set({ resumes: updated });
            get().paginateResumes(get().currentPage);
        } catch (err) {
            console.error("Failed to delete resume:", err);
        }
    },

    setSelectedResumeId: (id) => set({ selectedResumeId: id }),

    updateResumeState: (newResume) => set({ resume: newResume }),

    clearResume: () =>
        set({
            resume: null,
            selectedResume: null,
            resumes: [],
            selectedResumeId: null,
            loading: false,
            error: null,
            hasFetched: false,
        }),

    // Pagination logic
    paginateResumes: (page) => {
        const { resumes, itemsPerPage } = get();
        const start = (page - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        set({
            currentPage: page,
            paginatedResumes: resumes.slice(start, end),
        });
    },

    updateBasicsField: async (key, value) => {
        const { resume } = get();
        if (!resume?._id) return;
        try {
            const token = localStorage.getItem("token");
            const res = await axios.patch(
                `${API_BASE}/resume/addinfo/update-basics/${resume._id}`,
                { key, value },
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                    withCredentials: true,
                }
            );
            set({ resume: res.data.resume });
        } catch (err) {
            console.error("Failed to update basics field:", err);
        }
    },
}));
