<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
                        
// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);

// Check if profile is complete
// if (!isProfileComplete($user_id)) {
//     setMessage('Please complete your profile before creating a resume.', 'error');
//     redirect('templates.php');
// }

// Get template ID from URL
$template_id = isset($_GET['template_id']) ? (int)$_GET['template_id'] : 0;

// Check if template exists
$db->query("SELECT * FROM resume_templates WHERE id = :id");
$db->bind(':id', $template_id);
$template = $db->single();

if (!$template) {
    setMessage('Template not found.', 'error');
    redirect('templates.php');
}

// Get user data for resume
$user_skills = getUserSkills($user_id);
$user_experience = getUserWorkExperience($user_id);
$user_education = getUserEducation($user_id);
$user_projects = getUserProjects($user_id);
$user_certifications = getUserCertifications($user_id);

// Check if resume already exists
$resume_id = 0;
$resume_data = [];

if (isset($_GET['resume_id'])) {
    $resume_id = (int)$_GET['resume_id'];
    
    // Get resume data
    $db->query("SELECT * FROM user_resumes WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $resume_id);
    $db->bind(':user_id', $user_id);
    $resume = $db->single();
    
    if ($resume) {
        $resume_data = json_decode($resume['resume_data'], true);
    } else {
        $resume_id = 0;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $resume_name = sanitize($_POST['resume_name']);
    $resume_data = $_POST['resume_data'];
    
    // Validate form data
    if (empty($resume_name)) {
        setMessage('Resume name is required.', 'error');
    } else {
        // Save resume
        if ($resume_id > 0) {
            // Update existing resume
            $db->query("UPDATE user_resumes SET 
                resume_name = :resume_name, 
                resume_data = :resume_data, 
                updated_at = NOW() 
                WHERE id = :id AND user_id = :user_id");
            $db->bind(':resume_name', $resume_name);
            $db->bind(':resume_data', $resume_data);
            $db->bind(':id', $resume_id);
            $db->bind(':user_id', $user_id);
            
            if ($db->execute()) {
                setMessage('Resume updated successfully.', 'success');
                redirect('my-resumes.php');
            } else {
                setMessage('Failed to update resume.', 'error');
            }
        } else {
            // Create new resume
            $db->query("INSERT INTO user_resumes (user_id, template_id, resume_name, resume_data, created_at, updated_at) 
                VALUES (:user_id, :template_id, :resume_name, :resume_data, NOW(), NOW())");
            $db->bind(':user_id', $user_id);
            $db->bind(':template_id', $template_id);
            $db->bind(':resume_name', $resume_name);
            $db->bind(':resume_data', $resume_data);
            
            if ($db->execute()) {
                $resume_id = $db->lastInsertId();
                setMessage('Resume created successfully.', 'success');
                redirect('my-resumes.php');
            } else {
                setMessage('Failed to create resume.', 'error');
            }
        }
    }
}

// Set default resume name if new resume
$resume_name = isset($resume['resume_name']) ? $resume['resume_name'] : $profile['first_name'] . "'s " . $template['template_name'] . " Resume";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Resume Editor</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Montserrat:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Open+Sans:wght@300;400;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/resume-editor.css">
</head>
<body>
    <div class="editor-container">
        <header class="editor-header">
            <div class="logo">
                <h1>Resume Builder</h1>
            </div>
            <div class="actions">
                <button id="save-resume" class="btn primary"><i class="fas fa-save"></i> Save</button>
                <button id="preview-btn" class="btn secondary"><i class="fas fa-eye"></i> Preview</button>
                <button id="download-pdf" class="btn secondary"><i class="fas fa-download"></i> Download PDF</button>
            </div>
        </header>
        
        <div class="editor-content">
            <div class="editor-sidebar">
                <div class="sidebar-section">
                    <h3>Resume Sections</h3>
                    <div class="section-list" id="available-sections">
                        <div class="section-item" data-section-type="summary">
                            <i class="fas fa-user-circle"></i> Summary
                        </div>
                        <div class="section-item" data-section-type="experience">
                            <i class="fas fa-briefcase"></i> Experience
                        </div>
                        <div class="section-item" data-section-type="education">
                            <i class="fas fa-graduation-cap"></i> Education
                        </div>
                        <div class="section-item" data-section-type="skills">
                            <i class="fas fa-tools"></i> Skills
                        </div>
                        <div class="section-item" data-section-type="projects">
                            <i class="fas fa-project-diagram"></i> Projects
                        </div>
                        <div class="section-item" data-section-type="certifications">
                            <i class="fas fa-certificate"></i> Certifications
                        </div>
                        <div class="section-item" data-section-type="languages">
                            <i class="fas fa-language"></i> Languages
                        </div>
                        <div class="section-item" data-section-type="interests">
                            <i class="fas fa-heart"></i> Interests
                        </div>
                        <div class="section-item" data-section-type="custom">
                            <i class="fas fa-plus-circle"></i> Add Custom Section
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h3>Styling Options</h3>
                    <div class="style-options">
                        <div class="style-group">
                            <label>Template</label>
                            <select id="template-selector">
                                <option value="modern">Modern</option>
                                <option value="classic">Classic</option>
                                <option value="minimalist">Minimalist</option>
                                <option value="creative">Creative</option>
                            </select>
                        </div>
                        
                        <div class="style-group">
                            <label>Color Scheme</label>
                            <div class="color-options">
                                <div class="color-option active" data-color="#2c3e50" style="background-color: #2c3e50;"></div>
                                <div class="color-option" data-color="#3498db" style="background-color: #3498db;"></div>
                                <div class="color-option" data-color="#27ae60" style="background-color: #27ae60;"></div>
                                <div class="color-option" data-color="#e74c3c" style="background-color: #e74c3c;"></div>
                                <div class="color-option" data-color="#9b59b6" style="background-color: #9b59b6;"></div>
                                <div class="color-option custom" data-color="custom">
                                    <input type="color" id="custom-color" value="#2c3e50">
                                </div>
                            </div>
                        </div>
                        
                        <div class="style-group">
                            <label>Font Family</label>
                            <select id="font-family-selector">
                                <option value="Roboto, sans-serif">Roboto</option>
                                <option value="Montserrat, sans-serif">Montserrat</option>
                                <option value="Lato, sans-serif">Lato</option>
                                <option value="Open Sans, sans-serif">Open Sans</option>
                                <option value="Raleway, sans-serif">Raleway</option>
                            </select>
                        </div>
                        
                        <div class="style-group">
                            <label>Font Size</label>
                            <div class="font-size-controls">
                                <button id="decrease-font" class="btn icon-btn"><i class="fas fa-minus"></i></button>
                                <span id="current-font-size">Medium</span>
                                <button id="increase-font" class="btn icon-btn"><i class="fas fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="editor-main">
                <form id="resume-form" method="post" action="save-resume.php">
                    <input type="hidden" id="resume_id" name="resume_id" value="<?php echo isset($_GET['resume_id']) ? (int)$_GET['resume_id'] : 0; ?>">
                    <input type="hidden" id="template_id" name="template_id" value="<?php echo $template_id; ?>">
                    <input type="hidden" id="resume_data" name="resume_data" value="">
                    
                    <div class="form-group">
                        <label for="resume_name">Resume Name</label>
                        <input type="text" id="resume_name" name="resume_name" value="<?php echo htmlspecialchars($resume_name); ?>" required>
                    </div>
                    
                    <div class="resume-sections-container" id="resume-sections">
                        <!-- Header Section (Always Present) -->
                        <div class="editor-section active" data-section-id="header" data-section-type="header">
                            <div class="section-header">
                                <h3><i class="fas fa-id-card"></i> Personal Information</h3>
                                <div class="section-controls">
                                    <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                                </div>
                            </div>
                            <div class="section-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="full_name">Full Name</label>
                                        <input type="text" id="full_name" data-field="full_name" value="John Doe">
                                    </div>
                                    <div class="form-group">
                                        <label for="job_title">Job Title</label>
                                        <input type="text" id="job_title" data-field="job_title" value="Software Developer">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="email">Email</label>
                                        <input type="email" id="email" data-field="email" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="phone">Phone</label>
                                        <input type="tel" id="phone" data-field="phone" value="(*************">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="location">Location</label>
                                        <input type="text" id="location" data-field="location" value="New York, NY">
                                    </div>
                                    <div class="form-group">
                                        <label for="website">Website/LinkedIn</label>
                                        <input type="text" id="website" data-field="website" value="linkedin.com/in/johndoe">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Dynamic sections will be added here -->
                    </div>
                </form>
            </div>
            
            <div class="resume-preview">
                <div class="preview-header">
                    <h3>Preview</h3>
                    <button id="close-preview" class="btn icon-btn"><i class="fas fa-times"></i></button>
                </div>
                <div class="preview-content">
                    <div id="resume-template" class="modern-resume">
                        <!-- Resume template will be rendered here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Templates for dynamic content -->
    <template id="summary-section-template">
        <div class="editor-section" data-section-type="summary">
            <div class="section-header">
                <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                <div class="section-controls">
                    <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                    <button type="button" class="section-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label for="summary">Summary</label>
                    <textarea id="summary" data-field="summary" rows="4">Experienced software developer with over 5 years of expertise in web development, mobile applications, and database management. Skilled in JavaScript, Python, and SQL with a track record of delivering high-quality software solutions that meet business objectives.</textarea>
                </div>
            </div>
        </div>
    </template>
    
    <template id="experience-section-template">
        <div class="editor-section" data-section-type="experience">
            <div class="section-header">
                <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                <h3><i class="fas fa-briefcase"></i> Experience</h3>
                <div class="section-controls">
                    <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                    <button type="button" class="section-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="section-content">
                <div id="experience-items">
                    <!-- Experience items will be added here -->
                </div>
                <button type="button" id="add-experience" class="btn add-item-btn"><i class="fas fa-plus"></i> Add Experience</button>
            </div>
        </div>
    </template>
    
    <template id="education-section-template">
        <div class="editor-section" data-section-type="education">
            <div class="section-header">
                <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                <h3><i class="fas fa-graduation-cap"></i> Education</h3>
                <div class="section-controls">
                    <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                    <button type="button" class="section-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="section-content">
                <div id="education-items">
                    <!-- Education items will be added here -->
                </div>
                <button type="button" id="add-education" class="btn add-item-btn"><i class="fas fa-plus"></i> Add Education</button>
            </div>
        </div>
    </template>
    
    <template id="skills-section-template">
        <div class="editor-section" data-section-type="skills">
            <div class="section-header">
                <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                <h3><i class="fas fa-tools"></i> Skills</h3>
                <div class="section-controls">
                    <button type="button" class="section-toggle"><i class="fas fa-chevron-up"></i></button>
                    <button type="button" class="section-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label>Add Skill</label>
                    <div class="add-skill-container">
                        <input type="text" id="add-skill-input" placeholder="Enter a skill">
                        <button type="button" id="add-skill-btn" class="btn"><i class="fas fa-plus"></i></button>
                    </div>
                </div>
                <div id="skills-container" class="skills-container">
                    <!-- Skills will be added here -->
                </div>
            </div>
        </div>
    </template>
    
    <template id="experience-item-template">
        <div class="experience-item">
            <div class="item-header">
                <h4>Job Title at Company</h4>
                <div class="item-controls">
                    <button type="button" class="item-edit"><i class="fas fa-edit"></i></button>
                    <button type="button" class="item-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-form" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label>Job Title</label>
                        <input type="text" class="exp-job-title" value="Senior Software Developer">
                    </div>
                    <div class="form-group">
                        <label>Company</label>
                        <input type="text" class="exp-company" value="Tech Innovations Inc.">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Start Date</label>
                        <input type="text" class="exp-start-date" placeholder="e.g., Jan 2020">
                    </div>
                    <div class="form-group">
                        <label>End Date</label>
                        <input type="text" class="exp-end-date" placeholder="e.g., Present">
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="exp-description" rows="4" placeholder="Describe your responsibilities and achievements"></textarea>
                </div>
                <div class="form-group">
                    <label>Bullet Points</label>
                    <div class="bullet-points-container">
                        <div class="bullet-point">
                            <input type="text" value="Led a team of 5 developers to build a customer-facing web application.">
                            <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="bullet-point">
                            <input type="text" value="Implemented CI/CD pipelines that reduced deployment time by 60%.">
                            <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="bullet-point">
                            <input type="text" value="Collaborated with cross-functional teams to define product requirements and roadmap.">
                            <button type="button" class="remove-bullet"><i class="fas fa-times"></i></button>
                        </div>
                    </div>
                    <button type="button" class="add-bullet btn"><i class="fas fa-plus"></i> Add Bullet Point</button>
                </div>
                <div class="form-actions">
                    <button type="button" class="item-save btn primary">Save</button>
                    <button type="button" class="item-cancel btn">Cancel</button>
                </div>
            </div>
        </div>
    </template>
    
    <template id="education-item-template">
        <div class="education-item">
            <div class="item-header">
                <h4>Degree - Institution</h4>
                <div class="item-controls">
                    <button type="button" class="item-edit"><i class="fas fa-edit"></i></button>
                    <button type="button" class="item-remove"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <div class="item-form" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label>Degree</label>
                        <input type="text" class="edu-degree" value="Master of Computer Science">
                    </div>
                    <div class="form-group">
                        <label>Institution</label>
                        <input type="text" class="edu-institution" value="Massachusetts Institute of Technology">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Start Date</label>
                        <input type="text" class="edu-start-date" placeholder="e.g., 2016">
                    </div>
                    <div class="form-group">
                        <label>End Date</label>
                        <input type="text" class="edu-end-date" placeholder="e.g., 2018">
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="edu-description" rows="3">Focused on AI, machine learning, and full-stack development.</textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="item-save btn primary">Save</button>
                    <button type="button" class="item-cancel btn">Cancel</button>
                </div>
            </div>
        </div>
    </template>
    
    <template id="skill-tag-template">
        <div class="skill-tag">
            <span class="skill-name"></span>
            <button type="button" class="skill-remove"><i class="fas fa-times"></i></button>
        </div>
    </template>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
    <script src="js/resume-editor.js"></script>
</body>
</html>
