// ManagementBar.jsx
import React, { useState, useRef, useEffect } from "react";
import Menubar from "./Menubar";
import Template from "./components/Template";
import Layout from "./components/Layout";
import Export from "./components/Export";
import Page from "./components/Page";
import Typography from "./components/Typography";
import Theme from "./components/Theme";

const MIN_WIDTH = 60;
const MAX_WIDTH = 300;

const ManagementBar = () => {
  const [activeSection, setActiveSection] = useState("Template");
  const [sidebarWidth, setSidebarWidth] = useState(120);
  const sidebarRef = useRef(null);
  const isResizing = useRef(false);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing.current) return;

      const newWidth = Math.min(
        Math.max(window.innerWidth - e.clientX, MIN_WIDTH),
        MAX_WIDTH
      );
      setSidebarWidth(newWidth);
    };

    const handleMouseUp = () => {
      isResizing.current = false;
      document.body.style.cursor = "default";
    };

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, []);

  const handleMouseDown = () => {
    isResizing.current = true;
    document.body.style.cursor = "col-resize";
  };

  const renderSection = () => {
    switch (activeSection) {
      case "Template":
        return <Template />;
      case "Layout":
        return <Layout />;
      case "Margin":
        return <Page />;
      case "Theme":
        return <Theme />;
      case "Typography":
      case "Text":
        return <Typography />;
      case "Download":
        return <Export />;
      // case "Custom CSS":
      //   return (
      //     <div>
      //       <h2 className="text-lg font-semibold">Custom CSS Section</h2>
      //       <p className="text-gray-600">Custom CSS editor will appear here.</p>
      //     </div>
      //   );
      default:
        return <Template />;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-white text-black">
      {/* Main Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        <h1 className="text-2xl font-bold text-[#1e293b]">Management</h1>
        <div className="mt-4">{renderSection()}</div>
      </div>

      {/* Right Sidebar */}
      <div
        ref={sidebarRef}
        style={{ width: sidebarWidth }}
        className="flex-shrink-0 bg-white border-l border-gray-200 relative"
      >
        <Menubar
          activeLabel={activeSection}
          onSelect={setActiveSection}
          collapsed={sidebarWidth < 90}
        />

        {/* Resizer */}
        <div
          onMouseDown={handleMouseDown}
          className="absolute top-0 left-0 w-1 h-full cursor-col-resize z-10"
          style={{ backgroundColor: "transparent" }}
        />
      </div>
    </div>
  );
};

export default ManagementBar;
