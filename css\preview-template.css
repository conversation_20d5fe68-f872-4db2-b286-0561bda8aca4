/* Preview Template Page Styles */
.preview-template-container {
    background-color: #f5f7fa;
  }
  
  .preview-header {
    background-color: white;
    padding: 3rem 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    position: relative;
  }
  
  .preview-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .preview-info {
    max-width: 600px;
  }
  
  .preview-info h1 {
    margin-bottom: 0.75rem;
    font-size: 2rem;
  }
  
  .preview-info p {
    color: var(--gray);
    margin-bottom: 1.5rem;
  }
  
  .template-meta {
    display: flex;
    gap: 2rem;
    align-items: center;
  }
  
  .template-rating {
    display: flex;
    align-items: center;
    color: var(--warning);
  }
  
  .template-rating i {
    margin-right: 0.25rem;
  }
  
  .rating-value {
    margin-left: 0.5rem;
    font-weight: 600;
    color: var(--dark);
  }
  
  .template-downloads {
    display: flex;
    align-items: center;
    color: var(--gray);
  }
  
  .template-downloads i {
    margin-right: 0.5rem;
  }
  
  .preview-actions {
    display: flex;
    gap: 1rem;
  }
  
  .preview-content {
    padding: 4rem 0;
  }
  
  .preview-main {
    display: flex;
    flex-direction: column;
    gap: 3rem;
  }
  
  .template-preview-frame {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    height: 800px;
    transition: all 0.3s ease;
  }
  
  .template-preview-frame:hover {
    box-shadow: var(--shadow-lg);
  }
  
  .template-preview-frame iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  
  .template-features {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
  }
  
  .template-features h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
  
  .feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    list-style: none;
    padding: 0;
  }
  
  .feature-list li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
  }
  
  .feature-list li i {
    color: var(--success);
    margin-right: 0.75rem;
  }
  
  .preview-content .container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
  
  .template-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
  
  .sidebar-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
  }
  
  .sidebar-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    position: relative;
  }
  
  .sidebar-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary);
    border-radius: 3px;
  }
  
  .info-list {
    list-style: none;
    padding: 0;
  }
  
  .info-list li {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .info-list li:last-child {
    border-bottom: none;
  }
  
  .info-label {
    color: var(--gray);
    font-weight: 500;
  }
  
  .info-value {
    font-weight: 600;
  }
  
  .color-schemes {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }
  
  .color-preview:hover {
    transform: scale(1.1);
    border-color: rgba(0, 0, 0, 0.1);
  }
  
  .color-preview.blue {
    background-color: var(--blue);
  }
  
  .color-preview.green {
    background-color: var(--green);
  }
  
  .color-preview.purple {
    background-color: var(--purple);
  }
  
  .color-preview.red {
    background-color: var(--red);
  }
  
  .color-preview.orange {
    background-color: var(--orange);
  }
  
  .color-preview.teal {
    background-color: var(--teal);
  }
  
  .similar-templates {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .similar-template {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
  .similar-template-img {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .similar-template-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .similar-template-info {
    flex: 1;
  }
  
  .similar-template-info h4 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }
  
  .template-cta {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
  }
  
  .template-cta h2 {
    margin-bottom: 1rem;
    font-size: 2rem;
  }
  
  .template-cta p {
    max-width: 600px;
    margin: 0 auto 2rem;
    opacity: 0.9;
  }
  
  @media (max-width: 992px) {
    .preview-header-content {
      flex-direction: column;
      text-align: center;
      gap: 2rem;
    }
    
    .preview-info {
      max-width: 100%;
    }
    
    .template-meta {
      justify-content: center;
    }
    
    .preview-content .container {
      grid-template-columns: 1fr;
    }
    
    .template-preview-frame {
      height: 700px;
    }
  }
  
  @media (max-width: 768px) {
    .preview-actions {
      flex-direction: column;
      width: 100%;
    }
    
    .preview-actions .btn {
      width: 100%;
    }
    
    .feature-list {
      grid-template-columns: 1fr;
    }
    
    .template-preview-frame {
      height: 600px;
    }
  }
  
  @media (max-width: 576px) {
    .template-meta {
      flex-direction: column;
      gap: 1rem;
    }
    
    .template-preview-frame {
      height: 500px;
    }
    
    .template-cta h2 {
      font-size: 1.5rem;
    }
  }