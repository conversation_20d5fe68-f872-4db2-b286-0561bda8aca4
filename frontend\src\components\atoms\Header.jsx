import React, { useEffect, useState } from "react";
import { Menu, X, User } from "react-feather";
import { Link, useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, isAuthenticated, fetchCurrentUser, logout } = useAuthStore();
  const navigate = useNavigate();

  const links = [
    { name: "Home", path: "/" },
    { name: "Resumes", path: "/resumes" },
    { name: "Templates", path: "/templates" },
  ];

  useEffect(() => {
    fetchCurrentUser();
  }, []);

  const avatarUrl =
    user?.ProfilePic ||
    `https://avatar.iran.liara.run/username?username=${encodeURIComponent(
      user?.name || "user"
    )}`;

  const handleLogout = async () => {
    await logout();
    navigate("/login");
  };

  return (
    <header className="bg-white text-black shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto flex justify-between items-center p-4">
        {/* Left Side - Logo */}
        <Link to="/" className="text-2xl font-bold">
          {/* MyLogo */}
        </Link>

        {/* Right Side Links (Desktop) */}
        <div className="hidden md:flex items-center space-x-8">
          {links.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              className="hover:text-gray-600 transition"
            >
              {link.name}
            </Link>
          ))}

          {!isAuthenticated ? (
            <Link
              to="/login"
              className="flex items-center space-x-2 hover:text-gray-600 transition"
            >
              <User className="h-5 w-5" />
              <span>Login / Register</span>
            </Link>
          ) : (
            <div className="relative group">
              <img
                src={avatarUrl}
                alt="avatar"
                className="w-9 h-9 rounded-full border-2 border-yellow-400 cursor-pointer"
              />
              <div className="absolute right-0 mt-2 w-40 bg-white border rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity z-50">
                <div className="px-4 py-2 text-sm font-medium border-b">
                  {user.name}
                </div>
                <Link
                  to="/profile"
                  className="block px-4 py-2 text-sm hover:bg-gray-100"
                >
                  Profile
                </Link>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="focus:outline-none"
          >
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu Links */}
      {isOpen && (
        <div className="md:hidden flex flex-col items-start space-y-4 p-4 bg-gray-100">
          {links.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              className="w-full text-black hover:text-gray-600"
              onClick={() => setIsOpen(false)}
            >
              {link.name}
            </Link>
          ))}

          {!isAuthenticated ? (
            <Link
              to="/login"
              className="flex items-center space-x-2 hover:text-gray-600"
              onClick={() => setIsOpen(false)}
            >
              <User className="h-5 w-5" />
              <span>Login / Register</span>
            </Link>
          ) : (
            <div className="flex flex-col w-full">
              <div className="flex items-center gap-2 px-2 py-2">
                <img
                  src={avatarUrl}
                  alt="avatar"
                  className="w-8 h-8 rounded-full border border-yellow-500"
                />
                <span className="text-sm">{user.name}</span>
              </div>
              <Link
                to="/profile"
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 text-sm hover:bg-gray-200"
              >
                Profile
              </Link>
              <button
                onClick={() => {
                  handleLogout();
                  setIsOpen(false);
                }}
                className="px-4 py-2 text-sm text-left hover:bg-gray-200"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      )}
    </header>
  );
};

export default Header;
