/* Base Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --text-color: #333333;
    --text-light-color: #666666;
    --background-color: #ffffff;
    --border-color: #e0e0e0;
    --hover-color: #f5f5f5;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: "Inter", sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    line-height: 1.6;
  }
  
  /* Layout */
  .app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }
  
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
  }
  
  .header-actions {
    display: flex;
    gap: 1rem;
  }
  
  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  
  /* Sidebar */
  .sidebar {
    width: 320px;
    background-color: #fff;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .sidebar-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tab-btn {
    flex: 1;
    padding: 0.75rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-light-color);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .tab-btn:hover {
    background-color: var(--hover-color);
  }
  
  .tab-btn.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 500;
  }
  
  .tab-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
  }
  
  .tab-pane {
    display: none;
  }
  
  .tab-pane.active {
    display: block;
  }
  
  /* Accordion */
  .accordion-item {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0.5rem;
  }
  
  .accordion-header {
    padding: 0.75rem;
    background-color: var(--hover-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    border-radius: 4px;
  }
  
  .accordion-content {
    padding: 1rem;
    display: none;
  }
  
  .accordion-item.active .accordion-content {
    display: block;
  }
  
  .accordion-item.active .accordion-header i {
    transform: rotate(180deg);
  }
  
  /* Form Elements */
  .form-group {
    margin-bottom: 1rem;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light-color);
  }
  
  .form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
  }
  
  .form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
  }
  
  textarea.form-control {
    resize: vertical;
    min-height: 80px;
  }
  
  .form-control-file {
    display: none;
  }
  
  /* Buttons */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    gap: 0.5rem;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1a252f;
  }
  
  .btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
  }
  
  .btn-outline:hover {
    background-color: var(--hover-color);
  }
  
  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .btn-block {
    display: block;
    width: 100%;
  }
  
  .btn-icon {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light-color);
    transition: color 0.2s ease;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .btn-icon:hover {
    color: var(--primary-color);
  }
  
  .btn:disabled,
  .btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  /* Section Items */
  .section-item {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 1rem;
    background-color: #fff;
  }
  
  .section-item-header {
    padding: 0.5rem;
    background-color: var(--hover-color);
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
  }
  
  .drag-handle {
    cursor: move;
    padding: 0.25rem;
    margin-right: 0.5rem;
    color: var(--text-light-color);
  }
  
  .item-title {
    flex: 1;
    font-weight: 500;
  }
  
  .item-actions {
    display: flex;
    gap: 0.25rem;
  }
  
  .section-item-content {
    padding: 1rem;
  }
  
  /* Skills */
  .skill-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .skill-item .form-control {
    flex: 1;
    margin-right: 0.5rem;
  }
  
  /* Color Picker */
  .color-picker {
    display: flex;
    align-items: center;
  }
  
  .color-picker input[type="color"] {
    width: 40px;
    height: 40px;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
  }
  
  .color-value {
    flex: 1;
  }
  
  /* Range Slider */
  .range-slider {
    display: flex;
    align-items: center;
  }
  
  .range {
    flex: 1;
    margin-right: 1rem;
  }
  
  .range-value {
    width: 50px;
    text-align: center;
    font-size: 0.875rem;
  }
  
  /* Section Order */
  .section-order {
    border: 1px solid var(--border-color);
    border-radius: 4px;
  }
  
  .order-item {
    padding: 0.75rem;
    background-color: #fff;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    cursor: move;
  }
  
  .order-item:last-child {
    border-bottom: none;
  }
  
  .help-text {
    font-size: 0.75rem;
    color: var(--text-light-color);
    margin-bottom: 0.5rem;
  }
  
  /* Photo Upload */
  .photo-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .photo-preview {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: var(--hover-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    overflow: hidden;
  }
  
  .photo-preview i {
    font-size: 2rem;
    color: var(--text-light-color);
  }
  
  .photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* Resume Preview */
  .resume-preview-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f0f2f5;
    overflow: hidden;
  }
  
  .preview-toolbar {
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
  }
  
  .zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  #zoom-level {
    font-size: 0.875rem;
    width: 50px;
    text-align: center;
  }
  
  .preview-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .resume-preview-wrapper {
    flex: 1;
    overflow: auto;
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  
  .resume-preview {
    background-color: #fff;
    box-shadow: 0 4px 12px var(--shadow-color);
    transition: transform 0.3s ease;
    transform-origin: top center;
    width: 210mm; /* A4 width */
    min-height: 297mm; /* A4 height */
    padding: 20mm;
  }
  
  /* Modal */
  .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-color);
    animation: modalFadeIn 0.3s;
  }
  
  .modal-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
  }
  
  .close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light-color);
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
  }
  
  /* Templates Grid */
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
  }
  
  .template-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px var(--shadow-color);
  }
  
  .template-preview {
    position: relative;
    overflow: hidden;
    padding-top: 140%; /* Aspect ratio */
  }
  
  .template-preview img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .template-card:hover .template-overlay {
    opacity: 1;
  }
  
  .template-info {
    padding: 1rem;
  }
  
  .template-info h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .template-info p {
    font-size: 0.875rem;
    color: var(--text-light-color);
  }
  
  /* Style Sections */
  .style-section {
    margin-bottom: 1.5rem;
  }
  
  .style-section h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
  }
  
  /* Animations */
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Resume Templates */
  .resume-modern .resume-header {
    background-color: var(--primary-color);
    color: white;
    padding: 40px;
  }
  
  .resume-modern .resume-name {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .resume-modern .resume-title {
    font-size: 20px;
    margin-bottom: 25px;
    font-weight: 300;
  }
  
  .resume-modern .contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    font-size: 14px;
  }
  
  .resume-modern .contact-item {
    margin-right: 15px;
    margin-bottom: 10px;
  }
  
  .resume-modern .resume-section {
    margin-bottom: 30px;
  }
  
  .resume-modern .section-title {
    font-size: 22px;
    color: var(--primary-color);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
  }
  
  .resume-modern .section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
  }
  
  .resume-modern .section-item {
    margin-bottom: 25px;
  }
  
  .resume-modern .item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .resume-modern .item-title {
    font-weight: bold;
    font-size: 18px;
    color: var(--primary-color);
  }
  
  .resume-modern .item-subtitle {
    font-style: italic;
    margin-bottom: 8px;
    color: #7f8c8d;
  }
  
  .resume-modern .item-date {
    color: #7f8c8d;
    font-size: 14px;
  }
  
  .resume-modern .item-description {
    line-height: 1.6;
  }
  
  .resume-modern .skills-list {
    display: flex;
    flex-wrap: wrap;
  }
  
  .resume-modern .skill-tag {
    background-color: #f2f2f2;
    padding: 5px 12px;
    border-radius: 15px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
  }
  
  /* Responsive */
  @media (max-width: 1024px) {
    .sidebar {
      width: 280px;
    }
  }
  
  @media (max-width: 768px) {
    .main-content {
      flex-direction: column;
    }
  
    .sidebar {
      width: 100%;
      height: 50%;
    }
  
    .resume-preview-container {
      height: 50%;
    }
  
    .modal-content {
      width: 95%;
    }
  }
  