import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { useAuthStore } from "../../store/authStore";
import HomepageLayout from "../../components/Layout/HomepageLayout";

const OTPVerifypage = () => {
  const { verifyOtp, resendOtp } = useAuthStore();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [email, setEmail] = useState("");
  const [otpArray, setOtpArray] = useState(["", "", "", "", "", ""]);
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isAutoVerifying, setIsAutoVerifying] = useState(false);
  const [autoVerified, setAutoVerified] = useState(false);
  const inputRefs = useRef([]);

  // Auto-verification from URL query
  useEffect(() => {
    const queryEmail = searchParams.get("email");
    const queryOtp = searchParams.get("otp");

    if (queryEmail && queryOtp && queryOtp.length === 6) {
      setIsAutoVerifying(true);
      verifyOtp({ email: queryEmail, otp: queryOtp })
        .then(() => {
          toast.success("Email verified successfully!");
          setAutoVerified(true);
          setTimeout(() => navigate("/login"), 2000);
        })
        .catch((err) => {
          toast.error(err.message || "Auto verification failed.");
          setIsAutoVerifying(false);
          setEmail(queryEmail);
        });
    } else if (queryEmail) {
      setEmail(queryEmail);
    }
  }, [searchParams]);

  // Resend OTP timer countdown
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer((prev) => prev - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleOtpChange = (e, index) => {
    const value = e.target.value.replace(/\D/g, "");
    if (!value) return;

    const updatedOtp = [...otpArray];
    updatedOtp[index] = value;
    setOtpArray(updatedOtp);

    if (index < 5 && value) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleBackspace = (e, index) => {
    if (e.key === "Backspace" && otpArray[index] === "") {
      const updatedOtp = [...otpArray];
      updatedOtp[index - 1] = "";
      setOtpArray(updatedOtp);
      if (index > 0) inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    const otp = otpArray.join("");
    if (!email) return toast.error("Please enter your email.");
    if (otp.length !== 6)
      return toast.error("Please enter a valid 6-digit OTP.");

    try {
      await verifyOtp({ email, otp });
      toast.success("Email verified successfully!");
      setTimeout(() => navigate("/login"), 2000);
    } catch (err) {
      toast.error(err.message || "OTP verification failed.");
    }
  };

  const handleResendOtp = async () => {
    if (!email) return toast.error("Please enter your email to resend OTP.");

    try {
      await resendOtp({ email });
      toast.success("OTP resent to your email.");
      setResendTimer(60);
      setCanResend(false);
    } catch (err) {
      toast.error(err.message || "Failed to resend OTP.");
    }
  };

  return (
    <HomepageLayout>
      <main className="flex w-screen min-h-screen bg-white font-poppins justify-center items-center">
        <div className="flex flex-col justify-center w-full md:w-[35vw] max-w-md md:px-10 px-3 py-10 bg-white shadow-xl rounded-xl">
          <div className="flex flex-col items-center mb-8">
            <img
              src="/images/icons8-resume-50.png"
              alt="Logo"
              className="w-12 h-12 rounded-lg shadow-md bg-white"
            />
            <span className="text-2xl font-bold text-[#29354d] tracking-wide">
              ResumeBuilder
            </span>
          </div>

          <div className="rounded-xl bg-white p-8 w-full animate-fadeInUp">
            <h1 className="text-2xl font-semibold mb-2">Verify Your Email</h1>

            {isAutoVerifying ? (
              <div className="text-center">
                <p className="text-gray-500 mb-4">
                  Auto-verifying via secure link...
                </p>
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#29354d] mx-auto"></div>
              </div>
            ) : autoVerified ? (
              <div className="text-center text-green-600 font-medium text-lg">
                Email Verified! Redirecting...
              </div>
            ) : (
              <>
                <p className="text-gray-500 mb-4">
                  Enter your email and the 6-digit OTP sent to you.
                </p>

                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full mb-4 px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                />

                <div className="flex justify-between gap-2 mb-6">
                  {otpArray.map((digit, index) => (
                    <input
                      key={index}
                      type="text"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOtpChange(e, index)}
                      onKeyDown={(e) => handleBackspace(e, index)}
                      ref={(el) => (inputRefs.current[index] = el)}
                      className="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                    />
                  ))}
                </div>

                <button
                  onClick={handleVerify}
                  className="w-full py-3 rounded-lg font-semibold bg-[#29354d] text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d] transition"
                >
                  Verify Email <i className="fas fa-check-circle ml-2" />
                </button>

                <div className="text-center text-gray-700 mt-4">
                  Didn’t receive the OTP?{" "}
                  <button
                    onClick={handleResendOtp}
                    className="text-blue-600 font-semibold hover:underline disabled:text-gray-400"
                    disabled={!canResend}
                  >
                    {canResend ? "Resend OTP" : `Resend in ${resendTimer}s`}
                  </button>
                </div>

                <div className="text-center mt-4">
                  <button
                    onClick={() => window.location.reload()}
                    className="text-sm text-gray-500 underline hover:text-gray-800"
                  >
                    Reload Page
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </main>

      <style>{`
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(30px);}
          to { opacity: 1; transform: translateY(0);}
        }
        .animate-fadeInUp {
          animation: fadeInUp 0.7s cubic-bezier(.77,0,.18,1) both;
        }
      `}</style>
    </HomepageLayout>
  );
};

export default OTPVerifypage;
