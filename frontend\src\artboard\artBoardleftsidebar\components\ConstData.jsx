import React, { useEffect } from "react";
import { useAuthStore } from "../../../store/authStore"; // adjust path if needed

const ConstData = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  useEffect(() => {
    fetchCurrentUser(); // Ensures data is loaded
  }, []);
  return (
    <div>
      <div className="bg-white p-6 rounded-md shadow-md max-w-lg mx-auto mt-10">
        <h2 className="text-xl font-bold mb-4">User Info</h2>
        {user ? (
          <div className="space-y-4">
            <div>
              <label className="block text-gray-600 text-sm mb-1">Name</label>
              <input
                type="text"
                value={user.Name || user.name || ""}
                readOnly
                className=" text-black w-full border border-gray-300 px-3 py-2 rounded bg-gray-100 cursor-not-allowed"
              />
            </div>
            <div>
              <label className="block text-gray-600 text-sm mb-1">Email</label>
              <input
                type="email"
                value={user.Email || user.email || ""}
                readOnly
                className="text-black w-full border border-gray-300 px-3 py-2 rounded bg-gray-100 cursor-not-allowed"
              />
            </div>
          </div>
        ) : (
          <p className="text-gray-600">Loading user data...</p>
        )}
      </div>
    </div>
  );
};

export default ConstData;
