import React, { useState } from "react";
import HomepageLayout from "../../components/Layout/HomepageLayout";
import {
  EyeInvisibleOutlined,
  EyeTwoTone,
  InfoCircleOutlined,
  ExclamationCircleTwoTone,
  CheckCircleTwoTone,
} from "@ant-design/icons";
import { Input, Button, Modal } from "antd";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/authStore";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const getPasswordStrength = (password) => {
  let strength = 0;
  if (password.length >= 6) strength += 1;
  if (password.length >= 8) strength += 1;
  if (/[A-Z]/.test(password)) strength += 1;
  if (/[0-9]/.test(password)) strength += 1;
  if (/[^A-Za-z0-9]/.test(password)) strength += 1;
  return strength;
};

const getStrengthFeedback = (strength) => {
  if (strength === 0)
    return {
      icon: <InfoCircleOutlined />,
      text: "Password strength indicator",
      color: "text-gray-400",
    };
  if (strength <= 2)
    return {
      icon: <ExclamationCircleTwoTone twoToneColor="#f72585" />,
      text: "Weak password",
      color: "text-red-500",
    };
  if (strength <= 3)
    return {
      icon: <InfoCircleOutlined />,
      text: "Medium strength",
      color: "text-yellow-500",
    };
  if (strength <= 4)
    return {
      icon: <CheckCircleTwoTone twoToneColor="#4cc9f0" />,
      text: "Good password",
      color: "text-cyan-400",
    };
  return {
    icon: <CheckCircleTwoTone twoToneColor="#4ade80" />,
    text: "Strong password",
    color: "text-green-500",
  };
};

const RegistrationPage = () => {
  const navigate = useNavigate();
  const { register, isLoading } = useAuthStore();
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [socialModal, setSocialModal] = useState({
    open: false,
    provider: "",
    email: "",
  });

  const passwordStrength = getPasswordStrength(password);
  const strengthFeedback = getStrengthFeedback(passwordStrength);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const errors = [];

    if (!username.trim()) errors.push("Username is required");
    else if (username.length < 3 || username.length > 50)
      errors.push("Username must be between 3 and 50 characters");

    if (!email.trim()) errors.push("Email is required");
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
      errors.push("Invalid email format");

    if (!password) errors.push("Password is required");
    else if (password.length < 6)
      errors.push("Password must be at least 6 characters");

    if (password !== confirmPassword) errors.push("Passwords do not match");

    if (errors.length) {
      errors.forEach((err) => toast.error(err));
      return;
    }

    try {
      await register({ username, email, password });
      toast.success("OTP sent to your email.");
      navigate("/verify-otp", { state: { email } });
    } catch (error) {
      const message =
        error?.response?.data?.error ||
        error?.customMessage ||
        error?.message ||
        "Something went wrong. Try again.";

      if (message.toLowerCase().includes("email")) {
        toast.error("This email is already registered. Try logging in.");
      } else {
        toast.error(message);
      }
    }
  };

  return (
    <HomepageLayout>
      <main className="flex w-screen min-h-screen bg-[#f1f3f6] font-poppins">
        <div className="flex flex-col justify-center min-h-screen w-full md:w-[35vw] max-w-[35vw] md:px-10 px-3 py-10 bg-white z-10 shadow-lg">
          <h1 className="text-3xl font-bold text-[#29354d] mb-1">
            Create an Account
          </h1>
          <div className="h-1 w-16 bg-blue-500 rounded mb-4"></div>
          <p className="text-gray-500 mb-4">
            Join our platform to create professional resumes in minutes
          </p>

          <form
            className="space-y-5"
            onSubmit={handleSubmit}
            autoComplete="off"
          >
            {/* Username */}
            <div>
              <label className="block mb-1 font-medium text-gray-700">
                Username
              </label>
              <Input
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                size="large"
                className="rounded-lg"
              />
              <p className="text-xs text-gray-500 mt-1">
                <span className="font-semibold text-red-700">Note:</span> This
                username will be used in your resume and cannot be changed
                later.
              </p>
            </div>

            {/* Email */}
            <div>
              <label className="block mb-1 font-medium text-gray-700">
                Email
              </label>
              <Input
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                size="large"
                className="rounded-lg"
              />
            </div>

            {/* Password */}
            <div>
              <label className="block mb-1 font-medium text-gray-700">
                Password
              </label>
              <Input.Password
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                size="large"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className="rounded-lg"
              />
              <div className="h-2 w-full rounded bg-gray-200 mt-1 overflow-hidden">
                <div
                  className={`transition-all duration-300 h-full ${passwordStrength === 0
                      ? ""
                      : passwordStrength <= 2
                        ? "bg-red-500"
                        : passwordStrength <= 3
                          ? "bg-yellow-400"
                          : passwordStrength <= 4
                            ? "bg-cyan-400"
                            : "bg-green-400"
                    }`}
                  style={{
                    width:
                      passwordStrength === 0
                        ? "0%"
                        : passwordStrength <= 2
                          ? "25%"
                          : passwordStrength <= 3
                            ? "50%"
                            : passwordStrength <= 4
                              ? "75%"
                              : "100%",
                  }}
                />
              </div>
              <div
                className={`text-xs mt-1 flex items-center gap-1 ${strengthFeedback.color}`}
              >
                {strengthFeedback.icon}
                {strengthFeedback.text}
              </div>
            </div>

            {/* Confirm Password */}
            <div>
              <label className="block mb-1 font-medium text-gray-700">
                Confirm Password
              </label>
              <Input.Password
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Enter your password again"
                size="large"
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className={`rounded-lg ${confirmPassword && password !== confirmPassword
                    ? "border-red-500"
                    : ""
                  }`}
              />
            </div>

            {/* <Button
              htmlType="submit"
              type="primary"
              size="large"
              block
              loading={isLoading}
              className="bg-[#29354d] text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d] font-semibold"
            >
              Create Account
            </Button> */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 rounded-lg font-semibold flex items-center justify-center ${isLoading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-[#29354d] text-[#fcc250] hover:bg-[#fcc250] hover:text-[#29354d]"
                } transition`}
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin h-5 w-5 text-white mr-2"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  Creating...
                </>
              ) : (
                <>
                  Create Account <i className="fas fa-arrow-right ml-2" />
                </>
              )}
            </button>

            <div className="text-center text-gray-700 mt-2">
              Already have an account?{" "}
              <a
                href="/login"
                className="text-blue-600 font-semibold hover:underline"
              >
                Login
              </a>
            </div>
          </form>
        </div>
      </main>

      <Modal
        open={socialModal.open}
        title={`Sign up with ${socialModal.provider}`}
        footer={null}
        onCancel={() =>
          setSocialModal({ open: false, provider: "", email: "" })
        }
      >
        <div className="flex flex-col gap-4">
          <Input
            type="email"
            placeholder={`Enter your ${socialModal.provider} email`}
            size="large"
            value={socialModal.email}
            onChange={(e) =>
              setSocialModal({ ...socialModal, email: e.target.value })
            }
            autoFocus
          />
          <Button
            type="primary"
            block
            onClick={() => {
              toast.success(`${socialModal.provider} sign‑up link sent!`);
              setSocialModal({ ...socialModal, open: false });
            }}
          >
            Continue
          </Button>
        </div>
      </Modal>
    </HomepageLayout>
  );
};

export default RegistrationPage;
