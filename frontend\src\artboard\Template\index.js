import GaneshTemplate from "./GaneshTemlate";
import ClassicAdvancedResume from "./ClassicAdvancedResume";
import ClassicTemplate from "./ClassicTemplate";
import ModernTemplate from "./ModernTemplate";
import ImageInspiredTemplate from "./ImageInspiredTemplate";
import ModernTwoColumnTemplate from "./ModernTwoColumnTemplate";
import A4CompactTemplate from "./A4CompactTemplate";
import ElegantSplitLayoutTemplate from "./ElegantSplitLayoutTemplate"; // ⬅️ New import
import ClassicAdvaceTemplate from "./ClassicAdvaceTemplate";
import Thumb2025 from "../../../public/templets/2025.avif";
import Thumb1 from "../../../public/templets/template1.png"
import Thumb2 from "../../../public/templets/Template2.png";
import Thumb3 from "../../../public/templets/Template.png";
import Thumb4 from "../../../public/templets/template4.png";
import Thumb5 from "../../../public/templets/clean.avif";
import Thumb6 from "../../../public/templets/majestic.avif";
import Thumb7 from "../../../public/templets/elegant.avif"; // ⬅️ New thumbnail (replace with actual image path)
import Thumb8 from "../../../public/templets/elegant.avif"; // ⬅️ New thumbnail (replace with actual image path)
import SleekLeftTimelineTemplate from "./SleekLeftTimelineTemplate";


export const TEMPLATES = {

    modern: {
        id: "modern",
        name: "Modern Two Column",
        thumb: Thumb1,
        component: ModernTwoColumnTemplate,
        category: "Modern",
    },
    simple_bw: {
        id: "simple_bw",
        name: "Simple A4 BW",
        thumb: Thumb2,
        component: A4CompactTemplate,
        category: "Minimal",
    },
    classicadvanced: { // ✅ lowercased key
        id: "classicAdvanced",
        name: "Classic Advanced",
        thumb: Thumb3,
        component: ClassicAdvancedResume,
        category: "Classic",
    },
    classicadvacetemplate: {
        id: "classicadvacetemplate",
        name: "Classic Advanced",
        thumb: Thumb4,
        component: ClassicAdvaceTemplate,
        category: "Classic",
      },
    
    // ganesh: {
    //     id: "ganesh",
    //     name: "Ganesh Template",
    //     thumb: Thumb2025,
    //     component: GaneshTemplate,
    //     category: "Creative",
    // },
    // classic: {
    //     id: "classic",
    //     name: "Classic Template",
    //     thumb: Thumb3,
    //     component: ClassicTemplate,
    //     category: "Classic",
    // },
    // imageinspired: { // ✅ lowercased key
    //     id: "imageInspired",
    //     name: "Image Inspired Layout",
    //     thumb: Thumb5,
    //     component: ImageInspiredTemplate,
    //     category: "Visual",
    // },
    // modernsimple: { // ✅ lowercased key
    //     id: "modernSimple",
    //     name: "Modern Template",
    //     thumb: Thumb2,
    //     component: ModernTemplate,
    //     category: "Modern",
    // },
    // elegantsplit: { // ✅ lowercased key
    //     id: "elegantSplit",
    //     name: "Elegant Split Layout",
    //     thumb: Thumb7,
    //     component: ElegantSplitLayoutTemplate,
    //     category: "Elegant",
    // },
    // sleektimeline: {
    //     id: "sleektimeline",
    //     name: "Sleek Timeline Layout",
    //     thumb: Thumb8,
    //     component: SleekLeftTimelineTemplate,
    //     category: "Timeline",
    // },
};

export const TEMPLATE_ARRAY = Object.entries(TEMPLATES).map(
    ([key, value]) => ({ key, ...value })
);
