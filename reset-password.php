<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

// Check if token is provided
if (!isset($_GET['token']) || empty($_GET['token'])) {
    setMessage('Invalid or expired reset token', 'error');
    redirect('forgot-password.php');
}

$token = $_GET['token'];
$errors = [];
$success = false;

// Check if token is valid
$db->query("SELECT * FROM users WHERE reset_token = :token AND reset_expires > NOW()");
$db->bind(':token', $token);
$user = $db->single();

if (!$user) {
    setMessage('Invalid or expired reset token', 'error');
    redirect('forgot-password.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate form data
    if (empty($password)) {
        $errors[] = 'Password is required';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match';
    }
    
    // If no errors, reset password
    if (empty($errors)) {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Update user password and clear reset token
        $db->query("UPDATE users SET password = :password, reset_token = NULL, reset_expires = NULL WHERE id = :id");
        $db->bind(':password', $hashed_password);
        $db->bind(':id', $user['id']);
        
        if ($db->execute()) {
            setMessage('Password has been reset successfully. You can now login with your new password.', 'success');
            redirect('login.php');
        } else {
            $errors[] = 'Something went wrong. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="auth-container">
        <div class="auth-form-container">
            <h1>Reset Password</h1>
            <p class="auth-subtitle">Enter your new password</p>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form action="reset-password.php?token=<?php echo $token; ?>" method="POST" class="auth-form">
                <div class="form-group">
                    <label for="password">New Password</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" required>
                        <span class="toggle-password"><i class="fas fa-eye"></i></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm New Password</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                        <span class="toggle-password"><i class="fas fa-eye"></i></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block">Reset Password</button>
                </div>
                
                <div class="auth-links">
                    <p>Remember your password? <a href="login.php">Login</a></p>
                </div>
            </form>
        </div>
        
        <div class="auth-image">
            <img src="images/reset-password-image.jpg" alt="Reset Password">
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script src="js/auth.js"></script>
</body>
</html>