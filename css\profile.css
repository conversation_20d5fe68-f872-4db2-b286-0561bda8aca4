/* Profile Page Styles */
.profile-container {
    background-color: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--light-gray);
    overflow: hidden;
  }
  
  .profile-form {
    padding: var(--spacing-md);
  }
  
  .form-section {
    margin-bottom: var(--spacing-lg);
  }
  
  .form-section h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--light-gray);
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  @media (min-width: 768px) {
    .form-row {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .form-group {
    margin-bottom: var(--spacing-md);
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
  }
  
  .form-group input,
  .form-group textarea,
  .form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--light-gray);
    border-radius: var(--radius);
    font-family: inherit;
    font-size: 0.95rem;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .form-group input:disabled {
    background-color: var(--light);
    cursor: not-allowed;
  }
  
  .field-hint {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: var(--gray);
  }
  
  .field-hint a {
    color: var(--primary);
    text-decoration: none;
  }
  
  .field-hint a:hover {
    text-decoration: underline;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
  }
  
  @media (max-width: 768px) {
    .form-actions {
      flex-direction: column;
    }
  
    .form-actions .btn {
      width: 100%;
    }
  }
  