/* Home Page Styles - Enhanced Version */
.hero {
    padding: 6rem 0;
    background-color: white;
    position: relative;
    overflow: hidden;
  }
  
  .hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(114, 9, 183, 0.05) 100%);
    z-index: 0;
  }
  
  .hero::after {
    content: "";
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, transparent 70%);
    z-index: 0;
  }
  
  .hero .container {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .hero-content {
    flex: 1;
    max-width: 600px;
    animation: fadeInLeft 0.8s ease forwards;
  }
  
  .hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: var(--dark);
    line-height: 1.2;
    font-weight: 800;
    position: relative;
  }
  
  .hero h1 .highlight {
    color: var(--primary);
    position: relative;
    display: inline-block;
  }
  
  .hero h1 .highlight::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background-color: rgba(67, 97, 238, 0.2);
    z-index: -1;
    border-radius: 4px;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray);
    margin-bottom: 2.5rem;
    line-height: 1.7;
  }
  
  .hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
  }
  
  .hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
  }
  
  .hero-feature {
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
  }
  
  .hero-feature i {
    color: var(--primary);
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
  
  .hero-image {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    animation: fadeInRight 0.8s ease forwards;
  }
  
  .hero-image img {
    max-width: 100%;
    height: auto;
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
    transition: all 0.5s ease;
  }
  
  .hero-image:hover img {
    transform: translateY(-10px);
  }
  
  .hero-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
    animation: pulse 3s infinite;
  }
  
  .hero-scroll {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--gray);
    font-size: 0.875rem;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .hero-scroll:hover {
    color: var(--primary);
    transform: translate(-50%, -5px);
  }
  
  .hero-scroll i {
    font-size: 1.5rem;
    margin-top: 0.5rem;
    animation: fadeInDown 1.5s infinite;
  }
  
  /* Features Section */
  .features {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
  }
  
  .features::before {
    content: '';
    position: absolute;
    top: -10%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
  }
  
  .features::after {
    content: '';
    position: absolute;
    bottom: -10%;
    right: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(114, 9, 183, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
  }
  
  .section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 1;
  }
  
  .section-subtitle {
    display: inline-block;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  .section-title {
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
  }
  
  .section-title::after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--primary);
    border-radius: 2px;
  }
  
  .section-description {
    max-width: 700px;
    margin: 0 auto;
    color: var(--gray);
    font-size: 1.1rem;
    line-height: 1.7;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2.5rem;
    position: relative;
    z-index: 1;
  }
  
  .feature-card {
    background-color: white;
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    height: 100%;
  }
  
  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(114, 9, 183, 0.05) 100%);
    z-index: -1;
    opacity: 0;
    transition: all 0.4s ease;
  }
  
  .feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }
  
  .feature-card:hover::before {
    opacity: 1;
  }
  
  .feature-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.4s ease;
  }
  
  .feature-card:hover .feature-icon {
    background-color: var(--primary);
    transform: scale(1.1);
  }
  
  .feature-icon i {
    font-size: 2rem;
    color: var(--primary);
    transition: all 0.4s ease;
  }
  
  .feature-card:hover .feature-icon i {
    color: white;
  }
  
  .feature-card h3 {
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.35rem;
  }
  
  .feature-card p {
    color: var(--gray);
    text-align: center;
    line-height: 1.7;
  }
  
  /* Templates Section */
  .templates {
    background-color: white;
    position: relative;
    overflow: hidden;
  }
  
  .templates::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.05) 0%, transparent 70%);
    z-index: 0;
  }
  
  .section-header-alt {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
  }
  
  .section-header-alt .section-title {
    margin-bottom: 0;
    text-align: left;
  }
  
  .section-header-alt .section-title::after {
    left: 0;
    transform: none;
  }
  
  .slider-controls {
    display: flex;
    gap: 1rem;
  }
  
  .slider-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid rgba(67, 97, 238, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--primary);
  }
  
  .slider-btn:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
  }
  
  .slider-btn i {
    font-size: 1.25rem;
  }
  
  .template-slider {
    display: flex;
    overflow-x: auto;
    gap: 2rem;
    padding: 1rem 0 2rem;
    scroll-behavior: smooth;
    -ms-overflow-style: none;
    scrollbar-width: none;
    position: relative;
    z-index: 1;
  }
  
  .template-slider::-webkit-scrollbar {
    display: none;
  }
  
  .template-card {
    flex: 0 0 350px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
  }
  
  .template-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }
  
  .template-image {
    position: relative;
    height: 450px;
    overflow: hidden;
  }
  
  .template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
  }
  
  .template-card:hover .template-image img {
    transform: scale(1.05);
  }
  
  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    padding: 2rem;
    opacity: 0;
    transition: all 0.4s ease;
  }
  
  .template-card:hover .template-overlay {
    opacity: 1;
  }
  
  .template-overlay-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .template-info {
    padding: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .template-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
  }
  
  .template-info p {
    color: var(--gray);
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }
  
  .template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .template-rating {
    display: flex;
    align-items: center;
    color: var(--warning);
  }
  
  .template-rating i {
    margin-right: 0.25rem;
  }
  
  .template-downloads {
    color: var(--gray);
    font-size: 0.875rem;
  }
  
  /* How It Works Section */
  .how-it-works {
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
  }
  
  .how-it-works::before {
    content: '';
    position: absolute;
    bottom: -10%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(114, 9, 183, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
  }
  
  .steps {
    display: flex;
    justify-content: space-between;
    margin-top: 4rem;
    position: relative;
    z-index: 1;
  }
  
  .steps::before {
    content: '';
    position: absolute;
    top: 100px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, rgba(67, 97, 238, 0.2), rgba(114, 9, 183, 0.2));
    z-index: -1;
  }
  
  .step {
    flex: 1;
    text-align: center;
    padding: 0 1.5rem;
    position: relative;
    z-index: 1;
  }
  
  .step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 auto 2rem;
    position: relative;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
  }
  
  .step-icon {
    width: 100px;
    height: 100px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
  }
  
  .step:hover .step-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }
  
  .step-icon i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.4s ease;
  }
  
  .step h3 {
    margin-bottom: 1rem;
    font-size: 1.35rem;
  }
  
  .step p {
    color: var(--gray);
    line-height: 1.7;
  }
  
  /* Testimonials Section */
  .testimonials {
    background-color: white;
    position: relative;
    overflow: hidden;
  }
  
  .testimonials::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
  }
  
  .testimonial-slider {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }
  
  .testimonial-card {
    background-color: white;
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    position: relative;
    transition: all 0.4s ease;
  }
  
  .testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }
  
  .testimonial-card::before {
    content: '\201C';
    position: absolute;
    top: 20px;
    left: 30px;
    font-size: 5rem;
    color: rgba(67, 97, 238, 0.1);
    font-family: Georgia, serif;
    line-height: 1;
  }
  
  .testimonial-content {
    margin-bottom: 2rem;
  }
  
  .testimonial-content p {
    font-style: italic;
    color: var(--dark);
    font-size: 1.15rem;
    line-height: 1.8;
  }
  
  .testimonial-author {
    display: flex;
    align-items: center;
  }
  
  .testimonial-author img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 1.5rem;
    border: 3px solid rgba(67, 97, 238, 0.1);
  }
  
  .author-info h4 {
    margin-bottom: 0.25rem;
    font-size: 1.15rem;
  }
  
  .author-info p {
    color: var(--gray);
    font-size: 0.875rem;
  }
  
  .testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
  }
  
  .testimonial-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .testimonial-dot.active {
    background-color: var(--primary);
    transform: scale(1.2);
  }
  
  /* CTA Section */
  .cta {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    padding: 6rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
  }
  
  .cta::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 0;
  }
  
  .cta .container {
    position: relative;
    z-index: 1;
  }
  
  .cta h2 {
    margin-bottom: 1.5rem;
    font-size: 2.5rem;
  }
  
  .cta p {
    max-width: 700px;
    margin: 0 auto 2.5rem;
    opacity: 0.9;
    font-size: 1.15rem;
    line-height: 1.7;
  }
  
  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
  }
  
  .cta-btn {
    padding: 1rem 2.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
  }
  
  .cta-btn-light {
    background-color: white;
    color: var(--primary);
  }
  
  .cta-btn-light:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .cta-btn-outline {
    background-color: transparent;
    border: 2px solid white;
    color: white;
  }
  
  .cta-btn-outline:hover {
    background-color: white;
    color: var(--primary);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  /* Responsive */
  @media (max-width: 1200px) {
    .hero h1 {
      font-size: 3rem;
    }
    
    .steps::before {
      top: 80px;
    }
  }
  
  @media (max-width: 992px) {
    .hero .container {
      flex-direction: column;
      text-align: center;
    }
    
    .hero-content {
      margin-bottom: 4rem;
      max-width: 100%;
    }
    
    .hero h1 {
      font-size: 2.75rem;
    }
    
    .hero-buttons {
      justify-content: center;
    }
    
    .hero-features {
      justify-content: center;
    }
    
    .hero-image {
      justify-content: center;
    }
    
    .steps {
      flex-wrap: wrap;
    }
    
    .steps::before {
      display: none;
    }
    
    .step {
      flex: 0 0 50%;
      margin-bottom: 3rem;
    }
    
    .testimonial-card {
      padding: 2rem;
    }
  }
  
  @media (max-width: 768px) {
    .hero h1 {
      font-size: 2.5rem;
    }
    
    .hero-subtitle {
      font-size: 1.1rem;
    }
    
    .features-grid {
      grid-template-columns: 1fr;
    }
    
    .section-header-alt {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
    }
    
    .section-header-alt .section-title {
      text-align: center;
    }
    
    .section-header-alt .section-title::after {
      left: 50%;
      transform: translateX(-50%);
    }
    
    .slider-controls {
      justify-content: center;
    }
    
    .step {
      flex: 0 0 100%;
    }
    
    .cta h2 {
      font-size: 2rem;
    }
    
    .cta p {
      font-size: 1rem;
    }
  }
  
  @media (max-width: 576px) {
    .hero h1 {
      font-size: 2.25rem;
    }
    
    .hero-buttons {
      flex-direction: column;
      gap: 1rem;
    }
    
    .hero-buttons .btn {
      width: 100%;
    }
    
    .hero-features {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    
    .hero-feature {
      margin-right: 0;
    }
    
    .template-slider {
      gap: 1.5rem;
    }
    
    .template-card {
      flex: 0 0 280px;
    }
    
    .template-image {
      height: 380px;
    }
    
    .testimonial-author {
      flex-direction: column;
      text-align: center;
    }
    
    .testimonial-author img {
      margin-right: 0;
      margin-bottom: 1rem;
    }
    
    .cta-buttons {
      flex-direction: column;
      gap: 1rem;
    }
    
    .cta-btn {
      width: 100%;
    }
  }