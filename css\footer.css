:root {
    --primary: rgb(25,65,75);
    --primary-light: rgba(25,65,75,0.1);
    --primary-dark: rgb(15,45,55);
    --secondary: #f8a978;
    --secondary-light: #ffeee2;
    --accent: #4ecdc4;
    --light: #f7f9fc;
    --dark: #2d3748;
    --gray: #718096;
    --success: #48bb78;
    --white: #ffffff;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --radius: 8px;
    --radius-lg: 12px;
    --transition: all 0.3s ease;
}

/* Footer Styles */
footer {
    background-color: var(--dark);
    color: var(--light);
    padding: 4rem 0 2rem;
}

footer .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--white);
    text-decoration: none;
    margin-bottom: 1rem;
}

footer .logo i {
    font-size: 1.5rem;
}

.footer-tagline {
    color: rgba(255,255,255,0.7);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding-bottom: 2rem;
}

.footer-column h3 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 3px;
    background-color: var(--secondary);
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: 0.75rem;
}

.footer-column a {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.95rem;
}

.footer-column a:hover {
    color: var(--secondary);
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    transition: var(--transition);
    text-decoration: none;
}

.social-link:hover {
    background-color: var(--secondary);
    transform: translateY(-3px);
}

.newsletter {
    max-width: 500px;
    margin: 0 auto 3rem;
    text-align: center;
}

.newsletter h3 {
    color: var(--white);
    margin-bottom: 0.75rem;
}

.newsletter p {
    color: rgba(255,255,255,0.7);
    margin-bottom: 1.5rem;
}

.newsletter-form .form-group {
    display: flex;
    gap: 0.5rem;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--radius);
    background-color: rgba(255,255,255,0.1);
    color: var(--white);
}

.newsletter-form input::placeholder {
    color: rgba(255,255,255,0.5);
}

.newsletter-form button {
    background-color: var(--secondary);
    color: var(--dark);
}

.newsletter-form button:hover {
    background-color: var(--white);
}

.footer-bottom {
    text-align: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-badges {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.badge i {
    color: var(--secondary);
}

@media (max-width: 992px) {
    .footer-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-column:first-child {
        grid-column: span 3;
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-column:first-child {
        grid-column: span 2;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-badges {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .footer-grid {
        grid-template-columns: 1fr;
    }

    .footer-column:first-child {
        grid-column: span 1;
    }

    .newsletter-form .form-group {
        flex-direction: column;
    }
}