/* Sidebar Styles */
.sidebar {
    width: 260px;
    background-color: var(--white);
    border-right: 1px solid var(--light-gray);
    height: calc(100vh - 70px);
    position: sticky;
    top: 70px;
    overflow-y: auto;
    transition: var(--transition);
    z-index: 90;
  }
  
  .sidebar.collapsed {
    width: 70px;
  }
  
  .sidebar.collapsed .sidebar-toggle {
    right: -15px;
  }
  
  .user-info {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
    text-align: center;
    transition: var(--transition);
  }
  
  .sidebar.collapsed .user-info {
    padding: var(--spacing-sm) 0;
  }
  
  .sidebar .user-avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-sm);
    font-size: 1.5rem;
    transition: var(--transition);
  }
  
  .sidebar.collapsed .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .user-info h3 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    transition: var(--transition);
  }
  
  .user-info p {
    color: var(--gray);
    font-size: 0.9rem;
    transition: var(--transition);
  }
  
  .sidebar.collapsed .user-info h3,
  .sidebar.collapsed .user-info p,
  .sidebar.collapsed .profile-completion {
    opacity: 0;
    height: 0;
    margin: 0;
    overflow: hidden;
  }
  
  .profile-completion {
    margin-top: var(--spacing-sm);
    transition: var(--transition);
  }
  
  .completion-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }
  
  .percentage {
    font-weight: 600;
  }
  
  .sidebar-nav {
    padding: var(--spacing-md) 0;
  }
  
  .sidebar-nav ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem var(--spacing-md);
    color: var(--gray);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
    overflow: hidden;
    white-space: nowrap;
  }
  
  .sidebar-nav a:hover {
    color: var(--primary);
    background-color: var(--primary-light);
  }
  
  .sidebar-nav li.active a {
    color: var(--primary);
    border-left-color: var(--primary);
    background-color: var(--primary-light);
    font-weight: 500;
  }
  
  .sidebar-nav i {
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    transition: var(--transition);
  }
  
  .sidebar.collapsed .sidebar-nav a span {
    opacity: 0;
    width: 0;
    height: 0;
    overflow: hidden;
  }
  
  .sidebar-toggle {
    position: absolute;
    bottom: 20px;
    right: 2px;
    width: 30px;
    height: 30px;
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow);
    z-index: 10;
    transition: var(--transition);
  }
  
  .sidebar-toggle:hover {
    background-color: var(--primary-dark);
    transform: scale(1.1);
  }
  
  /* Overlay for mobile sidebar */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 89;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }
  
  .overlay.active {
    opacity: 1;
    visibility: visible;
  }
  
  /* Responsive Sidebar */
  @media (max-width: 992px) {
    .sidebar {
      position: fixed;
      left: -260px;
      height: 100vh;
      top: 0;
      z-index: 1000;
    }
  
    .sidebar.active {
      left: 0;
    }
  
    .sidebar-toggle {
      display: none;
    }
  
    .dashboard-content {
      margin-left: 0;
      width: 100%;
    }
  }
  