<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is logged in for certain features
$is_logged_in = isLoggedIn();
$user_id = $is_logged_in ? $_SESSION['user_id'] : null;

// Get all templates
$db->query("SELECT * FROM templates ORDER BY category, name");
$templates = $db->resultSet();

// Group templates by category
$grouped_templates = [];
foreach ($templates as $template) {
    $grouped_templates[$template['category']][] = $template;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Templates - Medini Resume Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--light);
            min-height: 100vh;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        /* Hero Section */
        .templates-hero {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: var(--spacing-xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .templates-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .templates-hero h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            animation: fadeInUp 0.8s ease-out;
        }
        
        .templates-hero p {
            font-size: 1.1rem;
            margin-bottom: var(--spacing-lg);
            opacity: 0.9;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }
        
        .hero-stats {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Filter Section */
        .filter-section {
            background-color: var(--white);
            padding: var(--spacing-lg) 0;
            border-bottom: 1px solid var(--light-gray);
            position: sticky;
            top: 70px;
            z-index: 90;
        }
        
        .filter-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .filter-tabs {
            display: flex;
            gap: 0.5rem;
        }
        
        .filter-tab {
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            background-color: transparent;
            border: 1px solid var(--light-gray);
            color: var(--gray);
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.9rem;
        }
        
        .filter-tab.active,
        .filter-tab:hover {
            background-color: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }
        
        .search-box {
            position: relative;
            max-width: 300px;
        }
        
        .search-box input {
            width: 100%;
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid var(--light-gray);
            border-radius: var(--radius);
            font-size: 0.9rem;
        }
        
        .search-box i {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }
        
        /* Templates Grid */
        .templates-section {
            padding: var(--spacing-xl) 0;
        }
        
        .category-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .category-header {
            margin-bottom: var(--spacing-lg);
        }
        
        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .category-description {
            color: var(--gray);
            font-size: 0.95rem;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .template-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
            transition: var(--transition);
            position: relative;
            cursor: pointer;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .template-preview {
            position: relative;
            height: 350px;
            overflow: hidden;
            background-color: var(--light);
        }
        
        .template-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }
        
        .template-card:hover .template-preview img {
            transform: scale(1.05);
        }
        
        .template-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(45, 95, 139, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            opacity: 0;
            transition: var(--transition);
        }
        
        .template-card:hover .template-overlay {
            opacity: 1;
        }
        
        .template-badge {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background-color: var(--accent);
            color: var(--white);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .template-badge.premium {
            background-color: var(--warning);
        }
        
        .template-info {
            padding: var(--spacing-md);
        }
        
        .template-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }
        
        .template-description {
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: var(--spacing-sm);
            line-height: 1.5;
        }
        
        .template-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: var(--spacing-sm);
        }
        
        .feature-tag {
            background-color: var(--secondary-light);
            color: var(--primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius);
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .template-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.85rem;
            text-align: center;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary);
            color: var(--white);
            transform: translateY(-2px);
        }
        
        .btn-white {
            background-color: var(--white);
            color: var(--primary);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-white:hover {
            background-color: var(--light);
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        .btn-block {
            width: 100%;
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--gray);
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
            opacity: 0.5;
        }
        
        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }
        
        /* Loading State */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-xl);
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-gray);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .template-card {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .filter-container {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-sm);
            }
            
            .filter-tabs {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .templates-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .templates-hero h1 {
                font-size: 2rem;
            }
            
            .hero-stats {
                flex-direction: column;
                gap: var(--spacing-md);
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .template-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php include "includes/header.php" ?>
    
    <!-- Hero Section -->
    <section class="templates-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Professional Resume Templates</h1>
                <p>Choose from our collection of ATS-friendly, professionally designed resume templates. Each template is crafted to help you stand out and land your dream job.</p>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Templates</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">ATS-Friendly</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">Free</div>
                        <div class="stat-label">Download</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container">
            <div class="filter-container">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-category="all">All Templates</button>
                    <button class="filter-tab" data-category="modern">Modern</button>
                    <button class="filter-tab" data-category="classic">Classic</button>
                    <button class="filter-tab" data-category="creative">Creative</button>
                    <button class="filter-tab" data-category="executive">Executive</button>
                    <button class="filter-tab" data-category="entry-level">Entry Level</button>
                </div>
                
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="template-search" placeholder="Search templates...">
                </div>
            </div>
        </div>
    </section>
    
    <!-- Templates Section -->
    <section class="templates-section">
        <div class="container">
            <div id="templates-container">
                <?php if (empty($templates)): ?>
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <h3>No Templates Available</h3>
                        <p>We're working on adding more templates. Please check back soon!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($grouped_templates as $category => $category_templates): ?>
                        <div class="category-section" data-category="<?php echo strtolower($category); ?>">
                            <div class="category-header">
                                <h2 class="category-title"><?php echo ucfirst($category); ?> Templates</h2>
                                <p class="category-description">
                                    <?php
                                    $descriptions = [
                                        'modern' => 'Clean, contemporary designs perfect for tech and creative industries.',
                                        'classic' => 'Traditional, professional layouts suitable for corporate environments.',
                                        'creative' => 'Unique, eye-catching designs for creative professionals.',
                                        'executive' => 'Sophisticated templates for senior-level positions.',
                                        'entry-level' => 'Simple, effective designs perfect for new graduates and career starters.'
                                    ];
                                    echo $descriptions[strtolower($category)] ?? 'Professional resume templates for various industries.';
                                    ?>
                                </p>
                            </div>
                            
                            <div class="templates-grid">
                                <?php foreach ($category_templates as $index => $template): ?>
                                    <div class="template-card" style="animation-delay: <?php echo $index * 0.1; ?>s;" data-template-id="<?php echo $template['id']; ?>">
                                        <div class="template-preview">
                                            <img src="https://writelatex.s3.amazonaws.com/published_ver/38188.jpeg?X-Amz-Expires=14400&X-Amz-Date=20250618T130107Z&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAWJBOALPNFPV7PVH5/20250618/us-east-1/s3/aws4_request&X-Amz-SignedHeaders=host&X-Amz-Signature=91455e9bd7e4013253abbf434ceb06c0f6daf3cfffcbbf185b157076714b6199">
                                            
                                            <div class="template-overlay">
                                                <?php if ($is_logged_in): ?>
                                                    <a href="edit-template.php?id=<?php echo $template['id']; ?>" class="btn btn-white btn-sm">
                                                        <i class="fas fa-edit"></i> Use Template
                                                    </a>
                                                    <a href="preview-template.php?id=<?php echo $template['id']; ?>" class="btn btn-outline btn-sm" style="color: white; border-color: white;">
                                                        <i class="fas fa-eye"></i> Preview
                                                    </a>
                                                <?php else: ?>
                                                    <a href="register.php" class="btn btn-white btn-sm">
                                                        <i class="fas fa-user-plus"></i> Sign Up to Use
                                                    </a>
                                                    <a href="preview-template.php?id=<?php echo $template['id']; ?>" class="btn btn-outline btn-sm" style="color: white; border-color: white;">
                                                        <i class="fas fa-eye"></i> Preview
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <?php if (isset($template['is_premium']) && $template['is_premium']): ?>
                                                <div class="template-badge premium">Premium</div>
                                            <?php else: ?>
                                                <div class="template-badge">Free</div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="template-info">
                                            <h3 class="template-name"><?php echo htmlspecialchars($template['name']); ?></h3>
                                            <p class="template-description"><?php echo htmlspecialchars($template['description']); ?></p>
                                            
                                            <div class="template-features">
                                                <span class="feature-tag">ATS-Friendly</span>
                                                <span class="feature-tag">PDF Export</span>
                                                <?php if (isset($template['features'])): ?>
                                                    <?php foreach (explode(',', $template['features']) as $feature): ?>
                                                        <span class="feature-tag"><?php echo trim($feature); ?></span>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="template-actions">
                                                <?php if ($is_logged_in): ?>
                                                    <a href="edit-template.php?id=<?php echo $template['id']; ?>" class="btn btn-primary btn-sm btn-block">
                                                        <i class="fas fa-edit"></i> Use Template
                                                    </a>
                                                <?php else: ?>
                                                    <a href="register.php" class="btn btn-primary btn-sm btn-block">
                                                        <i class="fas fa-user-plus"></i> Sign Up to Use
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>
    
    <?php include "includes/footer.php" ?>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter functionality
            const filterTabs = document.querySelectorAll('.filter-tab');
            const categorySection = document.querySelectorAll('.category-section');
            const searchInput = document.getElementById('template-search');
            
            // Category filtering
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Update active tab
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Filter categories
                    categorySection.forEach(section => {
                        if (category === 'all' || section.getAttribute('data-category') === category) {
                            section.style.display = 'block';
                            // Re-animate cards
                            const cards = section.querySelectorAll('.template-card');
                            cards.forEach((card, index) => {
                                card.style.animation = `fadeInUp 0.6s ease-out ${index * 0.1}s forwards`;
                            });
                        } else {
                            section.style.display = 'none';
                        }
                    });
                });
            });
            
            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const templateCards = document.querySelectorAll('.template-card');
                
                templateCards.forEach(card => {
                    const templateName = card.querySelector('.template-name').textContent.toLowerCase();
                    const templateDescription = card.querySelector('.template-description').textContent.toLowerCase();
                    
                    if (templateName.includes(searchTerm) || templateDescription.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
                
                // Hide empty categories
                categorySection.forEach(section => {
                    const visibleCards = section.querySelectorAll('.template-card[style*="display: block"], .template-card:not([style*="display: none"])');
                    if (visibleCards.length === 0) {
                        section.style.display = 'none';
                    } else {
                        section.style.display = 'block';
                    }
                });
            });
            
            // Template card click handling
            const templateCards = document.querySelectorAll('.template-card');
            templateCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    // Don't trigger if clicking on buttons
                    if (e.target.closest('.btn')) return;
                    
                    const templateId = this.getAttribute('data-template-id');
                    <?php if ($is_logged_in): ?>
                        window.location.href = `edit-template.php?id=${templateId}`;
                    <?php else: ?>
                        window.location.href = `preview-template.php?id=${templateId}`;
                    <?php endif; ?>
                });
            });
            
            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            templateCards.forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>