/* Auth Pages Styles */
.auth-container {
    display: flex;
    min-height: calc(100vh - 70px - 400px);
    background-color: white;
}

.auth-form-container {
    flex: 1;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-image {
    flex: 1;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-header {
    margin-bottom: 2rem;
    text-align: center;
}

.auth-header h1 {
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--gray);
}

.auth-form {
    max-width: 450px;
    margin: 0 auto;
}

.auth-footer {
    margin-top: 2rem;
    text-align: center;
}

.auth-links {
    margin-top: 1rem;
}

.auth-links a {
    margin: 0 0.5rem;
}

.social-login {
    margin-top: 2rem;
    text-align: center;
}

.social-login-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    transition: var(--transition);
}

.social-btn:hover {
    background-color: #f8f9fa;
}

.social-btn i {
    margin-right: 0.5rem;
}

.social-btn.google i {
    color: #DB4437;
}

.social-btn.facebook i {
    color: #4267B2;
}

.social-btn.linkedin i {
    color: #0077B5;
}

.or-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: var(--gray);
}

.or-divider::before,
.or-divider::after {
    content: "";
    flex: 1;
    height: 1px;
    background-color: #ddd;
}

.or-divider span {
    padding: 0 1rem;
}

@media (max-width: 992px) {
    .auth-container {
        flex-direction: column;
    }
    
    .auth-image {
        display: none;
    }
    
    .auth-form-container {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .auth-form-container {
        padding: 1.5rem;
    }
    
    .social-login-buttons {
        flex-direction: column;
    }
    
    .social-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}