<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Calculate profile completion percentage if not already set
if (!isset($completion_percentage)) {
    $completion_percentage = isset($profile) ? getProfileCompletionPercentage($user_id) : 0;
}
?>

<div class="sidebar" id="sidebar">
    <div class="user-info">
        <div class="user-avatar">
            <?php if (!empty($profile['profile_image'])): ?>
                <img src="<?php echo UPLOAD_DIR . 'profiles/' . $profile['profile_image']; ?>" alt="Profile Image">
            <?php else: ?>
                <i class="fas fa-user"></i>
            <?php endif; ?>
        </div>
        <h3><?php echo isset($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : 'Complete Profile'; ?></h3>
        <p><?php echo isset($profile['title']) ? $profile['title'] : 'Add your professional title'; ?></p>
        
        <div class="profile-completion">
            <div class="completion-label">
                <span>Profile Completion</span>
                <span class="percentage"><?php echo $completion_percentage; ?>%</span>
            </div>
            <div class="progress-bar">
                <div class="progress" style="width: <?php echo $completion_percentage; ?>%"></div>
            </div>
        </div>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li class="<?php echo $current_page == 'dashboard.php' ? 'active' : ''; ?>">
                <a href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'profile.php' ? 'active' : ''; ?>">
                <a href="profile.php">
                    <i class="fas fa-user"></i>
                    <span>My Profile</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'skills.php' ? 'active' : ''; ?>">
                <a href="skills.php">
                    <i class="fas fa-star"></i>
                    <span>Skills</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'experience.php' ? 'active' : ''; ?>">
                <a href="experience.php">
                    <i class="fas fa-briefcase"></i>
                    <span>Experience</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'education.php' ? 'active' : ''; ?>">
                <a href="education.php">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Education</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'projects.php' ? 'active' : ''; ?>">
                <a href="projects.php">
                    <i class="fas fa-project-diagram"></i>
                    <span>Projects</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'certifications.php' ? 'active' : ''; ?>">
                <a href="certifications.php">
                    <i class="fas fa-certificate"></i>
                    <span>Certifications</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'my-resumes.php' ? 'active' : ''; ?>">
                <a href="my-resumes.php">
                    <i class="fas fa-file"></i>
                    <span>My Resumes</span>
                </a>
            </li>
            <li class="<?php echo $current_page == 'settings.php' ? 'active' : ''; ?>">
                <a href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </li>
            <li>
                <a href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <button class="sidebar-toggle" id="sidebar-toggle">
        <i class="fas fa-chevron-left"></i>
    </button>
</div>
