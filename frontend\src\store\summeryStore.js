import { create } from "zustand";
import axios from "axios";
import { ADDINFO_ENDPOINTS, GETINFO_ENDPOINT } from "../lib/constants";
export const useSummaryStore = create((set) => ({
    summaries: [],
    user: null,
    isLoading: false,
    error: null,

    fetchSummaries: async () => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.get(GETINFO_ENDPOINT, {
                withCredentials: true,
            });

            const user = res.data.user;
            const summaries = user?.Summary || []; // matches backend field

            set({ user, summaries, isLoading: false });
        } catch (err) {
            console.error("Error fetching summaries:", err.response?.data || err.message);
            set({
                error: err.response?.data?.error || "Failed to load summaries",
                isLoading: false,
            });
        }
    },

    addSummary: async (text) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.post(
                ADDINFO_ENDPOINTS.SUMMERY, // This should point to "/addinfo/summery"
                { text }, // key must be `text` to match backend
                { withCredentials: true }
            );

            const updatedUser = res.data.user;
            set({
                summaries: updatedUser.Summary || [],
                user: updatedUser,
                isLoading: false,
            });
        } catch (err) {
            console.error("Error adding summary:", err.response?.data || err.message);
            set({
                error: err.response?.data?.error || "Failed to add summary",
                isLoading: false,
            });
        }
    },
    deleteSummary: async (summaryId) => {
        set({ isLoading: true, error: null });
        try {
            const res = await axios.delete(
                `${ADDINFO_ENDPOINTS.SUMMERY}/${summaryId}`,
                { withCredentials: true }
            );
            const updatedUser = res.data.user;
            set({
                summaries: updatedUser.Summary || [],
                user: updatedUser,
                isLoading: false,
            });
        } catch (err) {
            console.error("Error deleting summary:", err.response?.data || err.message);
            set({
                error: err.response?.data?.error || "Failed to delete summary",
                isLoading: false,
            });
        }
    },


    setSummaries: (summaries) => set({ summaries }),
    clearSummaries: () => set({ summaries: [], user: null, error: null }),
}));
