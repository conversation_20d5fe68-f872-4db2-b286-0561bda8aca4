<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);
$completion_percentage = getProfileCompletionPercentage($user_id);
$saved_resumes = getUserSavedResumes($user_id);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Medini Resume Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <style>
        :root {
            --primary: #2d5f8b;
            --primary-light: rgba(45, 95, 139, 0.1);
            --primary-dark: #1e4265;
            --secondary: #4a90e2;
            --secondary-light: #e9f3ff;
            --accent: #37b2a8;
            --light: #f8fafc;
            --dark: #333333;
            --gray: #6b7280;
            --light-gray: #e5e7eb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
            --radius: 6px;
            --radius-lg: 8px;
            --transition: all 0.3s ease;
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: var(--light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        /* Dashboard Layout */
        .dashboard-container {
            display: flex;
            flex: 1;
            position: relative;
        }
        
        .sidebar {
            width: 260px;
            background-color: var(--white);
            border-right: 1px solid var(--light-gray);
            height: calc(100vh - 70px);
            position: sticky;
            top: 70px;
            overflow-y: auto;
            transition: var(--transition);
            z-index: 90;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        .sidebar.collapsed .sidebar-toggle {
            right: -15px;
        }

        
        .user-info {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info {
            padding: var(--spacing-sm) 0;
        }
        
        .sidebar .user-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-sm);
            font-size: 1.5rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-avatar {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .user-info h3 {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .user-info p {
            color: var(--gray);
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .user-info h3,
        .sidebar.collapsed .user-info p {
            opacity: 0;
            height: 0;
            margin: 0;
            overflow: hidden;
        }
        
        .sidebar-nav {
            padding: var(--spacing-md) 0;
        }
        
        .sidebar-nav ul {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem var(--spacing-md);
            color: var(--gray);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .sidebar-nav a:hover {
            color: var(--primary);
            background-color: var(--primary-light);
        }
        
        .sidebar-nav li.active a {
            color: var(--primary);
            border-left-color: var(--primary);
            background-color: var(--primary-light);
            font-weight: 500;
        }
        
        .sidebar-nav i {
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
            transition: var(--transition);
        }
        
        .sidebar.collapsed .sidebar-nav a span {
            opacity: 0;
            width: 0;
            height: 0;
            overflow: hidden;
        }
        
        .sidebar-toggle {
    position: absolute;
    bottom: 20px;
    right: 2px;
    width: 30px;
    height: 30px;
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow);
    z-index: 10;
    transition: var(--transition);
}
        
        
.sidebar-toggle:hover {
    background-color: var(--primary-dark);
    transform: scale(1.1);
}

        
        .dashboard-content {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .dashboard-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--dark);
            animation: fadeInUp 0.6s ease-out;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            animation: fadeInUp 0.6s ease-out 0.2s both;
        }
        
        /* Dashboard Stats */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-card {
            background-color: var(--white);
            border-radius: var(--radius);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            transition: var(--transition);
            border: 1px solid var(--light-gray);
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.25rem;
        }
        
        .stat-info {
            flex: 1;
        }
        
        .stat-info h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            color: var(--primary);
        }
        
        .stat-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Dashboard Sections */
        .dashboard-sections {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }
        
        .section {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out 0.4s both;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .section-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark);
        }
        
        .section-content {
            padding: var(--spacing-md);
        }
        
        /* Profile Completion */
        .progress-bar {
            height: 10px;
            background-color: var(--light-gray);
            border-radius: 5px;
            margin-bottom: 0.5rem;
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background-color: var(--primary);
            border-radius: 5px;
            width: 0;
            transition: width 1.5s ease-in-out;
        }
        
        .completion-tips {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--secondary-light);
            border-radius: var(--radius);
            border-left: 3px solid var(--secondary);
        }
        
        .completion-tips h4 {
            margin-bottom: 0.5rem;
            color: var(--primary);
            font-size: 1rem;
        }
        
        .completion-tips ul {
            list-style: none;
            margin-left: 0.5rem;
        }
        
        .completion-tips li {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .completion-tips li::before {
            content: '\f054';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
            font-size: 0.8rem;
        }
        
        .completion-tips a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition);
        }
        
        .completion-tips a:hover {
            text-decoration: underline;
        }
        
        /* Recent Resumes */
        .resume-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-md);
        }
        
        .resume-card {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--light-gray);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .resume-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }
        
        .resume-preview {
            height: 160px;
            background-color: var(--light);
            position: relative;
            overflow: hidden;
        }
        
        .resume-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }
        
        .resume-card:hover .resume-preview img {
            transform: scale(1.05);
        }
        
        .resume-info {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--light-gray);
        }
        
        .resume-info h3 {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            color: var(--dark);
        }
        
        .resume-info p {
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .resume-actions {
            display: flex;
            gap: 0.5rem;
            padding: var(--spacing-sm);
            justify-content: flex-end;
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: var(--spacing-xl) var(--spacing-md);
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            background-color: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            color: var(--primary);
            font-size: 2rem;
        }
        
        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }
        
        .empty-state p {
            color: var(--gray);
            margin-bottom: var(--spacing-md);
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.6rem 1.25rem;
            border-radius: var(--radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            font-size: 0.95rem;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
        
        .btn i {
            margin-right: 0.5rem;
        }
        
        /* Alerts */
        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius);
            margin-bottom: var(--spacing-md);
            animation: fadeInUp 0.6s ease-out;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success);
            color: var(--success);
        }
        
        .alert-error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger);
            color: var(--danger);
        }
        
        /* Mobile Sidebar Toggle */
        .mobile-sidebar-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--dark);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        /* Footer */
        footer {
            background-color: var(--white);
            border-top: 1px solid var(--light-gray);
            padding: var(--spacing-md) 0;
            text-align: center;
        }
        
        footer p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sections {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: fixed;
                left: -260px;
                height: 100vh;
                top: 0;
                z-index: 1000;
            }
            
            .sidebar.active {
                left: 0;
            }
            
            .sidebar-toggle {
                display: none;
            }
            
            .mobile-sidebar-toggle {
                display: block;
            }
            
            .dashboard-content {
                margin-left: 0;
                width: 100%;
            }
            
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--transition);
            }
            
            .overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }
            
            .resume-cards {
                grid-template-columns: 1fr;
            }
            
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
            
            .action-buttons {
                width: 100%;
            }
            
            .btn {
                width: 100%;
            }
            
            nav ul {
                display: none;
            }
        }
    </style>
</head>
<body>
    <?php include "includes/header.php" ?>
    
    <div class="overlay"></div>
    
    <main class="dashboard-container">
        <div class="sidebar">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h3><?php echo !empty($profile['first_name']) ? $profile['first_name'] . ' ' . $profile['last_name'] : $_SESSION['username']; ?></h3>
                <p><?php echo $profile['title'] ?></p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="active"><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> <span>Dashboard</span></a></li>
                    <li><a href="profile.php"><i class="fas fa-user"></i> <span>My Profile</span></a></li>
                    <li><a href="skills.php"><i class="fas fa-star"></i> <span>Skills</span></a></li>
                    <li><a href="experience.php"><i class="fas fa-briefcase"></i> <span>Experience</span></a></li>
                    <li><a href="education.php"><i class="fas fa-graduation-cap"></i> <span>Education</span></a></li>
                    <li><a href="projects.php"><i class="fas fa-project-diagram"></i> <span>Projects</span></a></li>
                    <li><a href="certifications.php"><i class="fas fa-certificate"></i> <span>Certifications</span></a></li>
                    <li><a href="my-resumes.php"><i class="fas fa-file"></i> <span>My Resumes</span></a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> <span>Settings</span></a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> <span>Logout</span></a></li>
                </ul>
            </nav>
            
            <!-- <button class="sidebar-toggle">
                <i class="fas fa-chevron-left"></i>
            </button> -->
        </div>
        
        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>Dashboard</h1>
                <div class="action-buttons">
                    <a href="templates.php" class="btn btn-primary"><i class="fas fa-plus"></i> Create New Resume</a>
                </div>
            </div>
            
            <div class="alert alert-success">
                <p>Welcome back! You're making great progress on your profile.</p>
            </div>
            
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="count-up" data-target="3">0</h3>
                        <p>Saved Resumes</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="count-up" data-target="12">0</h3>
                        <p>Downloads</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="count-up" data-target="75">0</h3>
                        <p>Profile Completion</p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-sections">
                <div class="section profile-completion">
                    <div class="section-header">
                        <h2>Profile Completion</h2>
                        <a href="profile.html" class="btn btn-sm btn-outline">Complete Profile</a>
                    </div>
                    <div class="section-content">
                        <div class="progress-bar">
                            <div class="progress" style="width: 75%"></div>
                        </div>
                        <p>75% Complete</p>
                        
                        <div class="completion-tips">
                            <h4>Complete your profile to create better resumes</h4>
                            <ul>
                                <li><a href="skills.html">Add at least 5 skills</a></li>
                                <li><a href="projects.html">Add your projects</a></li>
                                <li><a href="certifications.html">Add your certifications</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Resumes Section -->
                <div class="section recent-resumes">
                    <div class="section-header">
                        <h2>Recent Resumes</h2>
                        <a href="my-resumes.html" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="section-content">
                        <div class="resume-cards">
                            <?php
                            // Get user's saved resumes
                            $saved_resumes = getUserSavedResumes($user_id);

                            // Check if user has any resumes
                            if (!empty($saved_resumes)) {
                                // Loop through resumes and display them
                                foreach ($saved_resumes as $resume) {
                                    ?>
                                    <div class="resume-card">
                                        <div class="resume-preview">
                                            <img src="<?php echo $resume['preview_image'] ? $resume['preview_image'] : 'https://placehold.co/400x200/f0f4f8/2d5f8b?text=Resume'; ?>" alt="Resume Preview">
                                        </div>
                                        <div class="resume-info">
                                            <h3><?php echo htmlspecialchars($resume['title']); ?></h3>
                                            <p>Template: <?php echo htmlspecialchars($resume['template_name']); ?></p>
                                            <p>Last Updated: <?php echo date('F j, Y', strtotime($resume['updated_at'])); ?></p>
                                        </div>
                                        <div class="resume-actions">
                                            <a href="edit-resume.html?id=<?php echo $resume['id']; ?>" class="btn btn-sm btn-outline"><i class="fas fa-edit"></i> Edit</a>
                                            <a href="view-resume.html?id=<?php echo $resume['id']; ?>" class="btn btn-sm btn-outline"><i class="fas fa-eye"></i> View</a>
                                            <a href="download-resume.html?id=<?php echo $resume['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-download"></i> Download</a>
                                        </div>
                                    </div>
                                    <?php
                                }
                            } else {
                                // Display message if no resumes found
                                ?>
                                <div class="no-resumes-message">
                                    <p>You haven't created any resumes yet.</p>
                                    <a href="create-resume.html" class="btn btn-primary">Create Your First Resume</a>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <span id="current-year">2023</span> Medini Resume Builder. All rights reserved.</p>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update current year
            document.getElementById('current-year').textContent = new Date().getFullYear();
            
            // User dropdown toggle
            const userDropdown = document.querySelector('.user-dropdown');
            const userDropdownToggle = document.querySelector('.user-dropdown-toggle');
            
            if (userDropdownToggle) {
                userDropdownToggle.addEventListener('click', function() {
                    userDropdown.classList.toggle('active');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!userDropdown.contains(event.target)) {
                        userDropdown.classList.remove('active');
                    }
                });
            }
            
            // Sidebar toggle
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    
                    // Update toggle icon
                    const icon = this.querySelector('i');
                    if (sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-left');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-left');
                    }
                });
            }
            
            // Mobile sidebar toggle
            const mobileSidebarToggle = document.querySelector('.mobile-sidebar-toggle');
            const overlay = document.querySelector('.overlay');
            
            if (mobileSidebarToggle && sidebar && overlay) {
                mobileSidebarToggle.addEventListener('click', function() {
                    sidebar.classList.add('active');
                    overlay.classList.add('active');
                });
                
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }
            
            // Animate progress bar
            const progressBar = document.querySelector('.progress');
            if (progressBar) {
                setTimeout(() => {
                    progressBar.style.width = progressBar.parentElement.getAttribute('data-progress') || '75%';
                }, 300);
            }
            
            // Number counter animation
            const counters = document.querySelectorAll('.count-up');
            const speed = 200; // The lower the faster
            
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / speed;
                let count = 0;
                
                const updateCount = () => {
                    if (count < target) {
                        count += increment;
                        if (count > target) count = target;
                        counter.textContent = Math.floor(count);
                        requestAnimationFrame(updateCount);
                    }
                };
                
                updateCount();
            });
        });
    </script>
</body>
</html>