<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resume Editor</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/editor.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div class="editor-container">
    <header class="editor-header">
      <div class="header-left">
        <a href="dashboard.html" class="back-btn">
          <i class="fas fa-arrow-left"></i>
        </a>
        <div class="document-info">
          <div class="document-name" contenteditable="true">Professional Resume</div>
          <div class="document-status">
            <span class="status-indicator saved">
              <i class="fas fa-check-circle"></i> Saved
            </span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <div class="template-selector">
          <button class="btn btn-outline btn-sm">
            <i class="fas fa-th-large"></i>
            <span>Change Template</span>
          </button>
        </div>
        <div class="action-buttons">
          <button class="btn btn-outline btn-sm" id="previewBtn">
            <i class="fas fa-eye"></i>
            <span>Preview</span>
          </button>
          <div class="dropdown">
            <button class="btn btn-outline btn-sm">
              <i class="fas fa-download"></i>
              <span>Download</span>
              <i class="fas fa-chevron-down"></i>
            </button>
            <div class="dropdown-menu">
              <a href="#" data-format="pdf"><i class="far fa-file-pdf"></i> PDF</a>
              <a href="#" data-format="docx"><i class="far fa-file-word"></i> Word (DOCX)</a>
              <a href="#" data-format="txt"><i class="far fa-file-alt"></i> Plain Text</a>
            </div>
          </div>
          <button class="btn btn-primary btn-sm" id="saveBtn">
            <i class="fas fa-save"></i>
            <span>Save</span>
          </button>
        </div>
      </div>
    </header>

    <div class="editor-content">
      <aside class="editor-sidebar">
        <div class="sidebar-header">
          <h3>Resume Sections</h3>
          <button class="btn-icon" id="addSectionBtn" title="Add Section">
            <i class="fas fa-plus"></i>
          </button>
        </div>
        
        <div class="sidebar-content">
          <div class="section-list" id="sectionList">
            <div class="section-item active" data-section="personal">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Personal Information</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="section-item" data-section="summary">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Professional Summary</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="section-item" data-section="experience">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Work Experience</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="section-item" data-section="education">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Education</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="section-item" data-section="skills">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Skills</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
            
            <div class="section-item" data-section="certifications">
              <div class="section-drag-handle">
                <i class="fas fa-grip-lines"></i>
              </div>
              <div class="section-info">
                <span>Certifications</span>
              </div>
              <div class="section-actions">
                <button class="btn-icon" title="Edit Section">
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="sidebar-footer">
          <button class="btn btn-outline btn-block">
            <i class="fas fa-cog"></i>
            <span>Template Settings</span>
          </button>
        </div>
      </aside>
      
      <main class="editor-main">
        <div class="editor-toolbar">
          <div class="toolbar-group">
            <button class="toolbar-btn" title="Bold">
              <i class="fas fa-bold"></i>
            </button>
            <button class="toolbar-btn" title="Italic">
              <i class="fas fa-italic"></i>
            </button>
            <button class="toolbar-btn" title="Underline">
              <i class="fas fa-underline"></i>
            </button>
          </div>
          
          <div class="toolbar-group">
            <button class="toolbar-btn" title="Align Left">
              <i class="fas fa-align-left"></i>
            </button>
            <button class="toolbar-btn" title="Align Center">
              <i class="fas fa-align-center"></i>
            </button>
            <button class="toolbar-btn" title="Align Right">
              <i class="fas fa-align-right"></i>
            </button>
          </div>
          
          <div class="toolbar-group">
            <button class="toolbar-btn" title="Bullet List">
              <i class="fas fa-list-ul"></i>
            </button>
            <button class="toolbar-btn" title="Numbered List">
              <i class="fas fa-list-ol"></i>
            </button>
          </div>
          
          <div class="toolbar-group">
            <select class="font-selector">
              <option value="default">Default Font</option>
              <option value="arial">Arial</option>
              <option value="georgia">Georgia</option>
              <option value="times">Times New Roman</option>
              <option value="verdana">Verdana</option>
            </select>
            
            <select class="font-size-selector">
              <option value="default">Size</option>
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
          
          <div class="toolbar-group">
            <button class="toolbar-btn" title="Text Color">
              <i class="fas fa-palette"></i>
            </button>
            <button class="toolbar-btn" title="Background Color">
              <i class="fas fa-fill-drip"></i>
            </button>
          </div>
        </div>
        
        <div class="editor-workspace">
          <div class="resume-preview">
            <div class="resume-page">
              <div class="resume-section active" id="personal-section">
                <div class="section-header">
                  <h2 contenteditable="true">Personal Information</h2>
                </div>
                <div class="section-content">
                  <div class="personal-info">
                    <div class="personal-header">
                      <div class="personal-avatar">
                        <div class="avatar-placeholder">
                          <i class="fas fa-user"></i>
                          <button class="upload-btn">
                            <i class="fas fa-upload"></i>
                          </button>
                        </div>
                      </div>
                      <div class="personal-name-title">
                        <h1 contenteditable="true" class="resume-name">John Doe</h1>
                        <p contenteditable="true" class="resume-title">Senior Software Engineer</p>
                      </div>
                    </div>
                    <div class="personal-details">
                      <div class="detail-item">
                        <i class="fas fa-envelope"></i>
                        <span contenteditable="true"><EMAIL></span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-phone"></i>
                        <span contenteditable="true">(*************</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span contenteditable="true">San Francisco, CA</span>
                      </div>
                      <div class="detail-item">
                        <i class="fab fa-linkedin"></i>
                        <span contenteditable="true">linkedin.com/in/johndoe</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="resume-section" id="summary-section">
                <div class="section-header">
                  <h2 contenteditable="true">Professional Summary</h2>
                </div>
                <div class="section-content">
                  <p contenteditable="true" class="summary-text">
                    Experienced software engineer with over 8 years of expertise in developing scalable web applications. Proficient in JavaScript, React, Node.js, and cloud technologies. Strong problem-solving skills with a focus on delivering high-quality, maintainable code.
                  </p>
                </div>
              </div>
              
              <div class="resume-section" id="experience-section">
                <div class="section-header">
                  <h2 contenteditable="true">Work Experience</h2>
                </div>
                <div class="section-content">
                  <div class="experience-item">
                    <div class="experience-header">
                      <div>
                        <h3 contenteditable="true">Senior Software Engineer</h3>
                        <h4 contenteditable="true">Tech Innovations Inc.</h4>
                      </div>
                      <div class="experience-date">
                        <span contenteditable="true">Jan 2020 - Present</span>
                      </div>
                    </div>
                    <div class="experience-description">
                      <ul>
                        <li contenteditable="true">Led a team of 5 developers in building a scalable e-commerce platform using React and Node.js</li>
                        <li contenteditable="true">Implemented CI/CD pipelines that reduced deployment time by 40%</li>
                        <li contenteditable="true">Optimized database queries resulting in 30% faster page load times</li>
                      </ul>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <div class="experience-item">
                    <div class="experience-header">
                      <div>
                        <h3 contenteditable="true">Software Engineer</h3>
                        <h4 contenteditable="true">Digital Solutions LLC</h4>
                      </div>
                      <div class="experience-date">
                        <span contenteditable="true">Jun 2016 - Dec 2019</span>
                      </div>
                    </div>
                    <div class="experience-description">
                      <ul>
                        <li contenteditable="true">Developed and maintained multiple web applications using JavaScript frameworks</li>
                        <li contenteditable="true">Collaborated with UX designers to implement responsive designs</li>
                        <li contenteditable="true">Mentored junior developers and conducted code reviews</li>
                      </ul>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <button class="add-item-btn">
                    <i class="fas fa-plus"></i>
                    <span>Add Experience</span>
                  </button>
                </div>
              </div>
              
              <div class="resume-section" id="education-section">
                <div class="section-header">
                  <h2 contenteditable="true">Education</h2>
                </div>
                <div class="section-content">
                  <div class="education-item">
                    <div class="education-header">
                      <div>
                        <h3 contenteditable="true">Master of Computer Science</h3>
                        <h4 contenteditable="true">Stanford University</h4>
                      </div>
                      <div class="education-date">
                        <span contenteditable="true">2014 - 2016</span>
                      </div>
                    </div>
                    <div class="education-description">
                      <p contenteditable="true">GPA: 3.8/4.0</p>
                      <p contenteditable="true">Specialized in Artificial Intelligence and Machine Learning</p>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <div class="education-item">
                    <div class="education-header">
                      <div>
                        <h3 contenteditable="true">Bachelor of Science in Computer Engineering</h3>
                        <h4 contenteditable="true">University of California, Berkeley</h4>
                      </div>
                      <div class="education-date">
                        <span contenteditable="true">2010 - 2014</span>
                      </div>
                    </div>
                    <div class="education-description">
                      <p contenteditable="true">GPA: 3.7/4.0</p>
                      <p contenteditable="true">Minor in Business Administration</p>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <button class="add-item-btn">
                    <i class="fas fa-plus"></i>
                    <span>Add Education</span>
                  </button>
                </div>
              </div>
              
              <div class="resume-section" id="skills-section">
                <div class="section-header">
                  <h2 contenteditable="true">Skills</h2>
                </div>
                <div class="section-content">
                  <div class="skills-container">
                    <div class="skill-item" contenteditable="true">JavaScript</div>
                    <div class="skill-item" contenteditable="true">React</div>
                    <div class="skill-item" contenteditable="true">Node.js</div>
                    <div class="skill-item" contenteditable="true">TypeScript</div>
                    <div class="skill-item" contenteditable="true">Python</div>
                    <div class="skill-item" contenteditable="true">AWS</div>
                    <div class="skill-item" contenteditable="true">Docker</div>
                    <div class="skill-item" contenteditable="true">MongoDB</div>
                    <div class="skill-item" contenteditable="true">SQL</div>
                    <div class="skill-item" contenteditable="true">Git</div>
                    <div class="skill-item" contenteditable="true">CI/CD</div>
                    <div class="skill-item" contenteditable="true">Agile/Scrum</div>
                    <button class="add-skill-btn">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="resume-section" id="certifications-section">
                <div class="section-header">
                  <h2 contenteditable="true">Certifications</h2>
                </div>
                <div class="section-content">
                  <div class="certification-item">
                    <div class="certification-header">
                      <h3 contenteditable="true">AWS Certified Solutions Architect</h3>
                      <span contenteditable="true">2021</span>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <div class="certification-item">
                    <div class="certification-header">
                      <h3 contenteditable="true">Google Cloud Professional Developer</h3>
                      <span contenteditable="true">2020</span>
                    </div>
                    <div class="item-actions">
                      <button class="btn-icon" title="Add Item">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn-icon" title="Remove Item">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </div>
                  
                  <button class="add-item-btn">
                    <i class="fas fa-plus"></i>
                    <span>Add Certification</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <aside class="editor-properties" id="propertiesPanel">
        <div class="properties-header">
          <h3>Section Properties</h3>
          <button class="btn-icon" id="closePropertiesBtn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="properties-content">
          <div class="property-group">
            <h4>Section Settings</h4>
            <div class="property-item">
              <label for="sectionTitle">Section Title</label>
              <input type="text" id="sectionTitle" value="Personal Information">
            </div>
            <div class="property-item">
              <label for="sectionVisibility">Visibility</label>
              <div class="toggle-switch">
                <input type="checkbox" id="sectionVisibility" checked>
                <label for="sectionVisibility"></label>
              </div>
            </div>
          </div>
          
          <div class="property-group">
            <h4>Appearance</h4>
            <div class="property-item">
              <label for="sectionSpacing">Spacing</label>
              <div class="range-slider">
                <input type="range" id="sectionSpacing" min="0" max="10" value="2">
                <span class="range-value">2</span>
              </div>
            </div>
            <div class="property-item">
              <label for="titleStyle">Title Style</label>
              <select id="titleStyle">
                <option value="default">Default</option>
                <option value="bold">Bold</option>
                <option value="underlined">Underlined</option>
                <option value="centered">Centered</option>
              </select>
            </div>
            <div class="property-item">
              <label for="sectionBorder">Border</label>
              <select id="sectionBorder">
                <option value="none">None</option>
                <option value="bottom">Bottom Only</option>
                <option value="full">Full Border</option>
              </select>
            </div>
          </div>
          
          <div class="property-group">
            <h4>Advanced</h4>
            <div class="property-item">
              <label for="sectionOrder">Order</label>
              <div class="number-input">
                <button class="number-down">-</button>
                <input type="number" id="sectionOrder" value="1" min="1">
                <button class="number-up">+</button>
              </div>
            </div>
            <div class="property-item">
              <label for="columnLayout">Column Layout</label>
              <select id="columnLayout">
                <option value="single">Single Column</option>
                <option value="double">Two Columns</option>
              </select>
            </div>
          </div>
          
          <div class="property-actions">
            <button class="btn btn-outline">
              <i class="fas fa-trash-alt"></i>
              <span>Delete Section</span>
            </button>
            <button class="btn btn-primary">
              <i class="fas fa-save"></i>
              <span>Apply Changes</span>
            </button>
          </div>
        </div>
      </aside>
    </div>
  </div>

  <div class="modal" id="previewModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Resume Preview</h2>
        <button class="close-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="preview-container">
          <iframe id="previewFrame" src="preview.html" frameborder="0"></iframe>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline" id="closePreviewBtn">Close</button>
        <button class="btn btn-primary">
          <i class="fas fa-download"></i>
          <span>Download PDF</span>
        </button>
      </div>
    </div>
  </div>

  <script src="js/main.js"></script>
  <script src="js/editor.js"></script>
</body>
</html>
