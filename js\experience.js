document.addEventListener('DOMContentLoaded', function() {
    // Add Experience Modal
    const addModal = document.getElementById('add-experience-modal');
    const addBtn = document.getElementById('add-experience-btn');
    const emptyAddBtn = document.getElementById('empty-add-experience-btn');
    const addCloseBtn = addModal.querySelector('.close');
    
    function openAddModal() {
        addModal.style.display = 'block';
    }
    
    function closeAddModal() {
        addModal.style.display = 'none';
    }
    
    if (addBtn) {
        addBtn.addEventListener('click', openAddModal);
    }
    
    if (emptyAddBtn) {
        emptyAddBtn.addEventListener('click', openAddModal);
    }
    
    addCloseBtn.addEventListener('click', closeAddModal);
    
    window.addEventListener('click', function(event) {
        if (event.target === addModal) {
            closeAddModal();
        }
    });
    
    // Edit Experience Modal
    const editModal = document.getElementById('edit-experience-modal');
    const editBtns = document.querySelectorAll('.edit-experience-btn');
    const editCloseBtn = editModal.querySelector('.close');
    
    function openEditModal(experienceId) {
        // Get experience data
        const experienceItem = document.querySelector(`.experience-item[data-id="${experienceId}"]`);
        
        if (experienceItem) {
            const jobTitle = experienceItem.querySelector('.experience-header h2').textContent;
            const company = experienceItem.querySelector('.experience-company').textContent;
            const location = experienceItem.querySelector('.experience-location') ? 
                experienceItem.querySelector('.experience-location').textContent.replace(/^\s*\S+\s*/, '') : '';
            
            const periodText = experienceItem.querySelector('.experience-period').textContent;
            const startDate = periodText.split(' - ')[0].trim();
            const endDate = periodText.split(' - ')[1].trim();
            const currentJob = endDate === 'Present';
            
            const description = experienceItem.querySelector('.experience-description p') ? 
                experienceItem.querySelector('.experience-description p').textContent : '';
            
            const responsibilities = experienceItem.querySelector('.experience-responsibilities p') ? 
                experienceItem.querySelector('.experience-responsibilities p').textContent : '';
            
            const achievements = experienceItem.querySelector('.experience-achievements p') ? 
                experienceItem.querySelector('.experience-achievements p').textContent : '';
            
            // Set form values
            document.getElementById('edit_experience_id').value = experienceId;
            document.getElementById('edit_job_title').value = jobTitle;
            document.getElementById('edit_company_name').value = company;
            document.getElementById('edit_location').value = location;
            
            // Format dates for input
            const startDateParts = startDate.split(' ');
            const startMonth = getMonthNumber(startDateParts[0]);
            const startYear = startDateParts[1];
            document.getElementById('edit_start_date').value = `${startYear}-${startMonth.toString().padStart(2, '0')}-01`;
            
            if (!currentJob) {
                const endDateParts = endDate.split(' ');
                const endMonth = getMonthNumber(endDateParts[0]);
                const endYear = endDateParts[1];
                document.getElementById('edit_end_date').value = `${endYear}-${endMonth.toString().padStart(2, '0')}-01`;
            } else {
                document.getElementById('edit_end_date').value = '';
            }
            
            document.getElementById('edit_current_job').checked = currentJob;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_responsibilities').value = responsibilities;
            document.getElementById('edit_achievements').value = achievements;
            
            // Show modal
            editModal.style.display = 'block';
        }
    }
    
    function closeEditModal() {
        editModal.style.display = 'none';
    }
    
    editBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const experienceId = this.getAttribute('data-id');
            openEditModal(experienceId);
        });
    });
    
    editCloseBtn.addEventListener('click', closeEditModal);
    
    window.addEventListener('click', function(event) {
        if (event.target === editModal) {
            closeEditModal();
        }
    });
    
    // Current job checkbox
    const currentJobCheckbox = document.getElementById('current_job');
    const endDateGroup = document.querySelector('.end-date-group');
    
    if (currentJobCheckbox && endDateGroup) {
        currentJobCheckbox.addEventListener('change', function() {
            if (this.checked) {
                endDateGroup.style.display = 'none';
                document.getElementById('end_date').value = '';
            } else {
                endDateGroup.style.display = 'block';
            }
        });
    }
    
    // Edit current job checkbox
    const editCurrentJobCheckbox = document.getElementById('edit_current_job');
    const editEndDateGroup = document.querySelector('.edit-end-date-group');
    
    if (editCurrentJobCheckbox && editEndDateGroup) {
        editCurrentJobCheckbox.addEventListener('change', function() {
            if (this.checked) {
                editEndDateGroup.style.display = 'none';
                document.getElementById('edit_end_date').value = '';
            } else {
                editEndDateGroup.style.display = 'block';
            }
        });
    }
    
    // Helper function to convert month name to number
    function getMonthNumber(monthName) {
        const months = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        };
        
        return months[monthName] || 1;
    }
});