/* Experience page styling */
.experience-container {
    padding: 20px 0;
}

.experience-timeline {
    position: relative;
    padding-left: 40px;
}

.experience-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e0e0e0;
}

.experience-item {
    position: relative;
    margin-bottom: 40px;
}

.experience-marker {
    position: absolute;
    left: -40px;
    top: 0;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #4a6cf7;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #4a6cf7;
}

.experience-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 25px;
    transition: all 0.3s ease;
}

.experience-content:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.experience-header {
    margin-bottom: 20px;
}

.experience-header h2 {
    font-size: 1.3rem;
    margin: 0 0 8px 0;
    color: #333;
}

.experience-company {
    font-size: 1.1rem;
    color: #555;
    font-weight: 600;
    margin-bottom: 5px;
}

.experience-period {
    font-size: 0.9rem;
    color: #777;
    margin-bottom: 5px;
}

.experience-location {
    font-size: 0.9rem;
    color: #777;
}

.experience-location i {
    margin-right: 5px;
    color: #4a6cf7;
}

.experience-description,
.experience-responsibilities,
.experience-achievements {
    margin-bottom: 20px;
}

.experience-description h3,
.experience-responsibilities h3,
.experience-achievements h3 {
    font-size: 1rem;
    margin: 0 0 10px 0;
    color: #555;
}

.experience-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.empty-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.empty-state p {
    color: #666;
    margin-bottom: 20px;
}

/* Modal styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 700px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s;
    max-height: 80vh;
    overflow-y: auto;
}

@keyframes slideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.close {
    font-size: 24px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input[type="text"]:focus,
.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4a6cf7;
    outline: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check input[type="checkbox"] {
    margin-right: 10px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Make the modal responsive */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
    }
    
    .form-row {
        flex-direction: column;
        gap: 20px;
    }
    
    .experience-timeline {
        padding-left: 30px;
    }
    
    .experience-timeline::before {
        left: 10px;
    }
    
    .experience-marker {
        left: -30px;
        width: 12px;
        height: 12px;
    }
}