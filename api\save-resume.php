<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid data format'
    ]);
    exit;
}

// Check if required fields are present
if (!isset($data['name']) || !isset($data['template_id']) || !isset($data['content'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Missing required fields'
    ]);
    exit;
}

// Check if it's an update or new resume
if (isset($data['resume_id']) && !empty($data['resume_id'])) {
    // Update existing resume
    $resume_id = $data['resume_id'];
    
    // Verify the resume belongs to the user
    $db->query("SELECT id FROM resumes WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $resume_id);
    $db->bind(':user_id', $user_id);
    $existing_resume = $db->single();
    
    if (!$existing_resume) {
        echo json_encode([
            'success' => false,
            'message' => 'Resume not found or access denied'
        ]);
        exit;
    }
    
    // Update the resume
    $db->query("UPDATE resumes SET 
                name = :resume_name, 
                content = :content, 
                updated_at = NOW() 
                WHERE id = :id");
    $db->bind(':resume_name', $data['resume_name']);
    $db->bind(':content', json_encode($data['content']));
    $db->bind(':id', $resume_id);
    $db->execute();
    
    echo json_encode([
        'success' => true,
        'message' => 'Resume updated successfully',
        'resume_id' => $resume_id
    ]);
} else {
    // Create new resume
    $db->query("INSERT INTO resumes 
                (user_id, template_id, name, content, created_at, updated_at) 
                VALUES 
                (:user_id, :template_id, :resume_name, :content, NOW(), NOW())");
    $db->bind(':user_id', $user_id);
    $db->bind(':template_id', $data['template_id']);
    $db->bind(':resume_name', $data['name']);
    $db->bind(':content', json_encode($data['content']));
    $db->execute();
    
    $resume_id = $db->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => 'Resume created successfully',
        'resume_id' => $resume_id
    ]);
}
?>
