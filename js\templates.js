document.addEventListener("DOMContentLoaded", () => {
  // Filter templates by category
  const filterButtons = document.querySelectorAll(".filter-btn")
  const templateCards = document.querySelectorAll(".template-card")
  const photoFilter = document.querySelector('input[data-feature="photo"]')
  const filtersToggle = document.querySelector(".filters-toggle")
  const filtersContent = document.querySelector(".filters-content")

  // Toggle filters on mobile
  if (filtersToggle && filtersContent) {
    filtersToggle.addEventListener("click", function () {
      filtersContent.classList.toggle("active")

      // Change icon based on state
      if (filtersContent.classList.contains("active")) {
        this.innerHTML = '<i class="fas fa-chevron-up"></i>'
      } else {
        this.innerHTML = '<i class="fas fa-chevron-down"></i>'
      }
    })
  }

  // Set active filter button
  filterButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Remove active class from all buttons
      filterButtons.forEach((btn) => btn.classList.remove("active"))

      // Add active class to clicked button
      this.classList.add("active")

      // Get category to filter by
      const category = this.getAttribute("data-category")

      // Filter templates
      filterTemplates(category, photoFilter.checked)
    })
  })

  // Filter templates by photo feature
  if (photoFilter) {
    photoFilter.addEventListener("change", function () {
      // Get active category
      const activeCategory = document.querySelector(".filter-btn.active").getAttribute("data-category")

      // Filter templates
      filterTemplates(activeCategory, this.checked)
    })
  }

  // Filter templates function
  function filterTemplates(category, hasPhoto) {
    templateCards.forEach((card) => {
      const cardCategory = card.getAttribute("data-category")
      const cardHasPhoto = card.getAttribute("data-photo") === "1"

      // Check if card matches category filter
      const matchesCategory = category === "all" || cardCategory === category

      // Check if card matches photo filter
      const matchesPhoto = !hasPhoto || cardHasPhoto

      // Show or hide card based on filters
      if (matchesCategory && matchesPhoto) {
        card.style.display = "block"
      } else {
        card.style.display = "none"
      }
    })
  }

  // Animation for template cards
  templateCards.forEach((card, index) => {
    // Add staggered animation delay
    card.style.animationDelay = `${index * 0.05}s`
    card.classList.add("fade-in")
  })
})

