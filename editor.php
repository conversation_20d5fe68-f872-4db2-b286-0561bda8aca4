<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

$user_id = $_SESSION['user_id'];
$profile = getUserProfile($user_id);

// Check if profile is complete enough to create a resume
$profile_complete = isProfileComplete($user_id);

// Get template ID from URL
$template_id = isset($_GET['template_id']) ? (int)$_GET['template_id'] : 0;
$resume_id = isset($_GET['resume_id']) ? (int)$_GET['resume_id'] : 0;

// Check if template exists
$db->query("SELECT * FROM resume_templates WHERE id = :id");
$db->bind(':id', $template_id);
$template = $db->single();

if (!$template) {
    setMessage('Template not found.', 'error');
    redirect('templates.php');
}

// Get user data for resume
$user_skills = getUserSkills($user_id);
$user_experience = getUserWorkExperience($user_id);
$user_education = getUserEducation($user_id);
$user_projects = getUserProjects($user_id);
$user_certifications = getUserCertifications($user_id);

// Check if resume already exists
$resume_data = [];

if ($resume_id > 0) {
    // Get resume data
    $db->query("SELECT * FROM user_resumes WHERE id = :id AND user_id = :user_id");
    $db->bind(':id', $resume_id);
    $db->bind(':user_id', $user_id);
    $resume = $db->single();
    
    if ($resume) {
        $resume_data = json_decode($resume['resume_data'], true);
    } else {
        $resume_id = 0;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $resume_name = sanitize($_POST['resume_name']);
    $resume_data = $_POST['resume_data'];
    
    // Validate form data
    if (empty($resume_name)) {
        setMessage('Resume name is required.', 'error');
    } else {
        // Save resume
        if ($resume_id > 0) {
            // Update existing resume
            $db->query("UPDATE user_resumes SET 
                resume_name = :resume_name, 
                resume_data = :resume_data, 
                updated_at = NOW() 
                WHERE id = :id AND user_id = :user_id");
            $db->bind(':resume_name', $resume_name);
            $db->bind(':resume_data', $resume_data);
            $db->bind(':id', $resume_id);
            $db->bind(':user_id', $user_id);
            
            if ($db->execute()) {
                setMessage('Resume updated successfully.', 'success');
                redirect('my-resumes.php');
            } else {
                setMessage('Failed to update resume.', 'error');
            }
        } else {
            // Create new resume
            $db->query("INSERT INTO user_resumes (user_id, template_id, resume_name, resume_data, created_at, updated_at) 
                VALUES (:user_id, :template_id, :resume_name, :resume_data, NOW(), NOW())");
            $db->bind(':user_id', $user_id);
            $db->bind(':template_id', $template_id);
            $db->bind(':resume_name', $resume_name);
            $db->bind(':resume_data', $resume_data);
            
            if ($db->execute()) {
                $resume_id = $db->lastInsertId();
                setMessage('Resume created successfully.', 'success');
                redirect('my-resumes.php');
            } else {
                setMessage('Failed to create resume.', 'error');
            }
        }
    }
}

// Set default resume name if new resume
$resume_name = isset($resume['resume_name']) ? $resume['resume_name'] : $profile['first_name'] . "'s " . $template['template_name'] . " Resume";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $resume_id > 0 ? 'Edit' : 'Create'; ?> Resume - Resume Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/editor.css">
    <link rel="stylesheet" href="../templates/<?php echo $template['template_file']; ?>/style.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <main class="editor-container">
        <div class="editor-sidebar">
            <div class="editor-header">
                <h2><?php echo $resume_id > 0 ? 'Edit' : 'Create'; ?> Resume</h2>
                <p><?php echo $template['template_name']; ?> Template</p>
            </div>
            
            <form id="resume-form" method="POST">
                <div class="form-group">
                    <label for="resume_name">Resume Name</label>
                    <input type="text" id="resume_name" name="resume_name" value="<?php echo $resume_name; ?>" required>
                </div>
                
                <input type="hidden" id="resume_data" name="resume_data" value="">
                
                <div class="editor-sections">
                    <div class="editor-section active" data-section="personal">
                        <div class="section-header">
                            <h3><i class="fas fa-user"></i> Personal Information</h3>
                            <button type="button" class="section-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="section-content">
                            <div class="form-group">
                                <label for="full_name">Full Name</label>
                                <input type="text" id="full_name" value="<?php echo $profile['first_name'] . ' ' . $profile['last_name']; ?>" data-field="full_name">
                            </div>
                            <div class="form-group">
                                <label for="job_title">Job Title</label>
                                <input type="text" id="job_title" value="<?php echo $profile['title']; ?>" data-field="job_title">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" value="<?php echo $_SESSION['email']; ?>" data-field="email">
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone</label>
                                <input type="text" id="phone" value="<?php echo $profile['phone']; ?>" data-field="phone">
                            </div>
                            <div class="form-group">
                                <label for="location">Location</label>
                                <input type="text" id="location" value="<?php echo $profile['address']; ?>" data-field="location">
                            </div>
                            <div class="form-group">
                                <label for="website">Website/LinkedIn</label>
                                <input type="text" id="website" value="<?php echo $profile['website']; ?>" data-field="website">
                            </div>
                        </div>
                    </div>
                    
                    <div class="editor-section" data-section="summary">
                        <div class="section-header">
                            <h3><i class="fas fa-file-alt"></i> Professional Summary</h3>
                            <button type="button" class="section-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="section-content">
                            <div class="form-group">
                                <label for="summary">Summary</label>
                                <textarea id="summary" rows="4" data-field="summary"><?php echo $profile['summary']; ?></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="editor-section" data-section="experience">
                        <div class="section-header">
                            <h3><i class="fas fa-briefcase"></i> Work Experience</h3>
                            <button type="button" class="section-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="section-content">
                            <div id="experience-items">
                                <?php foreach ($user_experience as $index => $experience): ?>
                                <div class="experience-item" data-index="<?php echo $index; ?>">
                                    <div class="item-header">
                                        <h4><?php echo $experience['job_title']; ?> at <?php echo $experience['company']; ?></h4>
                                        <div class="item-actions">
                                            <button type="button" class="btn-icon item-edit"><i class="fas fa-edit"></i></button>
                                            <button type="button" class="btn-icon item-remove"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="item-form" style="display: none;">
                                        <div class="form-group">
                                            <label>Job Title</label>
                                            <input type="text" class="exp-job-title" value="<?php echo $experience['job_title']; ?>">
                                        </div>
                                        <div class="form-group">
                                            <label>Company</label>
                                            <input type="text" class="exp-company" value="<?php echo $experience['company']; ?>">
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Start Date</label>
                                                <input type="text" class="exp-start-date" value="<?php echo $experience['start_date']; ?>">
                                            </div>
                                            <div class="form-group">
                                                <label>End Date</label>
                                                <input type="text" class="exp-end-date" value="<?php echo $experience['end_date']; ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Description</label>
                                            <textarea class="exp-description" rows="4"><?php echo $experience['description']; ?></textarea>
                                        </div>
                                        <div class="form-actions">
                                            <button type="button" class="btn btn-sm btn-outline item-cancel">Cancel</button>
                                            <button type="button" class="btn btn-sm btn-primary item-save">Save</button>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <button type="button" id="add-experience" class="btn btn-sm btn-outline">
                                <i class="fas fa-plus"></i> Add Experience
                            </button>
                        </div>
                    </div>
                    
                    <div class="editor-section" data-section="education">
                        <div class="section-header">
                            <h3><i class="fas fa-graduation-cap"></i> Education</h3>
                            <button type="button" class="section-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="section-content">
                            <div id="education-items">
                                <?php foreach ($user_education as $index => $education): ?>
                                <div class="education-item" data-index="<?php echo $index; ?>">
                                    <div class="item-header">
                                        <h4><?php echo $education['degree']; ?> - <?php echo $education['institution']; ?></h4>
                                        <div class="item-actions">
                                            <button type="button" class="btn-icon item-edit"><i class="fas fa-edit"></i></button>
                                            <button type="button" class="btn-icon item-remove"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="item-form" style="display: none;">
                                        <div class="form-group">
                                            <label>Degree</label>
                                            <input type="text" class="edu-degree" value="<?php echo $education['degree']; ?>">
                                        </div>
                                        <div class="form-group">
                                            <label>Institution</label>
                                            <input type="text" class="edu-institution" value="<?php echo $education['institution']; ?>">
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Start Date</label>
                                                <input type="text" class="edu-start-date" value="<?php echo $education['start_date']; ?>">
                                            </div>
                                            <div class="form-group">
                                                <label>End Date</label>
                                                <input type="text" class="edu-end-date" value="<?php echo $education['end_date']; ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Description</label>
                                            <textarea class="edu-description" rows="3"><?php echo $education['description']; ?></textarea>
                                        </div>
                                        <div class="form-actions">
                                            <button type="button" class="btn btn-sm btn-outline item-cancel">Cancel</button>
                                            <button type="button" class="btn btn-sm btn-primary item-save">Save</button>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <button type="button" id="add-education" class="btn btn-sm btn-outline">
                                <i class="fas fa-plus"></i> Add Education
                            </button>
                        </div>
                    </div>
                    
                    <div class="editor-section" data-section="skills">
                        <div class="section-header">
                            <h3><i class="fas fa-star"></i> Skills</h3>
                            <button type="button" class="section-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="section-content">
                            <div id="skills-container">
                                <?php foreach ($user_skills as $skill): ?>
                                <div class="skill-tag">
                                    <span><?php echo $skill['skill_name']; ?></span>
                                    <button type="button" class="skill-remove"><i class="fas fa-times"></i></button>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="form-group">
                                <label for="add-skill">Add Skill</label>
                                <div class="input-with-button">
                                    <input type="text" id="add-skill-input" placeholder="Enter a skill">
                                    <button type="button" id="add-skill-btn" class="btn btn-sm btn-primary">Add</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="editor-actions">
                    <a href="templates.php" class="btn btn-outline">Cancel</a>
                    <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                    <button type="submit" class="btn btn-primary">Save Resume</button>
                </div>
            </form>
        </div>
        
        <div class="resume-preview">
            <div class="preview-header">
                <h3>Preview</h3>
                <div class="preview-actions">
                    <button type="button" id="download-pdf" class="btn btn-sm btn-primary">
                        <i class="fas fa-download"></i> Download PDF
                    </button>
                </div>
            </div>
            
            <div class="preview-container">
                <div id="resume-template" class="resume-container">
                    <!-- Template will be loaded here -->
                    <?php include '../templates/' . $template['template_file'] . '/template.php'; ?>
                </div>
            </div>
        </div>
    </main>
    
    <?php include '../includes/footer.php'; ?>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="../assets/js/editor.js"></script>
    <script>
        // Initialize editor with resume data if editing
        <?php if (!empty($resume_data)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            initResumeEditor(<?php echo json_encode($resume_data); ?>);
        });
        <?php else: ?>
        document.addEventListener('DOMContentLoaded', function() {
            initResumeEditor();
        });
        <?php endif; ?>
    </script>
</body>
</html>