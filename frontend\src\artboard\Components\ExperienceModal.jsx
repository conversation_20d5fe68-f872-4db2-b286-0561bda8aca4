import React, { useEffect, useState } from "react";
import { FaPlus, FaTimes } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const ExperienceModal = ({ initial, onClose, onSave }) => {
  const [company, setCompany] = useState("");
  const [position, setPosition] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [location, setLocation] = useState("");
  const [website, setWebsite] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    if (initial) {
      setCompany(initial.Company || "");
      setPosition(initial.Position || "");
      setStartDate(initial.StartDate ? new Date(initial.StartDate) : null);
      setEndDate(initial.EndDate ? new Date(initial.EndDate) : null);
      setLocation(initial.Location || "");
      setWebsite(initial.Website || "");
      setDescription(initial.Description || "");
    } else {
      setCompany("");
      setPosition("");
      setStartDate(null);
      setEndDate(null);
      setLocation("");
      setWebsite("");
      setDescription("");
    }
  }, [initial]);

  const submit = () => {
    if (!company || !position || !startDate || !location) {
      alert("Please fill all required fields.");
      return;
    }

    const payload = {
      Company: company,
      Position: position,
      StartDate: startDate.toISOString().split("T")[0],
      EndDate: endDate ? endDate.toISOString().split("T")[0] : "",
      Location: location,
      Website: website,
      Description: description,
    };

    onSave(payload, initial?._id);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center px-4">
      <div className="relative w-full max-w-xl bg-white rounded-2xl p-6 shadow-xl">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-700 hover:text-black"
        >
          <FaTimes size={20} />
        </button>

        <h2 className="text-xl font-bold mb-6 flex items-center gap-2">
          <FaPlus /> {initial ? "Update Experience" : "Add New Experience"}
        </h2>

        <form onSubmit={(e) => e.preventDefault()} className="space-y-4">
          <input
            value={company}
            onChange={(e) => setCompany(e.target.value)}
            placeholder="Company"
            className="w-full border rounded px-3 py-2"
          />
          <input
            value={position}
            onChange={(e) => setPosition(e.target.value)}
            placeholder="Position"
            className="w-full border rounded px-3 py-2"
          />

          <div className="flex gap-4">
            <DatePicker
              selected={startDate}
              onChange={setStartDate}
              placeholderText="Start date"
              dateFormat="yyyy-MM-dd"
              className="w-full border rounded px-3 py-2"
            />
            <DatePicker
              selected={endDate}
              onChange={setEndDate}
              placeholderText="End date"
              dateFormat="yyyy-MM-dd"
              isClearable
              className="w-full border rounded px-3 py-2"
            />
          </div>

          <input
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="Location"
            className="w-full border rounded px-3 py-2"
          />
          <input
            value={website}
            onChange={(e) => setWebsite(e.target.value)}
            placeholder="Website"
            className="w-full border rounded px-3 py-2"
          />
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Description"
            className="w-full border rounded px-3 py-2 min-h-[80px]"
          />

          <div className="flex justify-end">
            <button
              onClick={submit}
              className="bg-[#73716c] hover:bg-[#000000] text-[#f9f9f9] font-semibold px-6 py-2 rounded"
            >
              {initial ? "Update" : "Create"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExperienceModal;
