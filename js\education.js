document.addEventListener('DOMContentLoaded', function() {
    // Get modal elements
    const addModal = document.getElementById('add-education-modal');
    const editModal = document.getElementById('edit-education-modal');
    const addBtn = document.getElementById('add-education-btn');
    const emptyAddBtn = document.getElementById('empty-add-education-btn');
    const closeButtons = document.querySelectorAll('.close');
    
    // Add click event for add education button
    if (addBtn) {
        addBtn.addEventListener('click', function() {
            addModal.style.display = 'block';
        });
    }
    
    // Add click event for empty state add button
    if (emptyAddBtn) {
        emptyAddBtn.addEventListener('click', function() {
            addModal.style.display = 'block';
        });
    }
    
    // Add click events for all close buttons
    closeButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            addModal.style.display = 'none';
            editModal.style.display = 'none';
        });
    });
    
    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target === addModal) {
            addModal.style.display = 'none';
        }
        if (event.target === editModal) {
            editModal.style.display = 'none';
        }
    });
    
    // Handle current education checkbox
    const currentEducationCheckbox = document.getElementById('current_education');
    const endDateInput = document.getElementById('end_date');
    const endDateGroup = document.querySelector('.end-date-group');
    
    if (currentEducationCheckbox && endDateInput) {
        currentEducationCheckbox.addEventListener('change', function() {
            if (this.checked) {
                endDateInput.removeAttribute('required');
                endDateInput.value = '';
                endDateGroup.style.opacity = '0.5';
                endDateInput.disabled = true;
            } else {
                endDateInput.setAttribute('required', 'required');
                endDateGroup.style.opacity = '1';
                endDateInput.disabled = false;
            }
        });
    }
    
    // Handle edit current education checkbox
    const editCurrentEducationCheckbox = document.getElementById('edit_current_education');
    const editEndDateInput = document.getElementById('edit_end_date');
    const editEndDateGroup = document.querySelector('.edit-end-date-group');
    
    if (editCurrentEducationCheckbox && editEndDateInput) {
        editCurrentEducationCheckbox.addEventListener('change', function() {
            if (this.checked) {
                editEndDateInput.removeAttribute('required');
                editEndDateInput.value = '';
                editEndDateGroup.style.opacity = '0.5';
                editEndDateInput.disabled = true;
            } else {
                editEndDateInput.setAttribute('required', 'required');
                editEndDateGroup.style.opacity = '1';
                editEndDateInput.disabled = false;
            }
        });
    }
    
    // Handle edit buttons
    const editButtons = document.querySelectorAll('.edit-education-btn');
    
    editButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const educationId = this.getAttribute('data-id');
            const educationItem = document.querySelector(`.education-item[data-id="${educationId}"]`);
            
            // Get education details
            const institution = educationItem.querySelector('.education-institution').textContent;
            const degree = educationItem.querySelector('h2').textContent;
            
            // Get field of study if it exists
            const fieldElement = educationItem.querySelector('.education-field');
            const fieldOfStudy = fieldElement ? fieldElement.textContent : '';
            
            // Get location if it exists
            const locationElement = educationItem.querySelector('.education-location');
            const location = locationElement ? locationElement.textContent.replace(/^.* /, '') : '';
            
            // Get dates
            const periodText = educationItem.querySelector('.education-period').textContent;
            const dates = periodText.split(' - ');
            const startDateText = dates[0].trim();
            const endDateText = dates[1].trim();
            
            // Convert dates to YYYY-MM-DD format for input
            // This is a simple conversion assuming dates are in MM/YYYY format
            const startDateParts = startDateText.split('/');
            const startDate = startDateParts.length === 2 ? 
                `${startDateParts[1]}-${startDateParts[0]}-01` : startDateText;
            
            const isCurrentEducation = endDateText === 'Present';
            let endDate = '';
            
            if (!isCurrentEducation) {
                const endDateParts = endDateText.split('/');
                endDate = endDateParts.length === 2 ? 
                    `${endDateParts[1]}-${endDateParts[0]}-01` : endDateText;
            }
            
            // Get GPA if it exists
            const gpaElement = educationItem.querySelector('.education-gpa');
            const gpa = gpaElement ? gpaElement.textContent.replace('GPA: ', '') : '';
            
            // Get description if it exists
            const descriptionElement = educationItem.querySelector('.education-description p');
            const description = descriptionElement ? descriptionElement.innerHTML.replace(/<br>/g, '\n') : '';
            
            // Get achievements if they exist
            const achievementsElement = educationItem.querySelector('.education-achievements p');
            const achievements = achievementsElement ? achievementsElement.innerHTML.replace(/<br>/g, '\n') : '';
            
            // Fill form fields
            document.getElementById('edit_education_id').value = educationId;
            document.getElementById('edit_institution').value = institution;
            document.getElementById('edit_degree').value = degree;
            document.getElementById('edit_field_of_study').value = fieldOfStudy;
            document.getElementById('edit_location').value = location;
            document.getElementById('edit_start_date').value = startDate;
            document.getElementById('edit_end_date').value = endDate;
            document.getElementById('edit_current_education').checked = isCurrentEducation;
            document.getElementById('edit_gpa').value = gpa;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_achievements').value = achievements;
            
            // Handle end date display based on current education status
            if (isCurrentEducation) {
                editEndDateInput.removeAttribute('required');
                editEndDateInput.disabled = true;
                editEndDateGroup.style.opacity = '0.5';
            } else {
                editEndDateInput.setAttribute('required', 'required');
                editEndDateInput.disabled = false;
                editEndDateGroup.style.opacity = '1';
            }
            
            // Show modal
            editModal.style.display = 'block';
        });
    });
    
    // Form validation
    const addForm = document.getElementById('add-education-form');
    const editForm = document.getElementById('edit-education-form');
    
    if (addForm) {
        addForm.addEventListener('submit', function(event) {
            const isCurrentEducation = currentEducationCheckbox.checked;
            if (!isCurrentEducation && !endDateInput.value) {
                event.preventDefault();
                alert('End date is required if not current education');
            }
        });
    }
    
    if (editForm) {
        editForm.addEventListener('submit', function(event) {
            const isCurrentEducation = editCurrentEducationCheckbox.checked;
            if (!isCurrentEducation && !editEndDateInput.value) {
                event.preventDefault();
                alert('End date is required if not current education');
            }
        });
    }
});